import React, { Component, useState, useEffect } from 'react';
import {
  View,
  Text,
  Platform,
  Dimensions,
  DevSettings,
  Alert,
  PermissionsAndroid,
  NativeEventEmitter,
  DeviceEventEmitter,
} from 'react-native';
import firestore from '@react-native-firebase/firestore';
import database from '@react-native-firebase/database';
import storage from '@react-native-firebase/storage';
import auth from '@react-native-firebase/auth';
import { Collections } from '../constant/firebase';
import { UserStore } from '../store/userStore';
import * as User from "./User";
import AsyncStorage from '@react-native-async-storage/async-storage';
import { MerchantStore } from '../store/merchantStore';
import { OutletStore } from '../store/outletStore';
import { TempStore } from '../store/tempStore';
import {
  OUTLET_SHIFT_STATUS,
  ROLE_TYPE,
  USER_ORDER_STATUS,
  USER_QUEUE_STATUS,
  USER_RESERVATION_STATUS,
  USER_RING_STATUS,
  REPORT_SORT_FIELD_TYPE_COMPARE,
  REPORT_SORT_FIELD_TYPE_VALUE,
  REPORT_SORT_COMPARE_OPERATOR,
  REPORT_SORT_FIELD_TYPE,
  ORDER_TYPE,
  ACCUMULATOR_ID,
  EMAIL_REPORT_TYPE,
  PAYMENT_SORT_FIELD_TYPE,
  PAYMENT_SORT_FIELD_TYPE_VALUE,
  PAYMENT_SORT_COMPARE_OPERATOR,
  PAYMENT_SORT_FIELD_TYPE_COMPARE,
  TIMEZONE,
  KD_PRINT_EVENT_TYPE,
  KD_PRINT_VARIATION,
  KD_FONT_SIZE,
  PRODUCT_PRICE_TYPE,
  UNIT_TYPE,
  DEFAULT_CRM_TAG,
  ORDER_TYPE_DETAILS,
  UNIT_TYPE_SHORT,
  APP_TYPE,
  PAYMENT_CHANNEL_NAME_PARSED,
  PRINTER_USER_PRIORITY,
  ORDER_TYPE_SUB,
  CHARGES_TYPE,
  REPORT_DISPLAY_TYPE,
  DATE_COMPARE_TYPE,
  KD_ITEM_STATUS,
  APP_ENV,
  ORDER_PROCESS_TYPE,
  FIRST_START_KEY,
  OUTLET_DISPLAY_PAIRING_DEVICE,
  WOILIST_SORT_COMPARE_OPERATOR,
  WOILIST_SORT_FIELD_TYPE,
  WOILIST_SORT_FIELD_TYPE_VALUE,
  WOILIST_SORT_FIELD_TYPE_COMPARE,
  WOLIST_SORT_COMPARE_OPERATOR,
  WOLIST_SORT_FIELD_TYPE,
  WOLIST_SORT_FIELD_TYPE_VALUE,
  WOLIST_SORT_FIELD_TYPE_COMPARE,
  COST_USAGE_WASTAGE_LEVEL,
} from '../constant/common';
import { APPLY_DISCOUNT_PER, APPLY_DISCOUNT_TYPE, PROMOTION_TYPE, PROMOTION_TYPE_VARIATION } from '../constant/promotions';
import { CommonStore } from '../store/commonStore';
import API from '../constant/API';
import moment from 'moment';
import messaging from '@react-native-firebase/messaging';
import RNFetchBlob from 'rn-fetch-blob';
import 'react-native-get-random-values';
import { v4 as uuidv4 } from 'uuid';
const RNFS = require('@dr.pogodin/react-native-fs');
import XLSX from 'xlsx';
import ApiClient from './ApiClient';
import ApiClientReporting from './ApiClientReporting';
const { nanoid } = require('nanoid');
const pixelWidth = require('string-pixel-width');
import { decode } from 'base64-arraybuffer';
import AWS from 'aws-sdk';
import { awsBucket, awsId, awsSecret } from '../constant/env';
import { calcPrintTotalForKdIndividual, connectToPrinter, printDocketForKD, printKDSummaryCategoryWrapper, printUserOrder } from './printer';
import { ESCPOS_CMD, PRINTER_DEVICE_TYPE, PRINTER_TASK_TYPE_PARSED, PRINTER_USAGE_TYPE, PRINTER_COMMAND_TYPE, PRINTER_MODEL_TYPE, } from '../constant/printer';
// import * as ScopedStorage from "react-native-scoped-storage"
import analytics from '@react-native-firebase/analytics';
import { isTablet as isTabletOriginal } from 'react-native-device-detection';
import DeviceInfo from 'react-native-device-info';

import { customAlphabet } from 'nanoid';
// import { storageMMKV } from './storageMMKV';
import { KD_OPTIONS_DELIVER_REJECT, PRINTER_PAPER_WIDTH } from '../constant/printer';
import BigNumber from 'bignumber.js';
import { FileLogger } from "react-native-file-logger";
import { Q } from '@nozbe/watermelondb';
import { LOYALTY_CAMPAIGN_CREDIT_TRANSACTION_TYPE } from '../constant/loyalty';

const alphabetEmailAddress = 'abcdefghijklmnopqrstuvwxyz0123456789';
const randomEmailAddress = customAlphabet(alphabetEmailAddress, 10);

const randomUserIdHuman = customAlphabet(alphabetEmailAddress, 7);

export const requestStoragePermission = async () => {
  try {
    // Scoped Storage access
    // const scope_storage_access = CommonStore.getRawState().scope_storage_access;

    // if (Platform.Version >= 30 && !scope_storage_access) {
    //   Alert.alert('Privacy issues: ', 'Please select a location to store/access files in this app',
    //     [
    //       {
    //         text: "Ok",
    //         onPress: async () => {
    //           let dir = await ScopedStorage.openDocumentTree(true);

    //           if (dir) {
    //             CommonStore.update((s) => {
    //               s.scope_storage_access = true;
    //               s.scope_storage_dir = dir;
    //             });
    //           } else {
    //             CommonStore.update((s) => {
    //               s.scope_storage_access = false;
    //               s.scope_storage_dir = '';
    //             });
    //           }
    //         }
    //       },
    //       {
    //         text: "Cancel",
    //         onPress: () => { }
    //       },
    //     ])
    // }

    const granted = await PermissionsAndroid.request(
      PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE,
      {
        title: 'KooDoo Merchant Storage Permission',
        message: 'KooDoo Merchant App needs access to your storage ',
        buttonNegative: 'Cancel',
        buttonPositive: 'OK',
      },
    );
    if (granted === PermissionsAndroid.RESULTS.GRANTED) {
      // console.log('Storage permission granted');
    } else {
      // console.log('Storage permission denied');
    }
  } catch (err) {
    console.warn(err);
  }
};

export const convertArrayToCSV = (objArray) => {
  var array = typeof objArray != 'object' ? JSON.parse(objArray) : objArray;
  var str = '';

  for (var i = 0; i < array.length; i++) {
    var line = '';
    for (var index in array[i]) {
      // skip array or object but continue the loop for other attributes
      if (typeof array[i][index] == 'object') continue;

      if (index === 'summaryId') {
        continue;
      }

      if (line != '') line += ',';

      line += array[i][index];
    }

    str += `${line}\r\n`;
  }

  return str;
};

export const uploadFileToFirebaseStorage = async (
  selectedFile,
  referencePath,
) => {
  if (selectedFile) {
    // referencePath = `/clients/profile_image/${uniqueId}/profile_image${selectedImage.type}`;
    // console.log(referencePath);
    const reference = storage().ref(referencePath);
    await reference.putFile(selectedFile);
    return referencePath;
  } else {
    return referencePath;
  }
};

const _calculateAndAddSummaryRow = (sheetData, summaryLabel = 'Total') => {
  if (!sheetData || sheetData.length === 0) {
    return sheetData;
  }
  // IMPORTANT: The JSON.parse(JSON.stringify(sheetData)) deep copy method
  // might convert actual numbers (like from your Excel data prep) into numbers,
  // but if your CSV data is parsed into strings (even if they look like numbers),
  // this deep copy won't change their type.
  // The logic inside _calculateAndAddSummaryRow for parseFloat will handle it.
  const dataCopy = JSON.parse(JSON.stringify(sheetData));
  const summaryRow = {};
  // No need for 'if (dataCopy.length === 0) return dataCopy;' again, checked at the start.
  const headers = Object.keys(dataCopy[0]);

  headers.forEach((header, index) => {
    if (index === 0) {
      summaryRow[header] = summaryLabel;
    } else {
      const isNumericColumn = dataCopy.some(r => {
        const val = r[header];
        return typeof val === 'number' || (typeof val === 'string' && val.trim() !== '' && !isNaN(parseFloat(val)));
      });
      summaryRow[header] = isNumericColumn ? 0 : '';
    }
  });

  let columnHasNumericData = {}; // To track if any numeric data was actually summed for a column

  dataCopy.forEach(row => {
    headers.forEach((header, index) => {
      if (index > 0) {
        let value = row[header];

        // This parseFloat is crucial for data coming from CSV parsing (which might be strings)
        // or from JSON.stringify if numbers were strings initially.
        if (typeof value === 'string' && value.trim() !== '' && !isNaN(parseFloat(value))) {
          value = parseFloat(value);
        }

        if (typeof value === 'number' && !isNaN(value)) {
          if (typeof summaryRow[header] !== 'number') summaryRow[header] = 0; // Ensure summary cell is number
          summaryRow[header] += value;
          columnHasNumericData[header] = true; // Mark that this column had numeric data
        }
      }
    });
  });

  // If a column was initialized for sum (e.g., as 0) but no numeric data was found, set it to empty string
  headers.forEach((header, index) => {
    if (index > 0 && !columnHasNumericData[header] && typeof summaryRow[header] === 'number') {
      summaryRow[header] = '';
    }
  });

  dataCopy.push(summaryRow);
  return dataCopy;
};

// Helper function to parse a CSV string into an array of objects
const parseCSVToObjectArray = (csvString) => {
  if (!csvString || typeof csvString !== 'string' || csvString.trim() === '') {
    return [];
  }

  const lines = csvString.trim().split(/\r?\n/);
  if (lines.length < 1) { // At least a header line
    return [];
  }

  const headers = lines[0].split(',').map(h => h.trim()); // Basic header parsing
  const dataArray = [];

  for (let i = 1; i < lines.length; i++) {
    const values = [];
    let currentField = '';
    let inQuotes = false;
    const rowString = lines[i];

    for (let charIndex = 0; charIndex < rowString.length; charIndex++) {
      const char = rowString[charIndex];
      if (char === '"') {
        if (inQuotes && charIndex + 1 < rowString.length && rowString[charIndex + 1] === '"') {
          currentField += '"'; // Escaped quote
          charIndex++;
        } else {
          inQuotes = !inQuotes;
        }
      } else if (char === ',' && !inQuotes) {
        values.push(currentField.trim());
        currentField = '';
      } else {
        currentField += char;
      }
    }
    values.push(currentField.trim()); // Last field

    if (values.length === headers.length) {
      const obj = {};
      headers.forEach((header, index) => {
        let value = values[index];
        const numValue = parseFloat(value);
        // Ensure that conversion to number is clean (e.g. "123" -> 123, not "123USD" -> 123)
        if (!isNaN(numValue) && value.trim() !== '' && String(numValue) === value.trim()) {
          obj[header] = numValue;
        } else {
          obj[header] = value;
        }
      });
      dataArray.push(obj);
    } else {
      console.warn(`Skipping CSV row due to mismatched columns: "${lines[i]}" (Expected ${headers.length}, got ${values.length})`);
    }
  }
  return dataArray;
};

// Helper function to convert an array of objects to a CSV string
const convertObjectArrayToCSV = (objectArray) => {
  if (!objectArray || objectArray.length === 0) {
    return '';
  }
  const headers = Object.keys(objectArray[0]);
  const csvRows = [];

  csvRows.push(headers.map(header => `"${String(header).replace(/"/g, '""')}"`).join(','));

  for (const row of objectArray) {
    const values = headers.map(header => {
      let cellValue = row[header];
      if (cellValue === null || cellValue === undefined) {
        cellValue = '';
      }
      const escaped = String(cellValue).replace(/"/g, '""');
      if (escaped.includes(',') || escaped.includes('"') || escaped.includes('\n') || escaped.includes('\r')) {
        return `"${escaped}"`;
      }
      return escaped;
    });
    csvRows.push(values.join(','));
  }
  return csvRows.join('\n');
};

// Helper function to process a CSV string: parse, optionally add summary, and convert back to CSV
const generateCSVString = (originalCSVString, addSummary, summaryRowLabel = 'Grand Total') => {
  if (!originalCSVString || typeof originalCSVString !== 'string' || originalCSVString.trim() === '') {
    console.warn("Original CSV string is empty or invalid for generateCSVString.");
    return originalCSVString; // Return original if it's empty or invalid
  }

  let objectArray = parseCSVToObjectArray(originalCSVString);

  if (objectArray.length === 0) {
    console.warn("CSV parsing resulted in no data for generateCSVString.");
    const lines = originalCSVString.trim().split(/\r?\n/);
    // If only headers, return the original CSV string (which is just the header line)
    if (lines.length === 1 && lines[0].trim() !== '') {
      return originalCSVString;
    }
    return ""; // Otherwise, if truly empty or parsing failed to produce objects, return empty string
  }

  if (addSummary) {
    objectArray = _calculateAndAddSummaryRow(objectArray, summaryRowLabel);
  }

  return convertObjectArrayToCSV(objectArray);
};

// Helper function to create an Excel Workbook object
const _createExcelWorkbook = (mainData, mainSheetName, additionalSheetsInfo) => {
  const excelWorkBook = XLSX.utils.book_new();

  // Main sheet
  const mainWorkSheet = XLSX.utils.json_to_sheet(mainData);
  const mainWsCols = _autoFitColumns(mainData, mainWorkSheet); // _autoFitColumns needs to be defined
  mainWorkSheet['!cols'] = mainWsCols;
  XLSX.utils.book_append_sheet(excelWorkBook, mainWorkSheet, mainSheetName);

  // Additional sheets
  additionalSheetsInfo.forEach(sheetInfo => {
    if (sheetInfo.data && sheetInfo.data.length > 0) {
      const additionalWorkSheet = XLSX.utils.json_to_sheet(sheetInfo.data);
      const additionalWsCols = _autoFitColumns(sheetInfo.data, additionalWorkSheet);
      additionalWorkSheet['!cols'] = additionalWsCols;
      XLSX.utils.book_append_sheet(excelWorkBook, additionalWorkSheet, sheetInfo.sheet);
    }
  });
  return excelWorkBook;
};

export const generateEmailReport = async (
  reportType,
  data, // For EXCEL, this is an array of objects. For CSV, this is a CSV string.
  reportSheetName,
  reportName,
  reportUrl,
  emailAddress,
  subject,
  text,
  callback,
  excelSheets = [],
  summaryRow = true, // New parameter, defaults to true
) => {
  // console.log('EMAIL data: ', typeof data, data?.length, data); // Safe navigation for length

  // Data emptiness check depends on type. For CSV string, length check is different.
  let isEmptyData = false;
  if (reportType === EMAIL_REPORT_TYPE.CSV && (typeof data !== 'string' || data.trim() === '')) {
    isEmptyData = true;
  } else if (reportType === EMAIL_REPORT_TYPE.EXCEL && (!Array.isArray(data) || data.length === 0)) {
    isEmptyData = true;
  }

  if (isEmptyData) {
    CommonStore.update((s) => {
      s.isLoading = false;
    });
    Alert.alert('Error: Empty data', 'No data found for report generation.');
    return;
  }

  const mainDataSummaryLabel = `Total`; // Consistent label

  if (reportType === EMAIL_REPORT_TYPE.EXCEL) {
    let mainDataProcessed = data; // data is an array of objects for Excel
    if (summaryRow && data.length > 0) { // Add summary only if requested and data exists
      mainDataProcessed = _calculateAndAddSummaryRow(data, mainDataSummaryLabel);
    }

    // Process additional excel sheets (always add summary if data exists, as per original logic)
    let processedExcelSheets = excelSheets.map((sheetInfo) => {
      const additionalSheetLabel = `Total`; // Or use a different label if needed
      return {
        ...sheetInfo,
        data: (sheetInfo.data && sheetInfo.data.length > 0)
          ? _calculateAndAddSummaryRow(sheetInfo.data, additionalSheetLabel)
          : [], // Keep empty if original was empty
      };
    });

    const excelWorkBook = _createExcelWorkbook(mainDataProcessed, reportSheetName, processedExcelSheets);

    if (Platform.Version >= 30) {
      const workBookDataBytes = XLSX.write(excelWorkBook, { type: 'array', bookType: 'xlsx' });
      const reference = storage().ref(reportUrl);
      await reference.put(workBookDataBytes, { contentType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });

      try {
        uploadEmailReportWithRefrencedStorage(reportType, '', reportName, reportUrl, emailAddress, subject, text, callback);
      } catch (e) {
        console.error("Error uploading Excel (API >= 30):", e);
        // Handle error, maybe CommonStore.isLoading = false; Alert.alert(...);
      }
    } else { // Android <= 10 API <= 29
      const excelFile = `${Platform.OS === 'ios' ? RNFS.DocumentDirectoryPath : RNFS.DownloadDirectoryPath}/temp.xlsx`;
      if (await RNFS.exists(excelFile)) { await RNFS.unlink(excelFile); }

      const workBookBinaryString = XLSX.write(excelWorkBook, { type: 'binary', bookType: 'xlsx' });
      RNFS.writeFile(excelFile, workBookBinaryString, 'ascii')
        .then(async () => {
          uploadEmailReport(reportType, excelFile, reportName, reportUrl, emailAddress, subject, text, callback);
        })
        .catch((err) => {
          console.error("Error writing Excel file (API < 30):", err.message);
          CommonStore.update((s) => { s.isLoading = false; });
          Alert.alert('Error', 'Failed to write Excel file.');
        });
    }

  } else if (reportType === EMAIL_REPORT_TYPE.CSV) {
    // For CSV, 'data' is the original CSV string.
    // The 'summaryRow' prop controls if summary is added to this CSV data.
    const csvSummaryLabel = `Total`; // Or summaryRowLabel from params if you want it configurable for CSV too
    const finalCSVString = generateCSVString(data, summaryRow, csvSummaryLabel);

    if (finalCSVString.trim() === '' || (summaryRow && finalCSVString.split(/\r?\n/).length <= 1 && parseCSVToObjectArray(data).length === 0)) {
      // This condition tries to catch if original CSV was just headers and summaryRow was true,
      // or if generateCSVString returned empty due to no actual data post-parsing.
      // The original `processCSVAndAddSummary` had a check for `objectArray.length === 0` after parsing
      // and would return original CSV or ""
      console.warn("CSV processing resulted in effectively empty data. Check original CSV and summary settings.");
      // Decide what to do: perhaps return, or send an empty file, or send the original header-only CSV.
      // For now, if generateCSVString returns an empty string or just headers and summary was requested but no data rows were present...
      // ...we might alert and stop, or proceed to send what we have.
      // The generateCSVString already handles returning originalCSV if it's just headers.
      // If finalCSVString is empty AFTER processing (e.g. originalCSV was empty and summary=false)
      if (finalCSVString.trim() === '') {
        CommonStore.update((s) => { s.isLoading = false; });
        Alert.alert('Error: Empty CSV', 'CSV processing resulted in empty data.');
        return;
      }
    }


    if (Platform.Version >= 30) {
      let enc = new TextEncoder(); // always utf-8
      let arrayBufferText = enc.encode(finalCSVString);

      const reference = storage().ref(reportUrl);
      await reference.put(arrayBufferText, { contentType: 'text/csv; charset=utf-8' }); // Added charset

      try {
        uploadEmailReportWithRefrencedStorage(reportType, '', reportName, reportUrl, emailAddress, subject, text, callback);
      } catch (e) {
        console.error("Error uploading CSV (API >= 30):", e);
      }
    } else { // Android <= 10 API <= 29
      const csvFile = `${Platform.OS === 'ios'
        ? RNFetchBlob.fs.dirs.DocumentDir
        : RNFetchBlob.fs.dirs.DownloadDir
        }/temp.csv`;

      RNFetchBlob.fs.writeFile(csvFile, finalCSVString, 'utf8')
        .then(async () => {
          let filePath = csvFile; // RNFetchBlob path is usually what's needed
          // The getPathForFirebaseStorageFromBlob logic seems specific, ensure it's correct for your setup.
          // It might not be needed if csvFile path is directly usable.
          // filePath = await getPathForFirebaseStorageFromBlob(csvFile); // Original line, keep if necessary

          // if (Platform.OS === 'ios' && filePath && filePath.fileCopyUri) { // Original block
          //   filePath = filePath.fileCopyUri;
          // }

          await uploadEmailReport(reportType, filePath, reportName, reportUrl, emailAddress, subject, text, callback);
        })
        .catch((error) => {
          console.error("Error writing/uploading CSV (API < 30):", error);
          CommonStore.update((s) => { s.isLoading = false; });
          Alert.alert('Error', 'Failed to process or upload CSV file.');
        });
    }
  }
};

export const uploadEmailReport = async (
  reportType,
  filePath,
  reportName,
  reportUrl,
  emailAddress,
  subject,
  text,
  callback,
) => {
  // console.log('reportUrl');
  // console.log(reportUrl);

  const reference = storage().ref(reportUrl);
  await reference.putFile(filePath);

  var body = {
    fileUrlList: [reportUrl],
    fileNameList: [reportName],
    email: emailAddress,
    subject,
    text,
  };

  // console.log(body);

  await new Promise(async (resolve, reject) => {
    ApiClient.POST(API.emailReport, body).then((result) => {
      callback && callback();

      resolve();
    });
  });
};

export const uploadEmailReportWithRefrencedStorage = async (
  reportType,
  filePath,
  reportName,
  reportUrl,
  emailAddress,
  subject,
  text,
  callback,
) => {
  // console.log('reportUrl');
  // console.log(reportUrl);

  var body = {
    fileUrlList: [reportUrl],
    fileNameList: [reportName],
    email: emailAddress,
    subject,
    text,
  };

  // console.log(body);

  await new Promise(async (resolve, reject) => {
    ApiClient.POST(API.emailReport, body).then((result) => {
      callback && callback();

      resolve();
    });
  });
};

export const uploadFileToFirebaseStorageWithCallback = async (
  selectedFile,
  referencePath,
  callback,
) => {
  if (selectedFile) {
    // referencePath = `/clients/profile_image/${uniqueId}/profile_image${selectedImage.type}`;
    // console.log(referencePath);
    const reference = storage().ref(referencePath);
    await reference.putFile(selectedFile);
    // return referencePath;
  } else {
    // return referencePath;
  }

  callback && callback(referencePath);
};

export const uploadImageToFirebaseStorage = async (
  selectedImage,
  referencePath,
) => {
  ////////////////////////////////////////////////////////////

  // firebase storage upload implementation

  // if (selectedImage && selectedImage.uri) {
  //   // referencePath = `/clients/profile_image/${uniqueId}/profile_image${selectedImage.type}`;
  //   // console.log(referencePath);
  //   const reference = storage().ref(referencePath);
  //   await reference.putFile(selectedImage.uri, {
  //     // cacheControl: 'max-age=31536000',
  //   });
  //   return referencePath;
  // } else {
  //   return referencePath;
  // }

  ////////////////////////////////////////////////////////////

  // aws s3 upload implementation

  if (selectedImage && selectedImage.uri) {
    const s3bucket = new AWS.S3({
      accessKeyId: awsId,
      secretAccessKey: awsSecret,
      Bucket: awsBucket,
      signatureVersion: 'v4',
    });

    const contentType = `image/${selectedImage.type.slice(1)}`;
    const contentDeposition = `inline;filename="${referencePath.slice(1)}"`;
    const fPath = selectedImage.uri;
    const base64 = await RNFS.readFile(fPath, 'base64');
    const arrayBuffer = decode(base64);

    return await new Promise(async (resolve, reject) => {
      return await s3bucket.createBucket(async () => {
        const params = {
          Bucket: awsBucket,
          Key: referencePath.slice(1),
          Body: arrayBuffer,
          ContentDisposition: contentDeposition,
          ContentType: contentType,
        };
        return await s3bucket.upload(params, (error, data) => {
          if (error) {
            // console.log('image upload error');
            // console.log(error);
            // reject(getApiError(error));
            resolve('');
          } else {
            // console.log('image upload success');
            // console.log(data);
            // resolve(data);
            resolve(data.Location);
          }
        });
      });
    });
  }

  ////////////////////////////////////////////////////////////
};

export const getImageFromFirebaseStorage = async (imageUrl, callback) => {
  try {
    // const url = await storage()
    //     .ref(imageUrl)
    //     .getDownloadURL()
    //     .then(url => {
    //         // console.log(url);

    //         // return url;
    //         callback && callback(url);
    //     });

    const ref = await storage().ref(imageUrl);

    const url = await ref
      .getDownloadURL()
      .then((url) => {
        // console.log(url);

        // return url;
        callback && callback(url);
      })
      .catch((ex) => {
        console.error('=========inner error url===========');
        console.error(imageUrl);

        console.error(ex);

        return '';
      });
  } catch (ex) {
    console.error('=========error url===========');
    console.error(imageUrl);

    console.error(ex);

    return '';
  }
};

////////////////////////////////////////////////////////

// 2025-03-13 - for po related

export const generateEmailPdf = async (
  reportType,
  data,
  reportSheetName,
  reportName,
  reportUrl,
  emailAddress,
  subject,
  text,
  callback,
) => {
  // console.log('EMAIL data: ', typeof data, data.length, data);

  if (data.length === 0) {
    CommonStore.update((s) => {
      s.isLoading = false;
    });
    Alert.alert('Error: Empty data', 'No data found for upload');
    return;
  }

  const reference = storage().ref(reportUrl);
  await reference.putString(data, 'base64', {
    contentType:
      'application/pdf',
  });

  // upload to email without the need of data (filePath)
  try {
    uploadEmailReportWithRefrencedStorage(
      reportType,
      '',
      reportName,
      reportUrl,
      emailAddress,
      subject,
      text,
      callback,
    );
  } catch (e) {
    // console.log(e);
  }
};

////////////////////////////////////////////////////////

// const getValueFromInfiniteList = (list, index) => {
//     const mappedIndex = index % list;

//     if (index >= list.length) {
//         return list[index]
//     }
// };

const deleteUserCart = async (userCartId) => {
  const body = {
    userCartId,
  };

  ApiClient.POST(API.deleteUserCart, body).then((result) => {
    if (result && result.status === 'success') {
      // console.log('ok');
    }
  });
};

// export const listenToFirestoreChanges = async (firebaseUid, merchantId, currOutletId) => {
//     // const firebaseUid = await AsyncStorage.getItem('firebaseUid');
//     // const merchantId = await AsyncStorage.getItem('merchantId');
//     // const role = await AsyncStorage.getItem('role');

//     // // console.log(firebaseUid);
//     // // console.log(merchantId);
//     // // console.log(role);

//     // Get user details
//     firestore()
//         .collection(Collections.User)
//         .where('firebaseUid', '==', firebaseUid)
//         .onSnapshot(snapshot => {
//             // console.log(`${Collections.User} changed!`);

//             if (snapshot && !snapshot.empty) {
//                 const record = snapshot.docs[0].data();

//                 UserStore.update(s => {
//                     s.avatar = record.avatar;
//                     s.dob = record.dob;
//                     s.email = record.email;
//                     s.gender = record.gender;
//                     s.name = record.name;
//                     s.number = record.number;
//                     s.outletId = record.outledId;
//                     s.race = record.race;
//                     s.state = record.state;
//                     s.uniqueName = record.uniqueName;
//                     s.updatedAt = record.updatedAt;
//                 });
//             }
//         });

//     // Get merchant info
//     firestore()
//         .collection(Collections.Merchant)
//         .where('uniqueId', '==', merchantId)
//         .onSnapshot(snapshot => {
//             // console.log(`${Collections.Merchant} changed!`);

//             if (snapshot && !snapshot.empty) {
//                 const record = snapshot.docs[0].data();

//                 MerchantStore.update(s => {
//                     s.description = record.description;
//                     s.name = record.name;
//                     s.shortcode = record.shortcode;
//                 });
//             }
//         });

//     // Get outlets info
//     firestore()
//         .collection(Collections.Outlet)
//         .where('merchantId', '==', merchantId)
//         .onSnapshot(snapshot => {
//             // console.log(`${Collections.Outlet} changed!`);

//             if (snapshot && !snapshot.empty) {
//                 var outlets = [];

//                 for (var i = 0; i < snapshot.size; i++) {
//                     const record = snapshot.docs[i].data();

//                     outlets.push(record);
//                 }

//                 MerchantStore.update(s => {
//                     s.allOutlets = outlets;
//                 });

//                 if (currOutletId === '') {
//                     var currOutletIdTemp = '';
//                     var currOutletTemp = {};

//                     if (outlets.length > 0) {
//                         currOutletIdTemp = outlets[0].uniqueId;
//                         currOutletTemp = outlets[0];
//                     }

//                     MerchantStore.update(s => {
//                         s.currOutletId = currOutletIdTemp;
//                         s.currOutlet = currOutletTemp;
//                     });
//                 }
//             }
//         });

//     // Get outlets info
//     // firestore()
//     //     .collection(Collections.Outlet)
//     //     .where('merchantId', '==', merchantId)
//     //     .onSnapshot(snapshot => {
//     //         // console.log(`${Collections.Outlet} changed!`);

//     //         if (snapshot && !snapshot.empty) {
//     //             var outlets = [];

//     //             for (var i = 0; i < snapshot.size; i++) {
//     //                 const record = snapshot.docs[i].data();

//     //                 outlets.push(record);
//     //             }

//     //             MerchantStore.update(s => {
//     //                 s.allOutlets = outlets;
//     //             });

//     //             if (currOutletId === '') {
//     //                 var currOutletIdTemp = '';

//     //                 if (outlets.length > 0) {
//     //                     currOutletIdTemp = outlets[0].uniqueId;
//     //                 }

//     //                 MerchantStore.update(s => {
//     //                     s.currOutletId = currOutletId;
//     //                 });
//     //             }
//     //         }
//     //     });

//     if (currOutletId !== '') {
//         // Get current outlet info
//         firestore()
//             .collection(Collections.OutletItem)
//             .where('outletId', '==', outletId)
//             .onSnapshot(snapshot => {
//                 // console.log(`${Collections.OutletItem} changed!`);

//                 if (snapshot && !snapshot.empty) {
//                     var outletItems = [];

//                     for (var i = 0; i < snapshot.size; i++) {
//                         const record = snapshot.docs[i].data();

//                         outletItems.push(record);
//                     }

//                     OutletStore.update(s => {
//                         s.outletItems = outletItems;
//                     });
//                 }
//             });

//         firestore()
//             .collection(Collections.OutletCategory)
//             .where('outletId', '==', outletId)
//             .onSnapshot(snapshot => {
//                 // console.log(`${Collections.OutletCategory} changed!`);

//                 if (snapshot && !snapshot.empty) {
//                     var outletCategories = [];
//                     var outletCategoriesDict = {};

//                     for (var i = 0; i < snapshot.size; i++) {
//                         const record = snapshot.docs[i].data();

//                         outletCategories.push(record);
//                         outletCategoriesDict[record.uniqueId] = record;
//                     }

//                     OutletStore.update(s => {
//                         s.outletCategories = outletCategories;
//                         s.outletCategoriesDict = outletCategoriesDict;
//                     });
//                 }
//             });
//     }
// };

////////////////////////////////////////////////////////////////////////////////////

export const listenToUserOrderHistoricalChanges = async (merchantId, startDate, endDate, sizeLimit = 500, reportBatchSize = 31, currOutletId, isMasterAccount, reportOutletIdList = [], firebaseUid = '', individualShift = false) => {
  console.log('listenToUserOrderHistoricalChanges triggered');
  global.isLoadingReportData = true;

  let startDateOriginal = startDate;
  let endDateOriginal = endDate;

  TempStore.update(s => {
    s.allOutletUserOrderDoneProcessed = [];
  });

  if (global.getRazerPayoutTransactionsParsedTimer) {
    clearTimeout(global.getRazerPayoutTransactionsParsedTimer);
  }

  global.getRazerPayoutTransactionsParsedTimer = setTimeout(() => {
    try {
      let body = {
        isMasterAccount,
        merchantId,
        outletId: currOutletId,
        reportOutletIdList,

        startDate: startDateOriginal,
        endDate: endDateOriginal,
      };

      // ApiClientReporting.POST(API.getRazerPayoutTransactionsParsed, body, false).then((result) => {
      ApiClientReporting.POST(API.getOutletUserOrderDoneProcessed, body, false).then((result) => {
        // console.log(result);
        if (result && result.status === 'success') {
          // global.payoutTransactions = result.payoutTransactions;
          // global.payoutTransactionsExtend = result.payoutTransactionsExtend;

          TempStore.update(s => {
            s.allOutletUserOrderDoneProcessed = result.allOutletUserOrderDoneProcessed;
          });

          OutletStore.update(s => {
            // s.payoutTransactionsExtend = payoutTransactionsExtend;

            // s.allOutletUserOrderDoneProcessed = result.allOutletUserOrderDoneProcessed;

            // s.reportingApiLoading = false;

            s.ptTimestamp = Date.now();
            s.pteTimestamp = Date.now();
          });
        }
        else {
          // OutletStore.update(s => {
          //   // s.payoutTransactionsExtend = payoutTransactionsExtend;

          //   s.reportingApiLoading = false;
          // });
        }

        global.isLoadingReportData = false;
      });
    }
    catch (ex) {
      console.error(ex);

      OutletStore.update(s => {
        // s.payoutTransactionsExtend = payoutTransactionsExtend;

        s.reportingApiLoading = false;
      });
    }
  }, 1000);

  //////////////////////////////////////

  // to save the current snapshot of time, so any changes of global.ptDateTimestamp, can be compared later on, within the same scope

  const ptSavedTimestamp = global.ptDateTimestamp;

  // init data

  // for UserOrder snapshot usage

  var parsedStartDate = moment(startDate).valueOf();
  var parsedEndDate = moment(endDate).valueOf();

  var defaultEndDate = moment().subtract(1, 'day').startOf('day').valueOf();

  if (moment(parsedStartDate).isSameOrAfter(defaultEndDate)) {
    parsedStartDate = defaultEndDate;
  }

  if (moment(parsedEndDate).isSameOrAfter(defaultEndDate)) {
    parsedEndDate = defaultEndDate;
  }

  //////////////////////////////////////

  // incremental data retrieval

  // note: needs to retrieve the data again (if let's got some months data interrupted, need to have workaround for this)

  const startDateFilter = moment(startDateOriginal).add(1, 'day').valueOf();
  const endDateFilter = moment(endDateOriginal).add(1, 'day').valueOf();

  let isReducedRange = false;
  let toResetData = false;

  if (global.ptStartDatePrev && global.ptEndDatePrev) {
    if (moment(startDate).isBefore(global.ptStartDatePrev, 'day') &&
      moment(global.ptEndDatePrev).isSame(endDate, 'day')) {
      // means only moved the start date

      endDate = global.ptStartDatePrev;
    }
    else if (moment(global.ptStartDatePrev).isSame(startDate, 'day') &&
      moment(endDate).isAfter(global.ptEndDatePrev, 'day')) {
      // means only moved the end date

      startDate = global.ptEndDatePrev;
    }
    else if (moment(startDate).isBefore(global.ptStartDatePrev, 'day') &&
      moment(endDate).isAfter(global.ptEndDatePrev, 'day')) {
      // means both dates changed, with either is both dates with larger scope
      // or is 1 date with less than start date, another date with less than end date (intersecting)

      // do nothing here, just use the chose startDate and endDate, can clear the data also

      global.payoutTransactions = [];
      global.payoutTransactionsExtend = [];

      global.ptDict = {};
      global.pteDict = {};

      toResetData = true;
    }
    else if (
      (
        moment(startDate).isBefore(global.ptStartDatePrev, 'day') &&
        moment(endDate).isBefore(global.ptEndDatePrev, 'day')
      )
      ||
      (
        moment(startDate).isAfter(global.ptStartDatePrev, 'day') &&
        moment(endDate).isAfter(global.ptEndDatePrev, 'day')
      )
    ) {
      // means both dates changed, with either is both dates with larger scope
      // or is 1 date with less than start date, another date with less than end date (intersecting)

      // do nothing here, just use the chose startDate and endDate, can clear the data also

      global.payoutTransactions = [];
      global.payoutTransactionsExtend = [];

      global.ptDict = {};
      global.pteDict = {};

      toResetData = true;
    }
    else {
      // date range reduced, retrieved whole data range again

      // try to use the partial data that already retrieved, only retrieve data that outside the range

      isReducedRange = true;

      global.payoutTransactions = global.payoutTransactions.filter(transaction => {
        if (moment(transaction.createdAt).isSameOrAfter(startDateFilter) &&
          moment(transaction.createdAt).isBefore(endDateFilter)) {
          return true;
        }
        else {
          global.ptDict[transaction.uniqueId] = false;

          return false;
        }
      });

      global.payoutTransationsExtend = global.payoutTransactionsExtend.filter(transaction => {
        if (moment(transaction.createdAt).isSameOrAfter(startDateFilter) &&
          moment(transaction.createdAt).isBefore(endDateFilter)) {
          return true;
        }
        else {
          global.pteDict[transaction.uniqueId] = false;

          return false;
        }
      });;
    }
  }

  global.ptStartDatePrev = startDateOriginal;
  global.ptEndDatePrev = endDateOriginal;

  //////////////////////////////////////


  // 2023-01-03 - Calculate batch size

  const diffDays = moment(endDate).diff(startDate, 'day');
  let batchSize = Math.floor(diffDays / reportBatchSize);
  if (diffDays % reportBatchSize > 0) {
    batchSize++; // for the remainders
  }

  let batchPairs = [];
  let batchStartDate = startDate;
  let batchEndDate = startDate;

  if (!isReducedRange) {
    for (let i = 0; i < batchSize; i++) {
      batchEndDate = moment(batchStartDate).add(reportBatchSize, 'day').valueOf();

      if (i === batchSize - 1) {
        batchEndDate = endDate;
      }

      batchPairs.push({
        startDate: batchStartDate,
        endDate: batchEndDate,
        startDateStr: moment(batchStartDate).format('YYYY-MM-DD'),
        endDateStr: moment(batchEndDate).format('YYYY-MM-DD'),
        index: i,

        batchId: uuidv4(),
        status: false, // true means executed & done
      });

      batchStartDate = moment(batchStartDate).add(reportBatchSize, 'day').valueOf();
    }
  }
  else {
    // if range reduced, no need retrieve new data
  }

  console.log('batchPairs');
  console.log(batchPairs);

  //////////////////////////////////////

  // update into existing pairs

  global.ptBatchPairs = batchPairs.concat(global.ptBatchPairs.filter(pair => {
    if (pair.status) {
      // if (moment(startDateOriginal).isSameOrBefore(pair.startDate) &&
      // moment(endDateOriginal).isSameOrAfter(pair.endDate)) {
      //   // the date range filter must be larger than the pair date range

      //   return true;
      // }

      // if tasks didn't executed success, put into task pair list again

      return false;
    }
    else if (toResetData) {
      // if need to reset data, no need previous unfinished batch

      return false;
    }
    else {
      return true;
    }
  }));

  global.pteBatchPairs = batchPairs.concat(global.pteBatchPairs.filter(pair => {
    if (pair.status) {
      // if (moment(startDateOriginal).isSameOrBefore(pair.startDate) &&
      // moment(endDateOriginal).isSameOrAfter(pair.endDate)) {
      //   // the date range filter must be larger than the pair date range

      //   return true;
      // }
      return false;
    }
    else if (toResetData) {
      // if need to reset data, no need previous unfinished batch

      return false;
    }
    else {
      return true;
    }
  }));

  console.log('global.ptBatchPairs');
  console.log(global.ptBathPairs);
  console.log('global.pteBatchPairs');
  console.log(global.pteBathPairs);

  //////////////////////////////////////

  var parsedStartDate = moment(startDate).valueOf();
  var parsedEndDate = moment(endDate).valueOf();

  var defaultEndDate = moment().subtract(1, 'day').startOf('day').valueOf();

  if (moment(parsedStartDate).isSameOrAfter(defaultEndDate)) {
    parsedStartDate = defaultEndDate;
  }

  if (moment(parsedEndDate).isSameOrAfter(defaultEndDate)) {
    parsedEndDate = defaultEndDate;
  }

  var subscriberUserOrderHistorical = () => { };
  var subscriberRazerPayoutTransaction = () => { };
  var subscriberRazerPayoutTransactionExtend = () => { };
  var subscriberOutletShift = () => { };

  if (
    // __DEV__
    true
  ) {
    if (isMasterAccount) {
      // 2025-05-28 - no needed
      // subscriberUserOrderHistorical = firestore()
      //   .collection(Collections.UserOrder)
      //   .where('merchantId', '==', merchantId)
      //   // .where('orderStatus', '==', USER_ORDER_STATUS.ORDER_COMPLETED)
      //   // .orderBy('createdAt') // invalid query, need fix this in future
      //   // .where('createdAt', '<', Date.now())
      //   // .where(
      //   //   'createdAt',
      //   //   '>=',
      //   //   moment().subtract(1, 'day').startOf('day').valueOf(),
      //   // )
      //   .where('createdAt', '>=', parsedStartDate)
      //   .where('createdAt', '<', parsedEndDate)
      //   .orderBy('createdAt', 'desc') // get the latest data first
      //   .limit(sizeLimit)
      //   .onSnapshot((snapshot) => {
      //     global.emitter.emit(`${Collections.UserOrder}-snapshot-historical`, { snapshot });
      //   });

      if (awsBucket === 'koodoodev') {
        ///////////////////////////////////////////////////

        // 2023-01-03 - Batch based

        for (let bpI = 0; bpI < global.ptBatchPairs.length; bpI++) {
          ///////////////////////////////////////////////////

          // RazerPayoutTransaction

          ///////////////////////////////////////////////////

          // check on local db first

          let payoutTransactions = [];
          let payoutTransactionSnapshot = null;

          let isAllDataCached = false;

          // payoutTransactions = await global.watermelonDBDatabase
          //   .get(Collections.RazerPayoutTransaction)
          //   .query(
          //     Q.and(
          //       Q.where('merchantId', Q.eq(merchantId)),
          //       Q.where('createdAt', Q.gte(moment(global.ptBatchPairs[bpI].startDate).add(1, 'day').valueOf())),
          //       Q.where('createdAt', Q.lt(moment(global.ptBatchPairs[bpI].endDate).add(1, 'day').valueOf())),
          //     ),
          //     Q.sortBy('createdAt', 'desc')
          //   );

          console.log('payoutTransactions offline db');
          console.log(payoutTransactions);

          if (payoutTransactions && payoutTransactions.length > 0) {
            const diffDaysBatch = moment(global.ptBatchPairs[bpI].endDate).diff(global.ptBatchPairs[bpI].startDate, 'day');

            if (moment(global.ptBatchPairs[bpI].endDate).isSame(moment(), 'day') ||
              global.ptBatchPairs[bpI].endDate >= moment().valueOf()) {
              // means the end date selection same as today, or larger than current moment (ex: tomorrow, next week, etc)        

              // ex: Jan 16 - Jan 17, diffDaysBatch = 1
              if (payoutTransactions.length >= diffDaysBatch) {
                // means valid (assume this was multi-outlet master account, will work also)

                // payout transactions retrieved from latest to oldest, if multi-outlets, the data stored might not until the oldest date
                // console.log(payoutTransactions[0].bqDt);
                // console.log(moment(global.ptBatchPairs[bpI].startDate).format('YYYY-MM-DD'));
                if (payoutTransactions[payoutTransactions.length - 1].bqDt === moment(global.ptBatchPairs[bpI].startDate).add(1, 'day').format('YYYY-MM-DD')) {
                  isAllDataCached = true;
                }
              }
            }
            else if (payoutTransactions.length >= (diffDaysBatch + 1)) {
              // means valid (assume this was multi-outlet master account, will work also)

              // payout transactions retrieved from latest to oldest, if multi-outlets, the data stored might not until the oldest date
              // console.log(payoutTransactions[0].bqDt);
              // console.log(moment(global.ptBatchPairs[bpI].startDate).format('YYYY-MM-DD'));
              if (payoutTransactions[payoutTransactions.length - 1].bqDt === moment(global.ptBatchPairs[bpI].startDate).add(1, 'day').format('YYYY-MM-DD')) {
                isAllDataCached = true;
              }
            }
          }

          if (isAllDataCached) {
            // means got the data already

            payoutTransactionSnapshot = true;
          }
          else {
            ///////////////////////////////////////////////////

            if (ptSavedTimestamp === global.ptDateTimestamp) {
              payoutTransactionSnapshot = await firestore()
                .collection(Collections.RazerPayoutTransaction)
                .where('merchantId', '==', merchantId)
                .where('createdAt', '>=', moment(global.ptBatchPairs[bpI].startDate).add(1, 'day').valueOf())
                .where('createdAt', '<', moment(global.ptBatchPairs[bpI].endDate).add(1, 'day').valueOf())
                .orderBy('createdAt', 'desc') // get the latest data first
                // .limit(reportBatchSize)
                .get();

              if (ptSavedTimestamp === global.ptDateTimestamp) {
                // var payoutTransactions = [];

                if (payoutTransactionSnapshot) {
                  payoutTransactions = payoutTransactionSnapshot.docs;

                  // here can write into db as well

                  // global.watermelonDBDatabase
                  //   .write(async () => {
                  //     try {
                  //       const writeItems = await payoutTransactions.map(transaction => {
                  //         try {
                  //           return global.watermelonDBDatabase
                  //             .get(Collections.RazerPayoutTransaction)
                  //             .prepareCreate(data => {
                  //               // transaction.uniqueId = data.uniqueId;

                  //               console.log('before modify');

                  //               const transactionParsed = transaction.data();

                  //               // data = transaction.data();

                  //               data.bankAccountName = transactionParsed.bankAccountName;
                  //               data.bankAccountNumber = transactionParsed.bankAccountNumber;
                  //               data.bankCode = transactionParsed.bankCode;
                  //               data.bankType = transactionParsed.bankType;
                  //               data.bqDt = transactionParsed.bqDt;
                  //               data.clientEmail = transactionParsed.clientEmail;
                  //               data.clientId = transactionParsed.clientId;
                  //               data.clientName = transactionParsed.clientName;
                  //               data.clientPhone = transactionParsed.clientPhone;
                  //               data.companyName = transactionParsed.companyName;
                  //               data.contactEmail = transactionParsed.contactEmail;
                  //               data.contactMobile = transactionParsed.contactMobile;
                  //               data.country = transactionParsed.country;
                  //               data.createdAt = transactionParsed.createdAt;
                  //               data.deletedAt = transactionParsed.deletedAt;
                  //               data.isSettledOnSameDay = transactionParsed.isSettledOnSameDay;
                  //               data.merchantId = transactionParsed.merchantId;
                  //               data.merchantLogo = transactionParsed.merchantLogo;
                  //               data.merchantName = transactionParsed.merchantName;
                  //               data.outletCover = transactionParsed.outletCover;
                  //               data.outletCycleFunds = transactionParsed.outletCycleFunds;
                  //               data.outletCycleKoodooPayoutsActual = transactionParsed.outletCycleKoodooPayoutsActual;
                  //               data.outletCycleKoodooPayoutsExpected = transactionParsed.outletCycleKoodooPayoutsExpected;
                  //               data.outletCycleMerchantOverdueAmounts = transactionParsed.outletCycleMerchantOverdueAmounts;
                  //               data.outletCycleMerchantPayoutsActual = transactionParsed.outletCycleMerchantPayoutsActual;
                  //               data.outletCycleMerchantPayoutsExpected = transactionParsed.outletCycleMerchantPayoutsExpected;
                  //               data.outletCycleMerchantPendingAmounts = transactionParsed.outletCycleMerchantPendingAmounts;
                  //               data.outletCycleMerchantPendingRefundOrdersAmount = transactionParsed.outletCycleMerchantPendingRefundOrdersAmount;
                  //               data.outletCycleMerchantRefundOrdersAmount = transactionParsed.outletCycleMerchantRefundOrdersAmount;
                  //               data.outletCycleMerchantToReturnByKoodooFeeAmount = transactionParsed.outletCycleMerchantToReturnByKoodooFeeAmount;
                  //               data.outletCycleRazerPayouts = transactionParsed.outletCycleRazerPayouts;
                  //               data.outletId = transactionParsed.outletId;
                  //               data.outletName = transactionParsed.outletName;
                  //               data.overdueAmountBackup = transactionParsed.overdueAmountBackup;
                  //               data.payeeID = transactionParsed.payeeID;
                  //               data.payoutFee = transactionParsed.payoutFee;
                  //               data.picFullName = transactionParsed.picFullName;
                  //               data.picNRICPassport = transactionParsed.picNRICPassport;
                  //               data.prevOverdueAmount = transactionParsed.prevOverdueAmount;
                  //               data.prevPendingAmount = transactionParsed.prevPendingAmount;
                  //               data.prevStockUpAmount = transactionParsed.prevStockUpAmount;
                  //               data.processingRate = transactionParsed.processingRate;
                  //               data.razerMerchantMassId = transactionParsed.razerMerchantMassId;
                  //               data.razerMerchantReferenceId = transactionParsed.razerMerchantReferenceId;
                  //               data.remarks = transactionParsed.remarks;
                  //               data.startCreatedAt = transactionParsed.startCreatedAt;
                  //               data.stockUpAmount = transactionParsed.stockUpAmount;
                  //               data.stockUpAmountBackup = transactionParsed.stockUpAmountBackup;
                  //               data.uniqueId = transactionParsed.uniqueId;
                  //               data.updatedAt = transactionParsed.updatedAt;
                  //               data.userOrdersFigures = JSON.stringify(transactionParsed.userOrdersFigures);
                  //               data.v = transactionParsed.v;

                  //               // data._raw = sanitizedRaw({ 
                  //               //   id: transactionParsed.uniqueId, 
                  //               //   ...transactionParsed, 
                  //               // }, global.watermelonDBDatabase
                  //               // .get(Collections.RazerPayoutTransaction));

                  //               data._raw.id = transactionParsed.uniqueId;

                  //               console.log('after modify');

                  //               return data;
                  //             });
                  //         } catch (e) {
                  //           console.log(e);
                  //         }
                  //       });

                  //       await global.watermelonDBDatabase.batch(writeItems);
                  //     }
                  //     catch (e) {
                  //       Alert.alert('Error', e.stack ? e.stack : e);
                  //     }
                  //   })
                }
              }
            }
            else {

            }
          }

          /////////////////////////////////////////////////

          // to update on the states

          if (ptSavedTimestamp === global.ptDateTimestamp) {
            global.payoutTransactions = global.payoutTransactions.filter(transaction => {
              if (moment(transaction.createdAt).isSameOrAfter(startDateFilter) &&
                moment(transaction.createdAt).isBefore(endDateFilter)) {
                return true;
              }
              else {
                global.ptDict[transaction.uniqueId] = false;

                return false;
              }
            });

            for (let index = 0; index < payoutTransactions.length; index++) {
              const record = payoutTransactions[index].uniqueId ? payoutTransactions[index] : payoutTransactions[index].data();

              if (!isReducedRange ||
                (isReducedRange &&
                  (
                    moment(record.createdAt).isSameOrAfter(startDateFilter) &&
                    moment(record.createdAt).isBefore(endDateFilter)
                  )
                )) {
                if (!global.ptDict[record.uniqueId]) {
                  global.ptDict[record.uniqueId] = true;

                  global.payoutTransactions.push({
                    ...record,

                    transactionDate: moment(record.createdAt).add(-1, 'day').valueOf(),

                    // 2023-05-03 - for amended fields
                    ...record.remarks >= 'v3' && {
                      userOrdersFigures: (Array.isArray(record.userOrdersFigures) ? record.userOrdersFigures :
                        (typeof record.userOrdersFigures === 'string' ? JSON.parse(record.userOrdersFigures) : [])
                      ).filter(order => {
                        // if (order.orderId === '4da8c7a0-9008-45c3-80ab-4ea602ba07f6') {
                        //   console.log('hit');
                        // }

                        if (order.userOrderPriceBeforeCommission <= 0) {
                          if (order.iro) {
                            return true;
                          }
                          else if (order.colf && order.colf.length > 0) {
                            return true;
                          }
                          else {
                            // are those orders that already joined as bill

                            return false;
                          }
                        }
                        else {
                          return true;
                        }
                      }).map(order => ({
                        ...order,

                        // rptId: record.uniqueId,
                        // ...(order.eiDId) && {
                        //   eiDocId: order.eiDId ? order.eiDId : '',
                        // },
                        // ...(order.eiLId) && {
                        //   eiLongId: order.eiLId ? order.eiLId : '',
                        // },
                        // ...(order.eiS) && {
                        //   eiStatus: order.eiS ? order.eiS : '',
                        // },

                        cartItems: (order.cartItems ? order.cartItems : [])
                          .map(cartItem => ({
                            ...cartItem,

                            itemId: cartItem.id,
                            quantity: cartItem.qty,
                            // price: cartItem.price,
                            discount: cartItem.disc,
                            upsellingCampaignId: cartItem.upsellId,

                            itemCostPrice: cartItem.icp,
                            priceBackup: cartItem.pbu,

                            addOns: cartItem.addons,

                            isFreeItem: cartItem.ifi,
                            priceOriginal: cartItem.po,

                            promotionId: cartItem.pi,
                            discountPromotions: cartItem.dp,
                          }))
                        ,

                        cartItemsCancelled: (order.cic ? order.cic : [])
                          .map(cartItem => ({
                            ...cartItem,

                            itemId: cartItem.id,
                            quantity: cartItem.qty,
                            // price: cartItem.price,
                            discount: cartItem.disc,
                            upsellingCampaignId: cartItem.upsellId,

                            itemCostPrice: cartItem.icp,
                            priceBackup: cartItem.pbu,

                            addOns: cartItem.addons,

                            isFreeItem: cartItem.ifi,
                            priceOriginal: cartItem.po,

                            promotionId: cartItem.pi,
                            discountPromotions: cartItem.dp,
                          }))
                        ,

                        uniqueId: order.orderId,
                        orderId: order.orderIdHuman ? order.orderIdHuman : '',
                        outletId: record.outletId,
                        finalPrice:
                          (
                            (
                              order.iro
                              &&
                              order.ro

                            )
                            ||
                            (
                              order.commissionFeeFinal > 0
                              &&
                              !order.iro
                            )
                            ||
                            (
                              order.commissionFeeFinal <= 0
                              &&
                              !order.iro
                            )
                          )
                            ?
                            order.userOrderPriceBeforeCommission
                            :
                            ((Math.round(Math.max(order.fpb) * 20) / 20)),

                        finalPriceBefore: order.fpb,
                        finalPriceBeforeBackup: order.fpbbu,
                        finalPriceBackup: order.fpbu,
                        discount: order.disc,
                        discountBackup: order.discbu,
                        isRefundOrder: order.iro,

                        isRefundOnline: order.ro ? order.ro : false, // added in 2023-07-29 support
                        combinedOrderListFrom: order.colf ? order.colf : [], // added in 2023-07-29 support

                        waiterName: order.wn,

                        completedDate: order.cd,

                        promotionIdList: order.pidl,
                        promoCodePromotionIdList: order.pcpidl,
                        cartPromotionIdList: order.cpidl,

                        taggableVoucherId: order.tvi,

                        paymentDetails: {
                          channel: order.pdChannel,
                        },

                        orderTypeSub: order.ots ? order.ots : ORDER_TYPE_SUB.NORMAL,
                      })),
                    }
                  });
                }
              }
            }

            if (payoutTransactionSnapshot) {
              console.log('==================================');
              console.log('test info');
              console.log(global.ptBatchPairs);
              console.log(bpI);
              console.log('==================================');

              global.ptBatchPairs[bpI].status = true;

              OutletStore.update(s => {
                // s.payoutTransactions = payoutTransactions;

                s.ptTimestamp = Date.now();
              });
            }
          }

          ///////////////////////////////////////////////////
        }

        for (let bpI = 0; bpI < global.pteBatchPairs.length; bpI++) {

          // RazerPayoutTransactionExtend

          if (ptSavedTimestamp === global.ptDateTimestamp) {
            // 2023-11-03 - for RazerPayoutTransactionExtend

            const payoutTransactionExtendSnapshot = await firestore()
              .collection(Collections.RazerPayoutTransactionExtend)
              .where('merchantId', '==', merchantId)
              .where('createdAt', '>=', moment(global.pteBatchPairs[bpI].startDate).add(1, 'day').valueOf())
              .where('createdAt', '<', moment(global.pteBatchPairs[bpI].endDate).add(1, 'day').valueOf())
              .orderBy('createdAt', 'desc') // get the latest data first
              // .limit(reportBatchSize)
              .get();

            if (ptSavedTimestamp === global.ptDateTimestamp) {
              // var payoutTransactionsExtend = [];

              if (payoutTransactionExtendSnapshot) {
                for (var i = 0; i < payoutTransactionExtendSnapshot.size; i++) {
                  const record = payoutTransactionExtendSnapshot.docs[i].data();

                  if (!isReducedRange ||
                    (isReducedRange &&
                      (
                        moment(record.createdAt).isSameOrAfter(startDateFilter) &&
                        moment(record.createdAt).isBefore(endDateFilter)
                      )
                    )) {
                    if (!global.pteDict[record.uniqueId]) {
                      global.pteDict[record.uniqueId] = true;

                      global.payoutTransactionsExtend.push({
                        ...record,

                        transactionDate: moment(record.createdAt).add(-1, 'day').valueOf(),

                        // 2023-05-03 - for amended fields
                        ...record.remarks >= 'v3' && {
                          userOrdersFigures: (record.userOrdersFigures ? record.userOrdersFigures : []).filter(order => {
                            // if (order.orderId === '4da8c7a0-9008-45c3-80ab-4ea602ba07f6') {
                            //   console.log('hit');
                            // }

                            if (order.userOrderPriceBeforeCommission <= 0) {
                              if (order.iro) {
                                return true;
                              }
                              else if (order.colf && order.colf.length > 0) {
                                return true;
                              }
                              else {
                                // are those orders that already joined as bill

                                return false;
                              }
                            }
                            else {
                              return true;
                            }
                          }).map(order => ({
                            ...order,

                            // rpteId: record.uniqueId,
                            // ...(order.eiDId) && {
                            //   eiDocId: order.eiDId ? order.eiDId : '',
                            // },
                            // ...(order.eiLId) && {
                            //   eiLongId: order.eiLId ? order.eiLId : '',
                            // },
                            // ...(order.eiS) && {
                            //   eiStatus: order.eiS ? order.eiS : '',
                            // },

                            cartItems: (order.cartItems ? order.cartItems : [])
                              .map(cartItem => ({
                                ...cartItem,

                                itemId: cartItem.id,
                                quantity: cartItem.qty,
                                // price: cartItem.price,
                                discount: cartItem.disc,
                                upsellingCampaignId: cartItem.upsellId,

                                itemCostPrice: cartItem.icp,
                                priceBackup: cartItem.pbu,

                                addOns: cartItem.addons,

                                isFreeItem: cartItem.ifi,
                                priceOriginal: cartItem.po,

                                promotionId: cartItem.pi,
                                discountPromotions: cartItem.dp,
                              }))
                            ,

                            cartItemsCancelled: (order.cic ? order.cic : [])
                              .map(cartItem => ({
                                ...cartItem,

                                itemId: cartItem.id,
                                quantity: cartItem.qty,
                                // price: cartItem.price,
                                discount: cartItem.disc,
                                upsellingCampaignId: cartItem.upsellId,

                                itemCostPrice: cartItem.icp,
                                priceBackup: cartItem.pbu,

                                addOns: cartItem.addons,

                                isFreeItem: cartItem.ifi,
                                priceOriginal: cartItem.po,

                                promotionId: cartItem.pi,
                                discountPromotions: cartItem.dp,
                              }))
                            ,

                            uniqueId: order.orderId,
                            orderId: order.orderIdHuman ? order.orderIdHuman : '',
                            outletId: record.outletId,
                            finalPrice:
                              (
                                (
                                  order.iro
                                  &&
                                  order.ro

                                )
                                ||
                                (
                                  order.commissionFeeFinal > 0
                                  &&
                                  !order.iro
                                )
                                ||
                                (
                                  order.commissionFeeFinal <= 0
                                  &&
                                  !order.iro
                                )
                              )
                                ?
                                order.userOrderPriceBeforeCommission
                                :
                                ((Math.round(Math.max(order.fpb) * 20) / 20)),

                            finalPriceBefore: order.fpb,
                            finalPriceBeforeBackup: order.fpbbu,
                            finalPriceBackup: order.fpbu,
                            discount: order.disc,
                            discountBackup: order.discbu,
                            isRefundOrder: order.iro,

                            isRefundOnline: order.ro ? order.ro : false, // added in 2023-07-29 support
                            combinedOrderListFrom: order.colf ? order.colf : [], // added in 2023-07-29 support

                            waiterName: order.wn,

                            completedDate: order.cd,

                            promotionIdList: order.pidl,
                            promoCodePromotionIdList: order.pcpidl,
                            cartPromotionIdList: order.cpidl,

                            taggableVoucherId: order.tvi,

                            paymentDetails: {
                              channel: order.pdChannel,
                            },

                            orderTypeSub: order.ots ? order.ots : ORDER_TYPE_SUB.NORMAL,
                          })),
                        }
                      });
                    }
                  }
                }
              }

              if (payoutTransactionExtendSnapshot) {
                global.pteBatchPairs[bpI].status = true;

                OutletStore.update(s => {
                  // s.payoutTransactionsExtend = payoutTransactionsExtend;

                  s.pteTimestamp = Date.now();
                });
              }
            }
          }
        }

        ///////////////////////////////////////////////////
      }
    }
    else {
      // 2025-05-28 - no needed
      // subscriberUserOrderHistorical = firestore()
      //   .collection(Collections.UserOrder)
      //   .where('outletId', '==', currOutletId)
      //   // .where('orderStatus', '==', USER_ORDER_STATUS.ORDER_COMPLETED)
      //   // .orderBy('createdAt') // invalid query, need fix this in future
      //   // .where('createdAt', '<', Date.now())
      //   // .where(
      //   //   'createdAt',
      //   //   '>=',
      //   //   moment().subtract(1, 'day').startOf('day').valueOf(),
      //   // )
      //   .where('createdAt', '>=', parsedStartDate)
      //   .where('createdAt', '<', parsedEndDate)
      //   .orderBy('createdAt', 'desc') // get the latest data first
      //   .limit(sizeLimit)
      //   .onSnapshot((snapshot) => {
      //     global.emitter.emit(`${Collections.UserOrder}-snapshot-historical`, { snapshot });
      //   });

      if (awsBucket === 'koodoodev') {
        ///////////////////////////////////////////////////

        // 2023-01-03 - Batch based

        for (let bpI = 0; bpI < global.ptBatchPairs.length; bpI++) {
          ///////////////////////////////////////////////////

          // RazerPayoutTransaction

          ///////////////////////////////////////////////////

          // check on local db first

          let payoutTransactions = [];
          let payoutTransactionSnapshot = null;

          let isAllDataCached = false;

          // payoutTransactions = await global.watermelonDBDatabase
          //   .get(Collections.RazerPayoutTransaction)
          //   .query(
          //     Q.and(
          //       Q.where('outletId', Q.eq(currOutletId)),
          //       Q.where('createdAt', Q.gte(moment(global.ptBatchPairs[bpI].startDate).add(1, 'day').valueOf())),
          //       Q.where('createdAt', Q.lt(moment(global.ptBatchPairs[bpI].endDate).add(1, 'day').valueOf())),
          //     ),
          //     Q.sortBy('createdAt', 'desc')
          //   );

          console.log('payoutTransactions offline db');
          console.log(payoutTransactions);

          if (payoutTransactions && payoutTransactions.length > 0) {
            const diffDaysBatch = moment(global.ptBatchPairs[bpI].endDate).diff(global.ptBatchPairs[bpI].startDate, 'day');

            if (moment(global.ptBatchPairs[bpI].endDate).isSame(moment(), 'day') ||
              global.ptBatchPairs[bpI].endDate >= moment().valueOf()) {
              // means the end date selection same as today, or larger than current moment (ex: tomorrow, next week, etc)        

              // ex: Jan 16 - Jan 17, diffDaysBatch = 1
              if (payoutTransactions.length >= diffDaysBatch) {
                // means valid (assume this was multi-outlet master account, will work also)

                // payout transactions retrieved from latest to oldest, if multi-outlets, the data stored might not until the oldest date
                // console.log(payoutTransactions[0].bqDt);
                // console.log(moment(global.ptBatchPairs[bpI].startDate).format('YYYY-MM-DD'));
                if (payoutTransactions[payoutTransactions.length - 1].bqDt === moment(global.ptBatchPairs[bpI].startDate).add(1, 'day').format('YYYY-MM-DD')) {
                  isAllDataCached = true;
                }
              }
            }
            else if (payoutTransactions.length >= (diffDaysBatch + 1)) {
              // means valid (assume this was multi-outlet master account, will work also)

              // payout transactions retrieved from latest to oldest, if multi-outlets, the data stored might not until the oldest date
              // console.log(payoutTransactions[0].bqDt);
              // console.log(moment(global.ptBatchPairs[bpI].startDate).format('YYYY-MM-DD'));
              if (payoutTransactions[payoutTransactions.length - 1].bqDt === moment(global.ptBatchPairs[bpI].startDate).add(1, 'day').format('YYYY-MM-DD')) {
                isAllDataCached = true;
              }
            }
          }

          if (isAllDataCached) {
            // means got the data already

            payoutTransactionSnapshot = true;
          }
          else {
            ///////////////////////////////////////////////////

            if (ptSavedTimestamp === global.ptDateTimestamp) {
              payoutTransactionSnapshot = await firestore()
                .collection(Collections.RazerPayoutTransaction)
                .where('outletId', '==', currOutletId)
                .where('createdAt', '>=', moment(global.ptBatchPairs[bpI].startDate).add(1, 'day').valueOf())
                .where('createdAt', '<', moment(global.ptBatchPairs[bpI].endDate).add(1, 'day').valueOf())
                .orderBy('createdAt', 'desc') // get the latest data first
                // .limit(reportBatchSize)
                .get();

              if (ptSavedTimestamp === global.ptDateTimestamp) {
                // var payoutTransactions = [];

                if (payoutTransactionSnapshot) {
                  payoutTransactions = payoutTransactionSnapshot.docs;

                  // here can write into db as well

                  // await global.watermelonDBDatabase
                  //   .write(async () => {
                  //     try {
                  //       const writeItems = await payoutTransactions.map(transaction => {
                  //         try {
                  //           return global.watermelonDBDatabase
                  //             .get(Collections.RazerPayoutTransaction)
                  //             .prepareCreate(data => {
                  //               // transaction.uniqueId = data.uniqueId;

                  //               console.log('before modify');

                  //               const transactionParsed = transaction.data();

                  //               // data = transaction.data();

                  //               data.bankAccountName = transactionParsed.bankAccountName;
                  //               data.bankAccountNumber = transactionParsed.bankAccountNumber;
                  //               data.bankCode = transactionParsed.bankCode;
                  //               data.bankType = transactionParsed.bankType;
                  //               data.bqDt = transactionParsed.bqDt;
                  //               data.clientEmail = transactionParsed.clientEmail;
                  //               data.clientId = transactionParsed.clientId;
                  //               data.clientName = transactionParsed.clientName;
                  //               data.clientPhone = transactionParsed.clientPhone;
                  //               data.companyName = transactionParsed.companyName;
                  //               data.contactEmail = transactionParsed.contactEmail;
                  //               data.contactMobile = transactionParsed.contactMobile;
                  //               data.country = transactionParsed.country;
                  //               data.createdAt = transactionParsed.createdAt;
                  //               data.deletedAt = transactionParsed.deletedAt;
                  //               data.isSettledOnSameDay = transactionParsed.isSettledOnSameDay;
                  //               data.merchantId = transactionParsed.merchantId;
                  //               data.merchantLogo = transactionParsed.merchantLogo;
                  //               data.merchantName = transactionParsed.merchantName;
                  //               data.outletCover = transactionParsed.outletCover;
                  //               data.outletCycleFunds = transactionParsed.outletCycleFunds;
                  //               data.outletCycleKoodooPayoutsActual = transactionParsed.outletCycleKoodooPayoutsActual;
                  //               data.outletCycleKoodooPayoutsExpected = transactionParsed.outletCycleKoodooPayoutsExpected;
                  //               data.outletCycleMerchantOverdueAmounts = transactionParsed.outletCycleMerchantOverdueAmounts;
                  //               data.outletCycleMerchantPayoutsActual = transactionParsed.outletCycleMerchantPayoutsActual;
                  //               data.outletCycleMerchantPayoutsExpected = transactionParsed.outletCycleMerchantPayoutsExpected;
                  //               data.outletCycleMerchantPendingAmounts = transactionParsed.outletCycleMerchantPendingAmounts;
                  //               data.outletCycleMerchantPendingRefundOrdersAmount = transactionParsed.outletCycleMerchantPendingRefundOrdersAmount;
                  //               data.outletCycleMerchantRefundOrdersAmount = transactionParsed.outletCycleMerchantRefundOrdersAmount;
                  //               data.outletCycleMerchantToReturnByKoodooFeeAmount = transactionParsed.outletCycleMerchantToReturnByKoodooFeeAmount;
                  //               data.outletCycleRazerPayouts = transactionParsed.outletCycleRazerPayouts;
                  //               data.outletId = transactionParsed.outletId;
                  //               data.outletName = transactionParsed.outletName;
                  //               data.overdueAmountBackup = transactionParsed.overdueAmountBackup;
                  //               data.payeeID = transactionParsed.payeeID;
                  //               data.payoutFee = transactionParsed.payoutFee;
                  //               data.picFullName = transactionParsed.picFullName;
                  //               data.picNRICPassport = transactionParsed.picNRICPassport;
                  //               data.prevOverdueAmount = transactionParsed.prevOverdueAmount;
                  //               data.prevPendingAmount = transactionParsed.prevPendingAmount;
                  //               data.prevStockUpAmount = transactionParsed.prevStockUpAmount;
                  //               data.processingRate = transactionParsed.processingRate;
                  //               data.razerMerchantMassId = transactionParsed.razerMerchantMassId;
                  //               data.razerMerchantReferenceId = transactionParsed.razerMerchantReferenceId;
                  //               data.remarks = transactionParsed.remarks;
                  //               data.startCreatedAt = transactionParsed.startCreatedAt;
                  //               data.stockUpAmount = transactionParsed.stockUpAmount;
                  //               data.stockUpAmountBackup = transactionParsed.stockUpAmountBackup;
                  //               data.uniqueId = transactionParsed.uniqueId;
                  //               data.updatedAt = transactionParsed.updatedAt;
                  //               data.userOrdersFigures = JSON.stringify(transactionParsed.userOrdersFigures);
                  //               data.v = transactionParsed.v;

                  //               // data._raw = sanitizedRaw({ 
                  //               //   id: transactionParsed.uniqueId, 
                  //               //   ...transactionParsed, 
                  //               // }, global.watermelonDBDatabase
                  //               // .get(Collections.RazerPayoutTransaction));

                  //               data._raw.id = transactionParsed.uniqueId;

                  //               console.log('after modify');

                  //               return data;
                  //             });
                  //         } catch (e) {
                  //           console.log(e);
                  //         }
                  //       });

                  //       await global.watermelonDBDatabase.batch(writeItems);
                  //     }
                  //     catch (e) {
                  //       Alert.alert('Error', e.stack ? e.stack : e);
                  //     }
                  //   })
                }
              }
            }
            else {

            }
          }

          /////////////////////////////////////////////////

          // to update on the states

          if (ptSavedTimestamp === global.ptDateTimestamp) {
            global.payoutTransactions = global.payoutTransactions.filter(transaction => {
              if (moment(transaction.createdAt).isSameOrAfter(startDateFilter) &&
                moment(transaction.createdAt).isBefore(endDateFilter)) {
                return true;
              }
              else {
                global.ptDict[transaction.uniqueId] = false;

                return false;
              }
            });

            for (let index = 0; index < payoutTransactions.length; index++) {
              const record = payoutTransactions[index].uniqueId ? payoutTransactions[index] : payoutTransactions[index].data();

              if (!isReducedRange ||
                (isReducedRange &&
                  (
                    moment(record.createdAt).isSameOrAfter(startDateFilter) &&
                    moment(record.createdAt).isBefore(endDateFilter)
                  )
                )) {
                if (!global.ptDict[record.uniqueId]) {
                  global.ptDict[record.uniqueId] = true;

                  global.payoutTransactions.push({
                    ...record,

                    transactionDate: moment(record.createdAt).add(-1, 'day').valueOf(),

                    // 2023-05-03 - for amended fields
                    ...record.remarks >= 'v3' && {
                      userOrdersFigures: (Array.isArray(record.userOrdersFigures) ? record.userOrdersFigures :
                        (typeof record.userOrdersFigures === 'string' ? JSON.parse(record.userOrdersFigures) : [])
                      ).filter(order => {
                        // if (order.orderId === '4da8c7a0-9008-45c3-80ab-4ea602ba07f6') {
                        //   console.log('hit');
                        // }

                        if (order.userOrderPriceBeforeCommission <= 0) {
                          if (order.iro) {
                            return true;
                          }
                          else if (order.colf && order.colf.length > 0) {
                            return true;
                          }
                          else {
                            // are those orders that already joined as bill

                            return false;
                          }
                        }
                        else {
                          return true;
                        }
                      }).map(order => ({
                        ...order,

                        // rptId: record.uniqueId,
                        // ...(order.eiDId) && {
                        //   eiDocId: order.eiDId ? order.eiDId : '',
                        // },
                        // ...(order.eiLId) && {
                        //   eiLongId: order.eiLId ? order.eiLId : '',
                        // },
                        // ...(order.eiS) && {
                        //   eiStatus: order.eiS ? order.eiS : '',
                        // },

                        cartItems: (order.cartItems ? order.cartItems : [])
                          .map(cartItem => ({
                            ...cartItem,

                            itemId: cartItem.id,
                            quantity: cartItem.qty,
                            // price: cartItem.price,
                            discount: cartItem.disc,
                            upsellingCampaignId: cartItem.upsellId,

                            itemCostPrice: cartItem.icp,
                            priceBackup: cartItem.pbu,

                            addOns: cartItem.addons,

                            isFreeItem: cartItem.ifi,
                            priceOriginal: cartItem.po,

                            promotionId: cartItem.pi,
                            discountPromotions: cartItem.dp,
                          }))
                        ,

                        cartItemsCancelled: (order.cic ? order.cic : [])
                          .map(cartItem => ({
                            ...cartItem,

                            itemId: cartItem.id,
                            quantity: cartItem.qty,
                            // price: cartItem.price,
                            discount: cartItem.disc,
                            upsellingCampaignId: cartItem.upsellId,

                            itemCostPrice: cartItem.icp,
                            priceBackup: cartItem.pbu,

                            addOns: cartItem.addons,

                            isFreeItem: cartItem.ifi,
                            priceOriginal: cartItem.po,

                            promotionId: cartItem.pi,
                            discountPromotions: cartItem.dp,
                          }))
                        ,

                        uniqueId: order.orderId,
                        orderId: order.orderIdHuman ? order.orderIdHuman : '',
                        outletId: record.outletId,
                        finalPrice:
                          (
                            (
                              order.iro
                              &&
                              order.ro

                            )
                            ||
                            (
                              order.commissionFeeFinal > 0
                              &&
                              !order.iro
                            )
                            ||
                            (
                              order.commissionFeeFinal <= 0
                              &&
                              !order.iro
                            )
                          )
                            ?
                            order.userOrderPriceBeforeCommission
                            :
                            ((Math.round(Math.max(order.fpb) * 20) / 20)),

                        finalPriceBefore: order.fpb,
                        finalPriceBeforeBackup: order.fpbbu,
                        finalPriceBackup: order.fpbu,
                        discount: order.disc,
                        discountBackup: order.discbu,
                        isRefundOrder: order.iro,

                        isRefundOnline: order.ro ? order.ro : false, // added in 2023-07-29 support
                        combinedOrderListFrom: order.colf ? order.colf : [], // added in 2023-07-29 support

                        waiterName: order.wn,

                        completedDate: order.cd,

                        promotionIdList: order.pidl,
                        promoCodePromotionIdList: order.pcpidl,
                        cartPromotionIdList: order.cpidl,

                        taggableVoucherId: order.tvi,

                        paymentDetails: {
                          channel: order.pdChannel,
                        },

                        orderTypeSub: order.ots ? order.ots : ORDER_TYPE_SUB.NORMAL,
                      })),
                    }
                  });
                }
              }
            }

            if (payoutTransactionSnapshot) {
              console.log('==================================');
              console.log('test info');
              console.log(global.ptBatchPairs);
              console.log(bpI);
              console.log('==================================');

              global.ptBatchPairs[bpI].status = true;

              OutletStore.update(s => {
                // s.payoutTransactions = payoutTransactions;

                s.ptTimestamp = Date.now();
              });
            }
          }

          ///////////////////////////////////////////////////
        }

        for (let bpI = 0; bpI < global.pteBatchPairs.length; bpI++) {

          // RazerPayoutTransactionExtend

          if (ptSavedTimestamp === global.ptDateTimestamp) {
            // 2023-11-03 - for RazerPayoutTransactionExtend

            const payoutTransactionExtendSnapshot = await firestore()
              .collection(Collections.RazerPayoutTransactionExtend)
              .where('outletId', '==', currOutletId)
              .where('createdAt', '>=', moment(global.pteBatchPairs[bpI].startDate).add(1, 'day').valueOf())
              .where('createdAt', '<', moment(global.pteBatchPairs[bpI].endDate).add(1, 'day').valueOf())
              .orderBy('createdAt', 'desc') // get the latest data first
              // .limit(reportBatchSize)
              .get();

            if (ptSavedTimestamp === global.ptDateTimestamp) {
              // var payoutTransactionsExtend = [];

              if (payoutTransactionExtendSnapshot) {
                for (var i = 0; i < payoutTransactionExtendSnapshot.size; i++) {
                  const record = payoutTransactionExtendSnapshot.docs[i].data();

                  if (!isReducedRange ||
                    (isReducedRange &&
                      (
                        moment(record.createdAt).isSameOrAfter(startDateFilter) &&
                        moment(record.createdAt).isBefore(endDateFilter)
                      )
                    )) {
                    if (!global.pteDict[record.uniqueId]) {
                      global.pteDict[record.uniqueId] = true;

                      global.payoutTransactionsExtend.push({
                        ...record,

                        transactionDate: moment(record.createdAt).add(-1, 'day').valueOf(),

                        // 2023-05-03 - for amended fields
                        ...record.remarks >= 'v3' && {
                          userOrdersFigures: (record.userOrdersFigures ? record.userOrdersFigures : []).filter(order => {
                            // if (order.orderId === '4da8c7a0-9008-45c3-80ab-4ea602ba07f6') {
                            //   console.log('hit');
                            // }

                            if (order.userOrderPriceBeforeCommission <= 0) {
                              if (order.iro) {
                                return true;
                              }
                              else if (order.colf && order.colf.length > 0) {
                                return true;
                              }
                              else {
                                // are those orders that already joined as bill

                                return false;
                              }
                            }
                            else {
                              return true;
                            }
                          }).map(order => ({
                            ...order,

                            // rpteId: record.uniqueId,
                            // ...(order.eiDId) && {
                            //   eiDocId: order.eiDId ? order.eiDId : '',
                            // },
                            // ...(order.eiLId) && {
                            //   eiLongId: order.eiLId ? order.eiLId : '',
                            // },
                            // ...(order.eiS) && {
                            //   eiStatus: order.eiS ? order.eiS : '',
                            // },

                            cartItems: (order.cartItems ? order.cartItems : [])
                              .map(cartItem => ({
                                ...cartItem,

                                itemId: cartItem.id,
                                quantity: cartItem.qty,
                                // price: cartItem.price,
                                discount: cartItem.disc,
                                upsellingCampaignId: cartItem.upsellId,

                                itemCostPrice: cartItem.icp,
                                priceBackup: cartItem.pbu,

                                addOns: cartItem.addons,

                                isFreeItem: cartItem.ifi,
                                priceOriginal: cartItem.po,

                                promotionId: cartItem.pi,
                                discountPromotions: cartItem.dp,
                              }))
                            ,

                            cartItemsCancelled: (order.cic ? order.cic : [])
                              .map(cartItem => ({
                                ...cartItem,

                                itemId: cartItem.id,
                                quantity: cartItem.qty,
                                // price: cartItem.price,
                                discount: cartItem.disc,
                                upsellingCampaignId: cartItem.upsellId,

                                itemCostPrice: cartItem.icp,
                                priceBackup: cartItem.pbu,

                                addOns: cartItem.addons,

                                isFreeItem: cartItem.ifi,
                                priceOriginal: cartItem.po,

                                promotionId: cartItem.pi,
                                discountPromotions: cartItem.dp,
                              }))
                            ,

                            uniqueId: order.orderId,
                            orderId: order.orderIdHuman ? order.orderIdHuman : '',
                            outletId: record.outletId,
                            finalPrice:
                              (
                                (
                                  order.iro
                                  &&
                                  order.ro

                                )
                                ||
                                (
                                  order.commissionFeeFinal > 0
                                  &&
                                  !order.iro
                                )
                                ||
                                (
                                  order.commissionFeeFinal <= 0
                                  &&
                                  !order.iro
                                )
                              )
                                ?
                                order.userOrderPriceBeforeCommission
                                :
                                ((Math.round(Math.max(order.fpb) * 20) / 20)),

                            finalPriceBefore: order.fpb,
                            finalPriceBeforeBackup: order.fpbbu,
                            finalPriceBackup: order.fpbu,
                            discount: order.disc,
                            discountBackup: order.discbu,
                            isRefundOrder: order.iro,

                            isRefundOnline: order.ro ? order.ro : false, // added in 2023-07-29 support
                            combinedOrderListFrom: order.colf ? order.colf : [], // added in 2023-07-29 support

                            waiterName: order.wn,

                            completedDate: order.cd,

                            promotionIdList: order.pidl,
                            promoCodePromotionIdList: order.pcpidl,
                            cartPromotionIdList: order.cpidl,

                            taggableVoucherId: order.tvi,

                            paymentDetails: {
                              channel: order.pdChannel,
                            },

                            orderTypeSub: order.ots ? order.ots : ORDER_TYPE_SUB.NORMAL,
                          })),
                        }
                      });
                    }
                  }
                }
              }

              if (payoutTransactionExtendSnapshot) {
                global.pteBatchPairs[bpI].status = true;

                OutletStore.update(s => {
                  // s.payoutTransactionsExtend = payoutTransactionsExtend;

                  s.pteTimestamp = Date.now();
                });
              }
            }
          }
        }

        ///////////////////////////////////////////////////
      }
    }
  }

  if (individualShift) {
    subscriberOutletShift = firestore()
      .collection(Collections.OutletShift)
      .where('outletId', '==', currOutletId)
      .where('userIdOpen', '==', firebaseUid)
      .where('createdAt', '>=', moment(startDateOriginal).valueOf())
      .where('createdAt', '<', moment(endDateOriginal).valueOf())
      .orderBy('createdAt', 'desc')
      .onSnapshot(async (snapshot) => {
        global.emitter.emit(`${Collections.OutletShift}-snapshot`, { snapshot });
      });
  }
  else {
    subscriberOutletShift = firestore()
      .collection(Collections.OutletShift)
      .where('outletId', '==', currOutletId)
      .where('createdAt', '>=', moment(startDateOriginal).valueOf())
      .where('createdAt', '<', moment(endDateOriginal).valueOf())
      .orderBy('createdAt', 'desc')
      .onSnapshot(async (snapshot) => {
        global.emitter.emit(`${Collections.OutletShift}-snapshot`, { snapshot });
      });
  }

  var subscriberSummary = () => {
    // 2025-05-28 - no needed
    // subscriberUserOrderHistorical();
    subscriberRazerPayoutTransaction();
    subscriberOutletShift();
  };

  return subscriberSummary;
};

export const listenToOutletReviewHistoricalChanges = async (merchantId, startDate, endDate, sizeLimit = 500, currOutletId, isMasterAccount) => {
  var parsedStartDate = moment(startDate).valueOf();
  var parsedEndDate = moment(endDate).valueOf();

  // var defaultEndDate = moment().subtract(1, 'day').startOf('day').valueOf();

  // if (moment(parsedStartDate).isSameOrAfter(defaultEndDate)) {
  //   parsedStartDate = defaultEndDate;
  // }

  // if (moment(parsedEndDate).isSameOrAfter(defaultEndDate)) {
  //   parsedEndDate = defaultEndDate;
  // }

  // var subscriberUserOrderHistorical = () => { };
  // var subscriberRazerPayoutTransaction = () => { };

  const outletReviewSnapshot = await firestore()
    .collection(Collections.CustomerReview)
    .where('outletId', '==', currOutletId)
    .where('createdAt', '>=', moment(parsedStartDate).valueOf())
    .where('createdAt', '<', moment(parsedEndDate).valueOf())
    .orderBy('createdAt', 'desc') // get the latest data first
    .limit(sizeLimit)
    .get();

  var outletReviews = [];

  if (outletReviewSnapshot) {
    for (var i = 0; i < outletReviewSnapshot.size; i++) {
      const record = outletReviewSnapshot.docs[i].data();

      outletReviews.push(record);
    }
  }

  if (outletReviewSnapshot) {
    OutletStore.update(s => {
      s.outletReviews = outletReviews;
    });
  }

  var subscriberSummary = () => {
    // subscriberUserOrderHistorical();
    // subscriberRazerPayoutTransaction();
  };

  return subscriberSummary;
};

export const listenToUserChangesMerchant = (
  firebaseUid,
  currOutletId,
) => {
  // Get user details

  var subscriberUser = () => { };

  subscriberUser = firestore()
    .collection(Collections.User)
    .where('firebaseUid', '==', firebaseUid)
    .onSnapshot(async (snapshot) => {
      global.emitter.emit(`${Collections.User}-snapshot`, { snapshot });
    });

  return () => {
    subscriberUser();
  };
};

export const listenToMerchantIdChangesMerchant = (merchantId, currOutletId, isMasterAccount, currOutlet = {}) => {
  var subscriberUserOrder = () => { };
  var subscriberMerchant = () => { };
  var subscriberOutletItem = () => { };
  var subscriberOutletItemCategory = () => { };
  var subscriberOutletItemAddOn = () => { };
  var subscriberOutletItemAddOnChoice = () => { };
  var subscriberOutletOpening = () => { };
  var subscriberUserAllOutletsEmployees = () => { };
  var subscriberUserAction = () => { };
  var subscriberEmployeeClock = () => { };
  var subscriberUserOrderLoyalty = () => { };
  var subscriberPreorderPackage = () => { };
  var subscriberSupplier = () => { };
  var subscriberSupplierProduct = () => { };
  var subscriberSupplyItem = () => { };
  var subscriberOutletSupplyItem = () => { };
  var subscriberStockTransfer = () => { };
  var subscriberStockTransferProduct = () => { };
  var subscriberStockTake = () => { };
  var subscriberStockTakeProduct = () => { };
  var subscriberStockReturnProduct = () => { };
  var subscriberOutletShift = () => { };
  var subscriberBeerDocketCategory = () => { };
  var subscriberBeerDocket = () => { };
  var subscriberUserBeerDocket = () => { };
  var subscriberUserOrderBeerDocket = () => { };
  var subscriberPromotion = () => { };
  var subscriberLoyaltyCampaign = () => { };
  var subscriberUpsellingCampaign = () => { };
  var subscriberTaggableVoucher = () => { };
  var subscriberTaggableVoucherMaster = () => { };
  var subscriberUserFavoriteMerchantIdList = () => { };
  var subscriberUserLinkedMerchantIdList = () => { };
  var subscriberCRMUserTag = () => { };
  var subscriberCRMSegment = () => { };
  var subscriberOutletPaymentMethod = () => { };
  var subscriberLoyaltyStamp = () => { };
  var subscriberLoyaltyStampType = () => { };
  var subscriberTopupCreditType = () => { };
  var subscriberUserOrderMetadata = () => { };
  var subscriberOutletCatalog = () => { };
  var subscriberWOItem = () => { };
  var subscriberWOList = () => { };
  var subscriberOSIT = () => { };

  if (isMasterAccount) {
    // Get merchant info
    subscriberMerchant = firestore()
      .collection(Collections.Merchant)
      .where('uniqueId', '==', merchantId)
      .onSnapshot((snapshot) => {
        global.emitter.emit(`${Collections.Merchant}-snapshot`, { snapshot });
      });

    subscriberOutletItem = firestore()
      .collection(Collections.OutletItem)
      // .where('merchantId', '==', merchantId)
      .where('outletId', '==', currOutletId)
      .onSnapshot((snapshot) => {
        global.emitter.emit(`${Collections.OutletItem}-snapshot`, { snapshot });
      });

    subscriberOutletItemCategory = firestore()
      .collection(Collections.OutletItemCategory)
      .where('merchantId', '==', merchantId)
      .onSnapshot((snapshot) => {
        global.emitter.emit(`${Collections.OutletItemCategory}-snapshot`, { snapshot });
      });

    // Get outlets info
    subscriberOutletItemAddOn = firestore()
      .collection(Collections.OutletItemAddOn)
      // .where('merchantId', '==', merchantId)
      .where('outletId', '==', currOutletId)
      .onSnapshot((snapshot) => {
        global.emitter.emit(`${Collections.OutletItemAddOn}-snapshot`, { snapshot });
      });

    subscriberOutletItemAddOnChoice = firestore()
      .collection(Collections.OutletItemAddOnChoice)
      // .where('merchantId', '==', merchantId)
      .where('outletId', '==', currOutletId)
      .onSnapshot((snapshot) => {
        global.emitter.emit(`${Collections.OutletItemAddOnChoice}-snapshot`, { snapshot });
      });

    subscriberOutletOpening = firestore()
      .collection(Collections.OutletOpening)
      .where('outletId', '==', currOutletId)
      .onSnapshot((snapshot) => {
        global.emitter.emit(`${Collections.OutletOpening}-snapshot`, { snapshot });
      });

    // optimizations
    // firestore()
    //   .collection(Collections.MerchantVoucher)
    //   .where('merchantId', '==', merchantId)
    //   .onSnapshot(async (snapshot) => {
    //     // console.log(`${Collections.MerchantVoucher} changed!`);

    //     var merchantVouchers = [];
    //     var merchantVouchersDict = {};

    //     if (snapshot && !snapshot.empty) {
    //       for (var i = 0; i < snapshot.size; i++) {
    //         const record = snapshot.docs[i].data();

    //         merchantVouchers.push(record);
    //         merchantVouchersDict[record.uniqueId] = record;
    //       }
    //     }

    //     merchantVouchers.sort((a, b) => b.updatedAt - a.updatedAt);

    //     CommonStore.update((s) => {
    //       s.merchantVouchers = merchantVouchers;
    //       s.merchantVouchersDict = merchantVouchersDict;
    //     });
    //   });

    // optimizations
    // firestore()
    //   .collection(Collections.MerchantVoucherReport)
    //   .where('merchantId', '==', merchantId)
    //   .onSnapshot(async (snapshot) => {
    //     // console.log(`${Collections.MerchantVoucherReport} changed!`);

    //     var merchantVoucherReportsVoucherIdDict = {};

    //     if (snapshot && !snapshot.empty) {
    //       for (var i = 0; i < snapshot.size; i++) {
    //         const record = snapshot.docs[i].data();

    //         merchantVoucherReportsVoucherIdDict[record.voucherId] = record;
    //       }
    //     }

    //     CommonStore.update((s) => {
    //       s.merchantVoucherReportsVoucherIdDict =
    //         merchantVoucherReportsVoucherIdDict;
    //     });
    //   });

    setTimeout(() => {
      global.firstStartDict[FIRST_START_KEY.ONE] = true;

      // 2025-05-22 - can comment to reduce memory usage
      // 2025-05-28 - no needed
      // subscriberUserOrder = firestore()
      //   .collection(Collections.UserOrder)
      //   // .where('merchantId', '==', merchantId)
      //   .where('outletId', '==', currOutletId)
      //   // .where('orderStatus', '==', USER_ORDER_STATUS.ORDER_COMPLETED)
      //   // .orderBy('createdAt') // invalid query, need fix this in future
      //   // .where('createdAt', '<', Date.now())
      //   // .where(
      //   //   'createdAt',
      //   //   '>=',
      //   //   moment().subtract(1, 'day').startOf('day').valueOf(),
      //   // )
      //   .where(
      //     'paymentDate',
      //     '>=',
      //     moment().subtract(1, 'day').startOf('day').valueOf(),
      //   )
      //   .onSnapshot((snapshot) => {
      //     global.emitter.emit(`${Collections.UserOrder}-snapshot`, { snapshot });
      //   });

      // subscriberOutletShift = firestore()
      //   .collection(Collections.OutletShift)
      //   .where('outletId', '==', currOutletId)
      //   .orderBy('createdAt', 'desc')
      //   .onSnapshot(async (snapshot) => {
      //     global.emitter.emit(`${Collections.OutletShift}-snapshot`, { snapshot });
      //   });

      subscriberCRMUserTag = firestore()
        .collection(Collections.CRMUserTag)
        .where('merchantId', '==', merchantId)
        .where('deletedAt', '==', null)
        .onSnapshot((snapshot) => {
          global.emitter.emit(`${Collections.CRMUserTag}-snapshot`, { snapshot });
        });

      subscriberCRMSegment = firestore()
        .collection(Collections.CRMSegment)
        .where('merchantId', '==', merchantId)
        .where('deletedAt', '==', null)
        .onSnapshot((snapshot) => {
          global.emitter.emit(`${Collections.CRMSegment}-snapshot`, { snapshot });
        });
    }, global.firstStartDict[FIRST_START_KEY.ONE] ? 0 : (Platform.OS === 'ios' ? 5000 : 10000));

    setTimeout(() => {
      global.firstStartDict[FIRST_START_KEY.TWO] = true;

      subscriberUserAllOutletsEmployees = firestore()
        .collection(Collections.User)
        .where('merchantId', '==', merchantId)
        // .orderBy('createdAt') // invalid query, need fix this in future
        .onSnapshot((snapshot) => {
          global.emitter.emit(`${Collections.User}-snapshot-allOutletsEmployees`, { snapshot });
        });

      // subscriberUserAction = firestore()
      //   .collection(Collections.UserAction)
      //   .where('merchantId', '==', merchantId)
      //   // .orderBy('createdAt') // invalid query, need fix this in future
      //   .onSnapshot((snapshot) => {
      //     global.emitter.emit(`${Collections.UserAction}-snapshot`, { snapshot });
      //   });

      subscriberEmployeeClock = firestore()
        .collection(Collections.EmployeeClock)
        .where('merchantId', '==', merchantId)
        // .orderBy('createdAt') // invalid query, need fix this in future
        .onSnapshot((snapshot) => {
          global.emitter.emit(`${Collections.EmployeeClock}-snapshot`, { snapshot });
        });

      // optimizations
      subscriberBeerDocketCategory = firestore()
        .collection(Collections.BeerDocketCategory)
        .where('outletId', '==', currOutletId)
        .onSnapshot((snapshot) => {
          global.emitter.emit(`${Collections.BeerDocketCategory}-snapshot`, { snapshot });
        });

      // optimizations
      subscriberBeerDocket = firestore()
        .collection(Collections.BeerDocket)
        .where('outletId', '==', currOutletId)
        .onSnapshot((snapshot) => {
          global.emitter.emit(`${Collections.BeerDocket}-snapshot`, { snapshot });
        });

      // optimizations
      subscriberUserBeerDocket = firestore()
        .collection(Collections.UserBeerDocket)
        .where('outletId', '==', currOutletId)
        .onSnapshot((snapshot) => {
          global.emitter.emit(`${Collections.UserBeerDocket}-snapshot`, { snapshot });
        });

      // optimizations
      subscriberUserOrderBeerDocket = firestore()
        .collection(Collections.UserOrderBeerDocket)
        .where('outletId', '==', currOutletId)
        .onSnapshot((snapshot) => {
          global.emitter.emit(`${Collections.UserOrderBeerDocket}-snapshot`, { snapshot });
        });

      // optimizations
      // subscriberStockTransfer = firestore()
      //   .collection(Collections.StockTransfer)
      //   .where('outletId', '==', currOutletId)
      //   .orderBy('updatedAt', 'desc')
      //   .limit(global.currOutlet.ciNum ? global.currOutlet.ciNum : 20)
      //   .onSnapshot(async (snapshot) => {
      //     global.emitter.emit(`${Collections.StockTransfer}-snapshot`, { snapshot });
      //   });

      subscriberUserOrderLoyalty = firestore()
        .collection(Collections.UserOrderLoyalty)
        .where('merchantId', '==', merchantId)
        // .where('orderStatus', '==', USER_ORDER_STATUS.ORDER_COMPLETED)
        // .orderBy('createdAt') // invalid query, need fix this in future
        // .where('createdAt', '<', Date.now())
        .where(
          'createdAt',
          '>=',
          moment().subtract(1, 'month').startOf('day').valueOf(),
        )
        .onSnapshot((snapshot) => {
          global.emitter.emit(`${Collections.UserOrderLoyalty}-snapshot`, { snapshot });
        });

      subscriberPreorderPackage = firestore()
        .collection(Collections.PreorderPackage)
        .where('merchantId', '==', merchantId)
        .onSnapshot((snapshot) => {
          global.emitter.emit(`${Collections.PreorderPackage}-snapshot`, { snapshot });
        });

      // optimizations
      subscriberSupplier = firestore()
        .collection(Collections.Supplier)
        .where('merchantId', '==', merchantId)
        .onSnapshot((snapshot) => {
          global.emitter.emit(`${Collections.Supplier}-snapshot`, { snapshot });
        });

      subscriberSupplierProduct = firestore()
        .collection(Collections.SupplierProduct)
        .where('merchantId', '==', merchantId)
        .onSnapshot((snapshot) => {
          global.emitter.emit(`${Collections.SupplierProduct}-snapshot`, { snapshot });
        });

      // optimizations
      subscriberSupplyItem = firestore()
        .collection(Collections.SupplyItem)
        .where('merchantId', '==', merchantId)
        .where('deletedAt', '==', null)
        .onSnapshot(async (snapshot) => {
          global.emitter.emit(`${Collections.SupplyItem}-snapshot`, { snapshot });
        });

      // optimizations
      subscriberOutletSupplyItem = firestore()
        .collection(Collections.OutletSupplyItem)
        .where('merchantId', '==', merchantId)
        .onSnapshot(async (snapshot) => {
          global.emitter.emit(`${Collections.OutletSupplyItem}-snapshot`, { snapshot });
        });

      subscriberStockTransferProduct = firestore()
        .collection(Collections.StockTransferProduct)
        .where('targetOutletId', '==', currOutletId)
        .orderBy('updatedAt', 'desc')
        .limit(global.currOutlet.iNum ? global.currOutlet.iNum : 20)
        .onSnapshot(async (snapshot) => {
          // console.log(`${Collections.StockTransferProduct} changed!`);

          global.emitter.emit(`${Collections.StockTransferProduct}-snapshot`, { snapshot });
        });

      // optimizations
      // subscriberStockTake = firestore()
      //   .collection(Collections.StockTake)
      //   .where('outletId', '==', currOutletId)
      //   .orderBy('updatedAt', 'desc')
      //   .limit(global.currOutlet.ciNum ? global.currOutlet.ciNum : 20)
      //   .onSnapshot(async (snapshot) => {
      //     global.emitter.emit(`${Collections.StockTake}-snapshot`, { snapshot });
      //   });

      subscriberStockTakeProduct = firestore()
        .collection(Collections.StockTakeProduct)
        .where('outletId', '==', currOutletId)
        .orderBy('updatedAt', 'desc')
        .limit(global.currOutlet.iNum ? global.currOutlet.iNum : 20)
        .onSnapshot(async (snapshot) => {
          global.emitter.emit(`${Collections.StockTakeProduct}-snapshot`, { snapshot });
        });

      subscriberStockReturnProduct = firestore()
        .collection(Collections.StockReturnProduct)
        .where('outletId', '==', currOutletId)
        .orderBy('updatedAt', 'desc')
        .limit(global.currOutlet.iNum ? global.currOutlet.iNum : 20)
        .onSnapshot(async (snapshot) => {
          global.emitter.emit(`${Collections.StockReturnProduct}-snapshot`, { snapshot });
        });

      subscriberTopupCreditType = firestore()
        .collection(Collections.TopupCreditType)
        .where('merchantId', '==', merchantId)
        .onSnapshot((snapshot) => {
          global.emitter.emit(`${Collections.TopupCreditType}-snapshot`, { snapshot });
        });

      subscriberUpsellingCampaign = firestore()
        .collection(Collections.UpsellingCampaign)
        .where('outletId', '==', currOutletId)
        .where('deletedAt', '==', null)
        .onSnapshot((snapshot) => {
          global.emitter.emit(`${Collections.UpsellingCampaign}-snapshot`, { snapshot });
        });

      //loyaltystamptype
      subscriberLoyaltyStampType = firestore()
        .collection(Collections.LoyaltyStampType)
        .where('merchantId', '==', merchantId)
        .onSnapshot((snapshot) => {
          global.emitter.emit(`${Collections.LoyaltyStampType}-snapshot`, { snapshot });
        });
    }, global.firstStartDict[FIRST_START_KEY.TWO] ? 0 : (Platform.OS === 'ios' ? 8000 : 30000));

    // Herks - 2022/05/30 - Try to split two portion of data for user orders list, one for actively monitored for real time changes, another is for cache purpose

    // firestore()
    //   .collection(Collections.UserOrder)
    //   .where('merchantId', '==', merchantId)
    //   // .where('orderStatus', '==', USER_ORDER_STATUS.ORDER_COMPLETED)
    //   // .orderBy('createdAt') // invalid query, need fix this in future
    //   .onSnapshot((snapshot) => {
    //     // console.log('=================================');
    //     // console.log(snapshot);
    //     // console.log('=================================');
    //     // console.log(`${Collections.UserOrder} changed!`);
    //     // console.log(snapshot.size);

    //     if (snapshot && !snapshot.empty) {
    //       var userOrders = [];
    //       var allOutletsUserOrders = [];

    //       for (var i = 0; i < snapshot.size; i++) {
    //         const record = snapshot.docs[i].data();

    //         if (record.combinedOrderList &&
    //           record.combinedOrderList.length > 0) {
    //           // means this order already merged with other orders
    //           // // console.log('GONEEEE',record)
    //         }
    //         else {
    //           userOrders.push(record);
    //         }

    //         // if (record.orderStatus === USER_ORDER_STATUS.ORDER_COMPLETED) {
    //         //   userOrders.push(record);
    //         // }

    //         allOutletsUserOrders.push(record);
    //       }

    //       OutletStore.update((s) => {
    //         s.allOutletsUserOrdersDone = userOrders;
    //         s.allOutletsUserOrders = allOutletsUserOrders;
    //       });
    //     }
    //   });

    // optimizations
    // firestore()
    //   .collection(Collections.PointsRedeemPackage)
    //   .where('merchantId', '==', merchantId)
    //   .onSnapshot((snapshot) => {
    //     // console.log(`${Collections.PointsRedeemPackage} changed!`);

    //     var pointsRedeemPackages = [];
    //     var pointsRedeemPackagesDict = {};

    //     if (snapshot && !snapshot.empty) {
    //       // var selectedOutletItemCategory = {};

    //       for (var i = 0; i < snapshot.size; i++) {
    //         const record = snapshot.docs[i].data();

    //         pointsRedeemPackages.push(record);
    //         pointsRedeemPackagesDict[record.uniqueId] = record;
    //       }

    //       pointsRedeemPackages.sort((a, b) =>
    //         (a.packageName ? a.packageName : '').localeCompare(
    //           b.packageName ? b.packageName : '',
    //         ),
    //       );
    //     }

    //     OutletStore.update((s) => {
    //       s.pointsRedeemPackages = pointsRedeemPackages;
    //       s.pointsRedeemPackagesDict = pointsRedeemPackagesDict;
    //     });
    //   });

    /////////////////////////////////////////

    // optimizations
    subscriberSupplier = firestore()
      .collection(Collections.Supplier)
      .where('merchantId', '==', merchantId)
      .onSnapshot((snapshot) => {
        global.emitter.emit(`${Collections.Supplier}-snapshot`, { snapshot });
      });

    subscriberSupplierProduct = firestore()
      .collection(Collections.SupplierProduct)
      .where('merchantId', '==', merchantId)
      .onSnapshot((snapshot) => {
        global.emitter.emit(`${Collections.SupplierProduct}-snapshot`, { snapshot });
      });

    // optimizations
    subscriberSupplyItem = firestore()
      .collection(Collections.SupplyItem)
      .where('merchantId', '==', merchantId)
      .where('deletedAt', '==', null)
      .onSnapshot(async (snapshot) => {
        global.emitter.emit(`${Collections.SupplyItem}-snapshot`, { snapshot });
      });

    // optimizations
    subscriberOutletSupplyItem = firestore()
      .collection(Collections.OutletSupplyItem)
      .where('merchantId', '==', merchantId)
      .onSnapshot(async (snapshot) => {
        global.emitter.emit(`${Collections.OutletSupplyItem}-snapshot`, { snapshot });
      });

    // optimizations
    // subscriberStockTransfer = firestore()
    //   .collection(Collections.StockTransfer)
    //   .where('outletId', '==', currOutletId)
    //   .orderBy('updatedAt', 'desc')
    //   .limit(global.currOutlet.ciNum ? global.currOutlet.ciNum : 20)
    //   .onSnapshot(async (snapshot) => {
    //     global.emitter.emit(`${Collections.StockTransfer}-snapshot`, { snapshot });
    //   });

    subscriberStockTransferProduct = firestore()
      .collection(Collections.StockTransferProduct)
      .where('targetOutletId', '==', currOutletId)
      .orderBy('updatedAt', 'desc')
      .limit(global.currOutlet.iNum ? global.currOutlet.iNum : 20)
      .onSnapshot(async (snapshot) => {
        // console.log(`${Collections.StockTransferProduct} changed!`);

        global.emitter.emit(`${Collections.StockTransferProduct}-snapshot`, { snapshot });
      });

    // optimizations
    // subscriberStockTake = firestore()
    //   .collection(Collections.StockTake)
    //   .where('outletId', '==', currOutletId)
    //   .orderBy('updatedAt', 'desc')
    //   .limit(global.currOutlet.ciNum ? global.currOutlet.ciNum : 20)
    //   .onSnapshot(async (snapshot) => {
    //     global.emitter.emit(`${Collections.StockTake}-snapshot`, { snapshot });
    //   });

    subscriberStockTakeProduct = firestore()
      .collection(Collections.StockTakeProduct)
      .where('outletId', '==', currOutletId)
      .orderBy('updatedAt', 'desc')
      .limit(global.currOutlet.iNum ? global.currOutlet.iNum : 20)
      .onSnapshot(async (snapshot) => {
        global.emitter.emit(`${Collections.StockTakeProduct}-snapshot`, { snapshot });
      });

    subscriberStockReturnProduct = firestore()
      .collection(Collections.StockReturnProduct)
      .where('outletId', '==', currOutletId)
      .orderBy('updatedAt', 'desc')
      .limit(global.currOutlet.iNum ? global.currOutlet.iNum : 20)
      .onSnapshot(async (snapshot) => {
        global.emitter.emit(`${Collections.StockReturnProduct}-snapshot`, { snapshot });
      });

    // subscriberOutletShift = firestore()
    //   .collection(Collections.OutletShift)
    //   .where('outletId', '==', currOutletId)
    //   .orderBy('createdAt', 'desc')
    //   .onSnapshot(async (snapshot) => {
    //     global.emitter.emit(`${Collections.OutletShift}-snapshot`, { snapshot });
    //   });

    // optimizations
    subscriberBeerDocketCategory = firestore()
      .collection(Collections.BeerDocketCategory)
      .where('outletId', '==', currOutletId)
      .onSnapshot((snapshot) => {
        global.emitter.emit(`${Collections.BeerDocketCategory}-snapshot`, { snapshot });
      });

    // optimizations
    subscriberBeerDocket = firestore()
      .collection(Collections.BeerDocket)
      .where('outletId', '==', currOutletId)
      .onSnapshot((snapshot) => {
        global.emitter.emit(`${Collections.BeerDocket}-snapshot`, { snapshot });
      });

    // optimizations
    subscriberUserBeerDocket = firestore()
      .collection(Collections.UserBeerDocket)
      .where('outletId', '==', currOutletId)
      .onSnapshot((snapshot) => {
        global.emitter.emit(`${Collections.UserBeerDocket}-snapshot`, { snapshot });
      });

    // optimizations
    subscriberUserOrderBeerDocket = firestore()
      .collection(Collections.UserOrderBeerDocket)
      .where('outletId', '==', currOutletId)
      .onSnapshot((snapshot) => {
        global.emitter.emit(`${Collections.UserOrderBeerDocket}-snapshot`, { snapshot });
      });

    subscriberPromotion = firestore()
      .collection(Collections.Promotion)
      .where('outletId', '==', currOutletId)
      .where('deletedAt', '==', null)
      .onSnapshot((snapshot) => {
        global.emitter.emit(`${Collections.Promotion}-snapshot`, { snapshot });
      });

    subscriberLoyaltyCampaign = firestore()
      .collection(Collections.LoyaltyCampaign)
      .where('outletId', '==', currOutletId)
      .where('deletedAt', '==', null)
      .onSnapshot((snapshot) => {
        global.emitter.emit(`${Collections.LoyaltyCampaign}-snapshot`, { snapshot });
      });

    subscriberTaggableVoucher = firestore()
      .collection(Collections.TaggableVoucher)
      .where('outletId', '==', currOutletId)
      .where('deletedAt', '==', null)
      .onSnapshot((snapshot) => {
        global.emitter.emit(`${Collections.TaggableVoucher}-snapshot`, { snapshot });
      });

    ///////////////////////////////////////////

    // 2025-02-12 - for master account's voucher support

    if (!currOutlet.isMasterAccount && global.allOutlets.length === 1) {
      subscriberTaggableVoucherMaster = firestore()
        .collection(Collections.TaggableVoucher)
        .where('merchantId', '==', merchantId)
        .where('isMasterAccount', '==', true)
        .where('deletedAt', '==', null)
        .onSnapshot((snapshot) => {
          global.emitter.emit(`${Collections.TaggableVoucher}-master-snapshot`, { snapshot });
        });
    }
    else {
      OutletStore.update(s => {
        s.taggableVouchersMerchant = [];
      });
    }

    ///////////////////////////////////////////

    // 2024-04-02 - no need first

    // subscriberUserFavoriteMerchantIdList = firestore()
    //   .collection(Collections.User)
    //   .where('favoriteMerchantIdList', 'array-contains', merchantId)
    //   .where('deletedAt', '==', null)
    //   .onSnapshot((snapshot) => {
    //     global.emitter.emit(`${Collections.User}-snapshot-favoriteMerchantIdList`, { snapshot });
    //   });

    // subscriberUserLinkedMerchantIdList = firestore()
    //   .collection(Collections.User)
    //   .where('linkedMerchantIdList', 'array-contains', merchantId)
    //   .where('deletedAt', '==', null)
    //   .onSnapshot((snapshot) => {
    //     global.emitter.emit(`${Collections.User}-snapshot-linkedMerchantIdList`, { snapshot });
    //   });

    ///////////////////////////////////////////

    // need change to more cost-effective
    // firestore()
    //     .collection(Collections.UserFavoriteOutlet)
    //     .where('linkedMerchantIdList', 'array-contains', merchantId)
    //     .where('deletedAt', '==', null)
    //     .onSnapshot(snapshot => {
    //         // console.log(`${Collections.User} changed!`);

    //         var linkedMerchantIdUsers = [];

    //         if (snapshot && !snapshot.empty) {
    //             for (var i = 0; i < snapshot.size; i++) {
    //                 const record = snapshot.docs[i].data();

    //                 linkedMerchantIdUsers.push(record);
    //             }
    //         }

    //         OutletStore.update(s => {
    //             s.linkedMerchantIdUsers = linkedMerchantIdUsers;
    //         });
    //     });

    ///////////////////////////////////////////////

    // 2022-08-15 - Do on outlet basis better

    // firestore()
    //   .collection(Collections.CRMUser)
    //   .where('merchantId', '==', merchantId)
    //   // .where('outletId', '==', outletId)
    //   .where('deletedAt', '==', null)
    //   .onSnapshot((snapshot) => {
    //     // console.log(`${Collections.CRMUser} changed!`);

    //     var crmUsersRaw = [];
    //     //var crmUsersDict = {};

    //     if (snapshot && !snapshot.empty) {
    //       var crmUsersDict = {};

    //       for (var i = 0; i < snapshot.size; i++) {
    //         const record = snapshot.docs[i].data();

    //         crmUsersRaw.push(record);
    //         // crmUsersDict[record.uniqueId] = record;
    //       }
    //     }

    //     OutletStore.update((s) => {
    //       s.crmUsersRaw = crmUsersRaw;
    //       // s.crmUsersDict = crmUsersDict;
    //     });
    //   });

    ///////////////////////////////////////////////

    subscriberOutletPaymentMethod = firestore()
      .collection(Collections.OutletPaymentMethod)
      .where('merchantId', '==', merchantId)
      .onSnapshot(async (snapshot) => {
        global.emitter.emit(`${Collections.OutletPaymentMethod}-snapshot`, { snapshot });
      });

    // optimizations
    subscriberLoyaltyStamp = firestore()
      .collection(Collections.LoyaltyStamp)
      .where('outletId', '==', currOutletId)
      .onSnapshot((snapshot) => {
        global.emitter.emit(`${Collections.LoyaltyStamp}-snapshot`, { snapshot });
      });

    subscriberOutletCatalog = firestore()
      .collection(Collections.OutletCatalog)
      .where('outletId', '==', currOutletId)
      .onSnapshot((snapshot) => {
        global.emitter.emit(`${Collections.OutletCatalog}-snapshot`, { snapshot });
      });

    subscriberWOItem = firestore()
      .collection(Collections.WOItem)
      // .where('merchantId', '==', merchantId)
      .where('outletId', '==', currOutletId)
      .where('status', '==', 'PENDING')
      .where('deletedAt', '==', null)
      .orderBy('updatedAt', 'desc')
      .limit(100)
      .onSnapshot((snapshot) => {
        global.emitter.emit(`${Collections.WOItem}-snapshot`, { snapshot });
      });

    subscriberWOList = firestore()
      .collection(Collections.WOList)
      .where('outletId', '==', currOutletId)
      .where('status', '==', 'PENDING')
      .where('deletedAt', '==', null)
      .orderBy('updatedAt', 'desc')
      .limit(100)
      .onSnapshot((snapshot) => {
        global.emitter.emit(`${Collections.WOList}-snapshot`, { snapshot });
      });

    subscriberOSIT = firestore()
      .collection(Collections.OutletSupplyItemTransaction)
      .where('outletId', '==', currOutletId)
      .where('deletedAt', '==', null)
      .onSnapshot((snapshot) => {
        global.emitter.emit(`${Collections.OutletSupplyItemTransaction}-snapshot`, { snapshot });
      });
  }
  else {
    // for sub account

    // Get merchant info
    subscriberMerchant = firestore()
      .collection(Collections.Merchant)
      .where('uniqueId', '==', merchantId)
      .onSnapshot((snapshot) => {
        global.emitter.emit(`${Collections.Merchant}-snapshot`, { snapshot });
      });

    subscriberOutletItem = firestore()
      .collection(Collections.OutletItem)
      .where('outletId', '==', currOutletId)
      .onSnapshot((snapshot) => {
        global.emitter.emit(`${Collections.OutletItem}-snapshot`, { snapshot });
      });

    subscriberOutletItemCategory = firestore()
      .collection(Collections.OutletItemCategory)
      .where('outletId', '==', currOutletId)
      .onSnapshot((snapshot) => {
        global.emitter.emit(`${Collections.OutletItemCategory}-snapshot`, { snapshot });
      });

    // Get outlets info
    subscriberOutletItemAddOn = firestore()
      .collection(Collections.OutletItemAddOn)
      .where('outletId', '==', currOutletId)
      .onSnapshot((snapshot) => {
        global.emitter.emit(`${Collections.OutletItemAddOn}-snapshot`, { snapshot });
      });

    subscriberOutletItemAddOnChoice = firestore()
      .collection(Collections.OutletItemAddOnChoice)
      .where('outletId', '==', currOutletId)
      .onSnapshot((snapshot) => {
        global.emitter.emit(`${Collections.OutletItemAddOnChoice}-snapshot`, { snapshot });
      });

    subscriberOutletOpening = firestore()
      .collection(Collections.OutletOpening)
      .where('outletId', '==', currOutletId)
      .onSnapshot((snapshot) => {
        global.emitter.emit(`${Collections.OutletOpening}-snapshot`, { snapshot });
      });

    // optimizations
    // firestore()
    //   .collection(Collections.MerchantVoucher)
    //   .where('merchantId', '==', merchantId)
    //   .onSnapshot(async (snapshot) => {
    //     // console.log(`${Collections.MerchantVoucher} changed!`);

    //     var merchantVouchers = [];
    //     var merchantVouchersDict = {};

    //     if (snapshot && !snapshot.empty) {
    //       for (var i = 0; i < snapshot.size; i++) {
    //         const record = snapshot.docs[i].data();

    //         merchantVouchers.push(record);
    //         merchantVouchersDict[record.uniqueId] = record;
    //       }
    //     }

    //     merchantVouchers.sort((a, b) => b.updatedAt - a.updatedAt);

    //     CommonStore.update((s) => {
    //       s.merchantVouchers = merchantVouchers;
    //       s.merchantVouchersDict = merchantVouchersDict;
    //     });
    //   });

    // optimizations
    // firestore()
    //   .collection(Collections.MerchantVoucherReport)
    //   .where('merchantId', '==', merchantId)
    //   .onSnapshot(async (snapshot) => {
    //     // console.log(`${Collections.MerchantVoucherReport} changed!`);

    //     var merchantVoucherReportsVoucherIdDict = {};

    //     if (snapshot && !snapshot.empty) {
    //       for (var i = 0; i < snapshot.size; i++) {
    //         const record = snapshot.docs[i].data();

    //         merchantVoucherReportsVoucherIdDict[record.voucherId] = record;
    //       }
    //     }

    //     CommonStore.update((s) => {
    //       s.merchantVoucherReportsVoucherIdDict =
    //         merchantVoucherReportsVoucherIdDict;
    //     });
    //   });

    setTimeout(() => {
      global.firstStartDict[FIRST_START_KEY.ONE] = true;

      // 2025-05-28 - no need
      // subscriberUserOrder = firestore()
      //   .collection(Collections.UserOrder)
      //   .where('outletId', '==', currOutletId)
      //   // .where('orderStatus', '==', USER_ORDER_STATUS.ORDER_COMPLETED)
      //   // .orderBy('createdAt') // invalid query, need fix this in future
      //   // .where('createdAt', '<', Date.now())
      //   .where(
      //     'createdAt',
      //     '>=',
      //     moment().subtract(1, 'day').startOf('day').valueOf(),
      //   )
      //   .onSnapshot((snapshot) => {
      //     global.emitter.emit(`${Collections.UserOrder}-snapshot`, { snapshot });
      //   });

      subscriberCRMUserTag = firestore()
        .collection(Collections.CRMUserTag)
        .where('merchantId', '==', merchantId)
        .where('deletedAt', '==', null)
        .onSnapshot((snapshot) => {
          global.emitter.emit(`${Collections.CRMUserTag}-snapshot`, { snapshot });
        });

      subscriberCRMSegment = firestore()
        .collection(Collections.CRMSegment)
        .where('merchantId', '==', merchantId)
        .where('deletedAt', '==', null)
        .onSnapshot((snapshot) => {
          global.emitter.emit(`${Collections.CRMSegment}-snapshot`, { snapshot });
        });
    }, global.firstStartDict[FIRST_START_KEY.ONE] ? 0 : (Platform.OS === 'ios' ? 5000 : 10000));

    setTimeout(() => {
      global.firstStartDict[FIRST_START_KEY.TWO] = true;

      subscriberUserAllOutletsEmployees = firestore()
        .collection(Collections.User)
        .where('outletId', '==', currOutletId)
        // .orderBy('createdAt') // invalid query, need fix this in future
        .onSnapshot((snapshot) => {
          global.emitter.emit(`${Collections.User}-snapshot-allOutletsEmployees`, { snapshot });
        });

      // subscriberUserAction = firestore()
      //   .collection(Collections.UserAction)
      //   .where('outletId', '==', currOutletId)
      //   // .orderBy('createdAt') // invalid query, need fix this in future
      //   .onSnapshot((snapshot) => {
      //     global.emitter.emit(`${Collections.UserAction}-snapshot`, { snapshot });
      //   });

      subscriberEmployeeClock = firestore()
        .collection(Collections.EmployeeClock)
        .where('outletId', '==', currOutletId)
        // .orderBy('createdAt') // invalid query, need fix this in future
        .onSnapshot((snapshot) => {
          global.emitter.emit(`${Collections.EmployeeClock}-snapshot`, { snapshot });
        });

      subscriberUserOrderLoyalty = firestore()
        .collection(Collections.UserOrderLoyalty)
        .where('outletId', '==', currOutletId)
        // .where('orderStatus', '==', USER_ORDER_STATUS.ORDER_COMPLETED)
        // .orderBy('createdAt') // invalid query, need fix this in future
        // .where('createdAt', '<', Date.now())
        .where(
          'createdAt',
          '>=',
          moment().subtract(1, 'month').startOf('day').valueOf(),
        )
        .onSnapshot((snapshot) => {
          global.emitter.emit(`${Collections.UserOrderLoyalty}-snapshot`, { snapshot });
        });

      subscriberPreorderPackage = firestore()
        .collection(Collections.PreorderPackage)
        .where('outletId', '==', currOutletId)
        .onSnapshot((snapshot) => {
          global.emitter.emit(`${Collections.PreorderPackage}-snapshot`, { snapshot });
        });

      // optimizations
      subscriberSupplier = firestore()
        .collection(Collections.Supplier)
        .where('outletId', '==', currOutletId)
        .onSnapshot((snapshot) => {
          global.emitter.emit(`${Collections.Supplier}-snapshot`, { snapshot });
        });

      subscriberSupplierProduct = firestore()
        .collection(Collections.SupplierProduct)
        .where('outletId', '==', currOutletId)
        .onSnapshot((snapshot) => {
          global.emitter.emit(`${Collections.SupplierProduct}-snapshot`, { snapshot });
        });

      // optimizations
      subscriberSupplyItem = firestore()
        .collection(Collections.SupplyItem)
        .where('outletId', '==', currOutletId)
        .where('deletedAt', '==', null)
        .onSnapshot(async (snapshot) => {
          global.emitter.emit(`${Collections.SupplyItem}-snapshot`, { snapshot });
        });

      // optimizations
      subscriberOutletSupplyItem = firestore()
        .collection(Collections.OutletSupplyItem)
        .where('outletId', '==', currOutletId)
        .onSnapshot(async (snapshot) => {
          global.emitter.emit(`${Collections.OutletSupplyItem}-snapshot`, { snapshot });
        });

      // optimizations
      // subscriberStockTransfer = firestore()
      //   .collection(Collections.StockTransfer)
      //   .where('outletId', '==', currOutletId)
      //   .orderBy('updatedAt', 'desc')
      //   .limit(global.currOutlet.ciNum ? global.currOutlet.ciNum : 20)
      //   .onSnapshot(async (snapshot) => {
      //     global.emitter.emit(`${Collections.StockTransfer}-snapshot`, { snapshot });
      //   });

      subscriberStockTransferProduct = firestore()
        .collection(Collections.StockTransferProduct)
        .where('targetOutletId', '==', currOutletId)
        .orderBy('updatedAt', 'desc')
        .limit(global.currOutlet.iNum ? global.currOutlet.iNum : 20)
        .onSnapshot(async (snapshot) => {
          // console.log(`${Collections.StockTransferProduct} changed!`);

          global.emitter.emit(`${Collections.StockTransferProduct}-snapshot`, { snapshot });
        });

      // optimizations
      // subscriberStockTake = firestore()
      //   .collection(Collections.StockTake)
      //   .where('outletId', '==', currOutletId)
      //   .orderBy('updatedAt', 'desc')
      //   .limit(global.currOutlet.ciNum ? global.currOutlet.ciNum : 20)
      //   .onSnapshot(async (snapshot) => {
      //     global.emitter.emit(`${Collections.StockTake}-snapshot`, { snapshot });
      //   });

      subscriberStockTakeProduct = firestore()
        .collection(Collections.StockTakeProduct)
        .where('outletId', '==', currOutletId)
        .orderBy('updatedAt', 'desc')
        .limit(global.currOutlet.iNum ? global.currOutlet.iNum : 20)
        .onSnapshot(async (snapshot) => {
          global.emitter.emit(`${Collections.StockTakeProduct}-snapshot`, { snapshot });
        });

      subscriberStockReturnProduct = firestore()
        .collection(Collections.StockReturnProduct)
        .where('outletId', '==', currOutletId)
        .orderBy('updatedAt', 'desc')
        .limit(global.currOutlet.iNum ? global.currOutlet.iNum : 20)
        .onSnapshot(async (snapshot) => {
          global.emitter.emit(`${Collections.StockReturnProduct}-snapshot`, { snapshot });
        });

      // subscriberOutletShift = firestore()
      //   .collection(Collections.OutletShift)
      //   .where('outletId', '==', currOutletId)
      //   .orderBy('createdAt', 'desc')
      //   .onSnapshot(async (snapshot) => {
      //     global.emitter.emit(`${Collections.OutletShift}-snapshot`, { snapshot });
      //   });

      // optimizations
      subscriberBeerDocketCategory = firestore()
        .collection(Collections.BeerDocketCategory)
        .where('outletId', '==', currOutletId)
        .onSnapshot((snapshot) => {
          global.emitter.emit(`${Collections.BeerDocketCategory}-snapshot`, { snapshot });
        });

      // optimizations
      subscriberBeerDocket = firestore()
        .collection(Collections.BeerDocket)
        .where('outletId', '==', currOutletId)
        .onSnapshot((snapshot) => {
          global.emitter.emit(`${Collections.BeerDocket}-snapshot`, { snapshot });
        });

      // optimizations
      subscriberUserBeerDocket = firestore()
        .collection(Collections.UserBeerDocket)
        .where('outletId', '==', currOutletId)
        .onSnapshot((snapshot) => {
          global.emitter.emit(`${Collections.UserBeerDocket}-snapshot`, { snapshot });
        });

      // optimizations
      subscriberUserOrderBeerDocket = firestore()
        .collection(Collections.UserOrderBeerDocket)
        .where('outletId', '==', currOutletId)
        .onSnapshot((snapshot) => {
          global.emitter.emit(`${Collections.UserOrderBeerDocket}-snapshot`, { snapshot });
        });

      subscriberTopupCreditType = firestore()
        .collection(Collections.TopupCreditType)
        .where('outletId', '==', currOutletId)
        .onSnapshot((snapshot) => {
          global.emitter.emit(`${Collections.TopupCreditType}-snapshot`, { snapshot });
        });

      subscriberUpsellingCampaign = firestore()
        .collection(Collections.UpsellingCampaign)
        .where('outletId', '==', currOutletId)
        .where('deletedAt', '==', null)
        .onSnapshot((snapshot) => {
          global.emitter.emit(`${Collections.UpsellingCampaign}-snapshot`, { snapshot });
        });

      //loyaltystamptype
      subscriberLoyaltyStampType = firestore()
        .collection(Collections.LoyaltyStampType)
        .where('outletId', '==', currOutletId)
        .onSnapshot((snapshot) => {
          global.emitter.emit(`${Collections.LoyaltyStampType}-snapshot`, { snapshot });
        });
    }, global.firstStartDict[FIRST_START_KEY.TWO] ? 0 : (Platform.OS === 'ios' ? 8000 : 30000));


    // Herks - 2022/05/30 - Try to split two portion of data for user orders list, one for actively monitored for real time changes, another is for cache purpose

    // firestore()
    //   .collection(Collections.UserOrder)
    //   .where('merchantId', '==', merchantId)
    //   // .where('orderStatus', '==', USER_ORDER_STATUS.ORDER_COMPLETED)
    //   // .orderBy('createdAt') // invalid query, need fix this in future
    //   .onSnapshot((snapshot) => {
    //     // console.log('=================================');
    //     // console.log(snapshot);
    //     // console.log('=================================');
    //     // console.log(`${Collections.UserOrder} changed!`);
    //     // console.log(snapshot.size);

    //     if (snapshot && !snapshot.empty) {
    //       var userOrders = [];
    //       var allOutletsUserOrders = [];

    //       for (var i = 0; i < snapshot.size; i++) {
    //         const record = snapshot.docs[i].data();

    //         if (record.combinedOrderList &&
    //           record.combinedOrderList.length > 0) {
    //           // means this order already merged with other orders
    //           // // console.log('GONEEEE',record)
    //         }
    //         else {
    //           userOrders.push(record);
    //         }

    //         // if (record.orderStatus === USER_ORDER_STATUS.ORDER_COMPLETED) {
    //         //   userOrders.push(record);
    //         // }

    //         allOutletsUserOrders.push(record);
    //       }

    //       OutletStore.update((s) => {
    //         s.allOutletsUserOrdersDone = userOrders;
    //         s.allOutletsUserOrders = allOutletsUserOrders;
    //       });
    //     }
    //   });

    // optimizations
    // firestore()
    //   .collection(Collections.PointsRedeemPackage)
    //   .where('merchantId', '==', merchantId)
    //   .onSnapshot((snapshot) => {
    //     // console.log(`${Collections.PointsRedeemPackage} changed!`);

    //     var pointsRedeemPackages = [];
    //     var pointsRedeemPackagesDict = {};

    //     if (snapshot && !snapshot.empty) {
    //       // var selectedOutletItemCategory = {};

    //       for (var i = 0; i < snapshot.size; i++) {
    //         const record = snapshot.docs[i].data();

    //         pointsRedeemPackages.push(record);
    //         pointsRedeemPackagesDict[record.uniqueId] = record;
    //       }

    //       pointsRedeemPackages.sort((a, b) =>
    //         (a.packageName ? a.packageName : '').localeCompare(
    //           b.packageName ? b.packageName : '',
    //         ),
    //       );
    //     }

    //     OutletStore.update((s) => {
    //       s.pointsRedeemPackages = pointsRedeemPackages;
    //       s.pointsRedeemPackagesDict = pointsRedeemPackagesDict;
    //     });
    //   });

    /////////////////////////////////////////

    // optimizations
    subscriberSupplier = firestore()
      .collection(Collections.Supplier)
      .where('outletId', '==', currOutletId)
      .onSnapshot((snapshot) => {
        global.emitter.emit(`${Collections.Supplier}-snapshot`, { snapshot });
      });

    subscriberSupplierProduct = firestore()
      .collection(Collections.SupplierProduct)
      .where('outletId', '==', currOutletId)
      .onSnapshot((snapshot) => {
        global.emitter.emit(`${Collections.SupplierProduct}-snapshot`, { snapshot });
      });

    // optimizations
    subscriberSupplyItem = firestore()
      .collection(Collections.SupplyItem)
      .where('outletId', '==', currOutletId)
      .where('deletedAt', '==', null)
      .onSnapshot(async (snapshot) => {
        global.emitter.emit(`${Collections.SupplyItem}-snapshot`, { snapshot });
      });

    // optimizations
    subscriberOutletSupplyItem = firestore()
      .collection(Collections.OutletSupplyItem)
      .where('outletId', '==', currOutletId)
      .onSnapshot(async (snapshot) => {
        global.emitter.emit(`${Collections.OutletSupplyItem}-snapshot`, { snapshot });
      });

    // optimizations
    // subscriberStockTransfer = firestore()
    //   .collection(Collections.StockTransfer)
    //   .where('outletId', '==', currOutletId)
    //   .orderBy('updatedAt', 'desc')
    //   .limit(global.currOutlet.ciNum ? global.currOutlet.ciNum : 20)
    //   .onSnapshot(async (snapshot) => {
    //     global.emitter.emit(`${Collections.StockTransfer}-snapshot`, { snapshot });
    //   });

    subscriberStockTransferProduct = firestore()
      .collection(Collections.StockTransferProduct)
      .where('targetOutletId', '==', currOutletId)
      .orderBy('updatedAt', 'desc')
      .limit(global.currOutlet.iNum ? global.currOutlet.iNum : 20)
      .onSnapshot(async (snapshot) => {
        // console.log(`${Collections.StockTransferProduct} changed!`);

        global.emitter.emit(`${Collections.StockTransferProduct}-snapshot`, { snapshot });
      });

    // optimizations
    // subscriberStockTake = firestore()
    //   .collection(Collections.StockTake)
    //   .where('outletId', '==', currOutletId)
    //   .orderBy('updatedAt', 'desc')
    //   .limit(global.currOutlet.ciNum ? global.currOutlet.ciNum : 20)
    //   .onSnapshot(async (snapshot) => {
    //     global.emitter.emit(`${Collections.StockTake}-snapshot`, { snapshot });
    //   });

    subscriberStockTakeProduct = firestore()
      .collection(Collections.StockTakeProduct)
      .where('outletId', '==', currOutletId)
      .orderBy('updatedAt', 'desc')
      .limit(global.currOutlet.iNum ? global.currOutlet.iNum : 20)
      .onSnapshot(async (snapshot) => {
        global.emitter.emit(`${Collections.StockTakeProduct}-snapshot`, { snapshot });
      });

    subscriberStockReturnProduct = firestore()
      .collection(Collections.StockReturnProduct)
      .where('outletId', '==', currOutletId)
      .orderBy('updatedAt', 'desc')
      .limit(global.currOutlet.iNum ? global.currOutlet.iNum : 20)
      .onSnapshot(async (snapshot) => {
        global.emitter.emit(`${Collections.StockReturnProduct}-snapshot`, { snapshot });
      });

    // subscriberOutletShift = firestore()
    //   .collection(Collections.OutletShift)
    //   .where('outletId', '==', currOutletId)
    //   .orderBy('createdAt', 'desc')
    //   .onSnapshot(async (snapshot) => {
    //     global.emitter.emit(`${Collections.OutletShift}-snapshot`, { snapshot });
    //   });

    // optimizations
    subscriberBeerDocketCategory = firestore()
      .collection(Collections.BeerDocketCategory)
      .where('outletId', '==', currOutletId)
      .onSnapshot((snapshot) => {
        global.emitter.emit(`${Collections.BeerDocketCategory}-snapshot`, { snapshot });
      });

    // optimizations
    subscriberBeerDocket = firestore()
      .collection(Collections.BeerDocket)
      .where('outletId', '==', currOutletId)
      .onSnapshot((snapshot) => {
        global.emitter.emit(`${Collections.BeerDocket}-snapshot`, { snapshot });
      });

    // optimizations
    subscriberUserBeerDocket = firestore()
      .collection(Collections.UserBeerDocket)
      .where('outletId', '==', currOutletId)
      .onSnapshot((snapshot) => {
        global.emitter.emit(`${Collections.UserBeerDocket}-snapshot`, { snapshot });
      });

    // optimizations
    subscriberUserOrderBeerDocket = firestore()
      .collection(Collections.UserOrderBeerDocket)
      .where('outletId', '==', currOutletId)
      .onSnapshot((snapshot) => {
        global.emitter.emit(`${Collections.UserOrderBeerDocket}-snapshot`, { snapshot });
      });

    subscriberPromotion = firestore()
      .collection(Collections.Promotion)
      .where('outletId', '==', currOutletId)
      .where('deletedAt', '==', null)
      .onSnapshot((snapshot) => {
        global.emitter.emit(`${Collections.Promotion}-snapshot`, { snapshot });
      });

    subscriberLoyaltyCampaign = firestore()
      .collection(Collections.LoyaltyCampaign)
      .where('outletId', '==', currOutletId)
      .where('deletedAt', '==', null)
      .onSnapshot((snapshot) => {
        global.emitter.emit(`${Collections.LoyaltyCampaign}-snapshot`, { snapshot });
      });

    subscriberTaggableVoucher = firestore()
      .collection(Collections.TaggableVoucher)
      .where('outletId', '==', currOutletId)
      .where('deletedAt', '==', null)
      .onSnapshot((snapshot) => {
        global.emitter.emit(`${Collections.TaggableVoucher}-snapshot`, { snapshot });
      });

    ///////////////////////////////////////////

    // 2025-02-12 - for master account's voucher support

    if (!currOutlet.isMasterAccount && global.allOutlets.length === 1) {
      subscriberTaggableVoucherMaster = firestore()
        .collection(Collections.TaggableVoucher)
        .where('merchantId', '==', merchantId)
        .where('isMasterAccount', '==', true)
        .where('deletedAt', '==', null)
        .onSnapshot((snapshot) => {
          global.emitter.emit(`${Collections.TaggableVoucher}-master-snapshot`, { snapshot });
        });
    }
    else {
      OutletStore.update(s => {
        s.taggableVouchersMerchant = [];
      });
    }

    ///////////////////////////////////////////

    // 2024-04-02 - no need first

    // subscriberUserFavoriteMerchantIdList = firestore()
    //   .collection(Collections.User)
    //   .where('favoriteMerchantIdList', 'array-contains', merchantId)
    //   .where('deletedAt', '==', null)
    //   .onSnapshot((snapshot) => {
    //     global.emitter.emit(`${Collections.User}-snapshot-favoriteMerchantIdList`, { snapshot });
    //   });

    // subscriberUserLinkedMerchantIdList = firestore()
    //   .collection(Collections.User)
    //   .where('linkedMerchantIdList', 'array-contains', merchantId)
    //   .where('deletedAt', '==', null)
    //   .onSnapshot((snapshot) => {
    //     global.emitter.emit(`${Collections.User}-snapshot-linkedMerchantIdList`, { snapshot });
    //   });

    ///////////////////////////////////////////

    // need change to more cost-effective
    // firestore()
    //     .collection(Collections.UserFavoriteOutlet)
    //     .where('linkedMerchantIdList', 'array-contains', merchantId)
    //     .where('deletedAt', '==', null)
    //     .onSnapshot(snapshot => {
    //         // console.log(`${Collections.User} changed!`);

    //         var linkedMerchantIdUsers = [];

    //         if (snapshot && !snapshot.empty) {
    //             for (var i = 0; i < snapshot.size; i++) {
    //                 const record = snapshot.docs[i].data();

    //                 linkedMerchantIdUsers.push(record);
    //             }
    //         }

    //         OutletStore.update(s => {
    //             s.linkedMerchantIdUsers = linkedMerchantIdUsers;
    //         });
    //     });

    ///////////////////////////////////////////////

    // 2022-08-15 - Do on outlet basis better

    // firestore()
    //   .collection(Collections.CRMUser)
    //   .where('merchantId', '==', merchantId)
    //   // .where('outletId', '==', outletId)
    //   .where('deletedAt', '==', null)
    //   .onSnapshot((snapshot) => {
    //     // console.log(`${Collections.CRMUser} changed!`);

    //     var crmUsersRaw = [];
    //     //var crmUsersDict = {};

    //     if (snapshot && !snapshot.empty) {
    //       var crmUsersDict = {};

    //       for (var i = 0; i < snapshot.size; i++) {
    //         const record = snapshot.docs[i].data();

    //         crmUsersRaw.push(record);
    //         // crmUsersDict[record.uniqueId] = record;
    //       }
    //     }

    //     OutletStore.update((s) => {
    //       s.crmUsersRaw = crmUsersRaw;
    //       // s.crmUsersDict = crmUsersDict;
    //     });
    //   });

    ///////////////////////////////////////////////

    subscriberOutletPaymentMethod = firestore()
      .collection(Collections.OutletPaymentMethod)
      .where('merchantId', '==', merchantId)
      .onSnapshot(async (snapshot) => {
        global.emitter.emit(`${Collections.OutletPaymentMethod}-snapshot`, { snapshot });
      });

    // optimizations
    subscriberLoyaltyStamp = firestore()
      .collection(Collections.LoyaltyStamp)
      .where('outletId', '==', currOutletId)
      .onSnapshot((snapshot) => {
        global.emitter.emit(`${Collections.LoyaltyStamp}-snapshot`, { snapshot });
      });

    subscriberOutletCatalog = firestore()
      .collection(Collections.OutletCatalog)
      .where('outletId', '==', currOutletId)
      .onSnapshot((snapshot) => {
        global.emitter.emit(`${Collections.OutletCatalog}-snapshot`, { snapshot });
      });

    subscriberWOItem = firestore()
      .collection(Collections.WOItem)
      .where('outletId', '==', currOutletId)
      .where('status', '==', 'PENDING')
      .where('deletedAt', '==', null)
      .orderBy('updatedAt', 'desc')
      .limit(100)
      .onSnapshot((snapshot) => {
        global.emitter.emit(`${Collections.WOItem}-snapshot`, { snapshot });
      });

    subscriberWOList = firestore()
      .collection(Collections.WOList)
      .where('outletId', '==', currOutletId)
      .where('status', '==', 'PENDING')
      .where('deletedAt', '==', null)
      .orderBy('updatedAt', 'desc')
      .limit(100)
      .onSnapshot((snapshot) => {
        global.emitter.emit(`${Collections.WOList}-snapshot`, { snapshot });
      });

    subscriberOSIT = firestore()
      .collection(Collections.OutletSupplyItemTransaction)
      .where('outletId', '==', currOutletId)
      .where('deletedAt', '==', null)
      .onSnapshot((snapshot) => {
        global.emitter.emit(`${Collections.OutletSupplyItemTransaction}-snapshot`, { snapshot });
      });
  }

  subscriberUserOrderMetadata = firestore()
    .collection(Collections.UserOrderMetadataV2)
    .where('outletId', '==', currOutletId)
    .where('pTasks', '>', 0)
    // .where('createdAt', '')
    .orderBy('pTasks')  // Add this
    .orderBy('createdAt', 'desc')
    .limit(global.currOutlet.uomdv2Limit ? global.currOutlet.uomdv2Limit : 100)
    // .where('orderStatus', '==', USER_ORDER_STATUS.ORDER_COMPLETED)
    // .orderBy('createdAt') // invalid query, need fix this in future
    // .where('createdAt', '<', Date.now())
    // .where(
    //   'createdAt',
    //   '>=',
    //   moment().subtract(1, 'day').startOf('day').valueOf(),
    // )
    .onSnapshot((snapshot) => {
      if (snapshot) {
        logToFile(`UserOrderMetadataV2.size: ${snapshot.size}`);

        global.emitter.emit(`${Collections.UserOrderMetadataV2}-snapshot`, { snapshot });
      }
    });

  var subscriberSummary = () => {
    // 2025-05-28 - no need
    // subscriberUserOrder();
    subscriberMerchant();
    subscriberOutletItem();
    subscriberOutletItemCategory();
    subscriberOutletItemAddOn();
    subscriberOutletItemAddOnChoice();
    subscriberOutletOpening();
    subscriberUserAllOutletsEmployees();
    subscriberUserAction();
    subscriberEmployeeClock();
    subscriberUserOrderLoyalty();
    subscriberPreorderPackage();
    subscriberSupplier();
    subscriberSupplierProduct();
    subscriberSupplyItem();
    subscriberOutletSupplyItem();
    subscriberStockTransfer();
    subscriberStockTransferProduct();
    subscriberStockTake();
    subscriberStockTakeProduct();
    subscriberStockReturnProduct();
    subscriberOutletShift();
    subscriberBeerDocketCategory();
    subscriberBeerDocket();
    subscriberUserBeerDocket();
    subscriberUserOrderBeerDocket();
    subscriberPromotion();
    subscriberLoyaltyCampaign();
    subscriberUpsellingCampaign();
    subscriberTaggableVoucher();
    subscriberTaggableVoucherMaster();
    subscriberUserFavoriteMerchantIdList();
    subscriberUserLinkedMerchantIdList();
    subscriberCRMUserTag();
    subscriberCRMSegment();
    subscriberOutletPaymentMethod();
    subscriberLoyaltyStamp();
    subscriberLoyaltyStampType();
    subscriberTopupCreditType();
    subscriberUserOrderMetadata();
    subscriberOutletCatalog();
    subscriberWOItem();
    subscriberWOList();
    subscriberOSIT();
  };

  return subscriberSummary;
};

export const listenToCurrOutletIdChangesMerchant = (
  merchantId,
  currOutletId,
  isMasterAccount,
  firebaseUid = '',
  individualShift = false,
) => {
  // Get outlets info

  var subscriberOutlet = firestore()
    .collection(Collections.Outlet)
    .where('merchantId', '==', merchantId)
    .onSnapshot(async (snapshot) => {
      // global.emitter.emit(`${Collections.Outlet}-snapshot`, { snapshot: snapshot, currOutletId: currOutletId });

      global.isSnapshotChanging = true;

      // console.log(`${Collections.Outlet} changed!`);

      if (snapshot && !snapshot.empty) {
        var outlets = [];
        var outletsDict = {};

        for (var i = 0; i < snapshot.size; i++) {
          const record = snapshot.docs[i].data();

          // if (record.uniqueId === currOutletId || isMasterAccount) {
          //   outlets.push(record);
          //   outletsDict[record.uniqueId] = record;
          // }

          outlets.push(record);
          outletsDict[record.uniqueId] = record;
        }

        global.allOutlets = outlets;

        MerchantStore.update((s) => {
          s.allOutlets = outlets;
          s.allOutletsDict = outletsDict;
        });

        if (currOutletId !== '') {
          var currOutletIdTemp = '';
          var currOutletTemp = {
            uniqueId: '',
            privileges: [],
          };

          if (outlets.length > 0) {
            // var firstOutlet = null;
            // var oldestDate = moment();

            // for (var i = 0; i < outlets.length; i++) {
            //     if (moment(outlets[i].createdAt).isBefore(oldestDate)) {
            //         oldestDate = outlets[i].createdAt;
            //         firstOutlet = outlets[i];
            //     }
            // }

            // currOutletIdTemp = outlets.uniqueId;
            currOutletTemp = outlets.find(
              (outlet) => outlet.uniqueId === currOutletId,
            );
          }

          if (currOutletTemp && currOutletTemp.uniqueId) {
            MerchantStore.update((s) => {
              // s.currOutletId = currOutletIdTemp;
              s.currOutlet = currOutletTemp;

              // s.isMasterAccount = currOutletTemp.isMasterAccount !== undefined ? currOutletTemp.isMasterAccount : true;
            });

            global.outletKdEventTypes = currOutletTemp.kdPrintEventTypes !== undefined ? currOutletTemp.kdPrintEventTypes : [
              KD_PRINT_EVENT_TYPE.DELIVER,
              KD_PRINT_EVENT_TYPE.REJECT,
              KD_PRINT_EVENT_TYPE.UNDO_DELIVER,
              KD_PRINT_EVENT_TYPE.UNDO_REJECT,
              KD_PRINT_EVENT_TYPE.SWITCH_TABLE,
            ];

            global.outletKdVariation = currOutletTemp.kdPrintVariation !== undefined ? currOutletTemp.kdPrintVariation : KD_PRINT_VARIATION.SUMMARY;

            global.outletKdFontSize = currOutletTemp.kdFontSize !== undefined ? currOutletTemp.kdFontSize : KD_FONT_SIZE.NORMAL;

            global.outletKdHeaderFontSize = currOutletTemp.kdHeaderFontSize !== undefined ? currOutletTemp.kdHeaderFontSize : KD_FONT_SIZE.EXTRA_LARGE;

            global.outletKdPrintSku = currOutletTemp.isKdPrintSku !== undefined ? currOutletTemp.isKdPrintSku : false;

            global.outletToPrintVariantAddonTitle = currOutletTemp.toPrintVariantAddonTitle !== undefined ? currOutletTemp.toPrintVariantAddonTitle : true;

            global.outletKdPrintUserInfo = currOutletTemp.isKdPrintUserInfo !== undefined ? currOutletTemp.isKdPrintUserInfo : false;

            global.outletPrintReceiptWhenPaidOnline = currOutletTemp.printReceiptWhenPaidOnline !== undefined ? currOutletTemp.printReceiptWhenPaidOnline : false;

            global.outletAutoPrintPaySlip = currOutletTemp.autoPrintPaySlip !== undefined ? currOutletTemp.autoPrintPaySlip : true;

            global.outletShortenShiftReport = currOutletTemp.shortenShiftReport !== undefined ? currOutletTemp.shortenShiftReport : false;

            global.outletShortenReceipt = currOutletTemp.shortenReceipt !== undefined ? currOutletTemp.shortenReceipt : false;

            global.pKdLogo = currOutletTemp.pKdLogo !== undefined ? currOutletTemp.pKdLogo : false;
            global.pMLogo = currOutletTemp.pMLogo !== undefined ? currOutletTemp.pMLogo : true;

            global.scl = currOutletTemp.scl !== undefined ? currOutletTemp.scl : false; // 2024-08-08 - support code logging, default is false

            // global.outletToggleDisableAutoPrint = currOutletTemp.toggleDisableAutoPrint ? currOutletTemp.toggleDisableAutoPrint : false;

            await updateOutletAsyncStorage(currOutletTemp);

            if (currOutletTemp.toggleOfflineMode !== global.outletToggleOfflineMode) {
              if (currOutletTemp.toggleOfflineMode) {
                // await database().goOffline();
                await database()
                  .setPersistenceEnabled(true)
                  .then(() => database().goOffline());
              }
              else {
                // await database().goOnline();

                await database()
                  .setPersistenceEnabled(true)
                  .then(() => database().goOnline());
              }
            }

            global.outletToggleOfflineMode = currOutletTemp.toggleOfflineMode !== undefined ? currOutletTemp.toggleOfflineMode : false;

            global.outletToggleOpenOrder = currOutletTemp.toggleOpenOrder !== undefined ? currOutletTemp.toggleOpenOrder : false;

            global.uanI = (currOutletTemp.uanI > 0) ? currOutletTemp.uanI : 0.2;

            // global.outletSelectedCustomerInfoWaitTime = currOutletTemp.sciwt !== undefined ? currOutletTemp.sciwt : 3;

            /////////////////////////////////////////////////////////////

            global.rcL = currOutletTemp.rcL !== undefined ? currOutletTemp.rcL : true;
            global.rcMnP = currOutletTemp.rcMnP !== undefined ? currOutletTemp.rcMnP : true;
            global.rcA = currOutletTemp.rcA !== undefined ? currOutletTemp.rcA : true;
            global.rcMn = currOutletTemp.rcMn !== undefined ? currOutletTemp.rcMn : true;
            global.rcOi = currOutletTemp.rcOi !== undefined ? currOutletTemp.rcOi : true;
            global.rcCnP = currOutletTemp.rcCnP !== undefined ? currOutletTemp.rcCnP : true;
            global.rcOd = currOutletTemp.rcOd !== undefined ? currOutletTemp.rcOd : true;
            global.rcCd = currOutletTemp.rcCd !== undefined ? currOutletTemp.rcCd : true;
            global.rcSd = currOutletTemp.rcSd !== undefined ? currOutletTemp.rcSd : true;
            global.rcR = currOutletTemp.rcR !== undefined ? currOutletTemp.rcR : true;
            global.rcT = currOutletTemp.rcT !== undefined ? currOutletTemp.rcT : true;
            global.rcC = currOutletTemp.rcC !== undefined ? currOutletTemp.rcC : true;
            global.rcTyo = currOutletTemp.rcTyo !== undefined ? currOutletTemp.rcTyo : true;
            global.rcPbKd = currOutletTemp.rcPbKd !== undefined ? currOutletTemp.rcPbKd : true;
            global.hRm = currOutletTemp.hRm !== undefined ? currOutletTemp.hRm : false;
            global.pkdT = currOutletTemp.pkdT !== undefined ? currOutletTemp.pkdT : true;
            /////////////////////////////////////////////////////////////
          }
        }
      }

      global.isSnapshotChanging = false;
    });

  var subscriberOutletShift = () => { };

  if (individualShift) {
    subscriberOutletShift = firestore()
      .collection(Collections.OutletShift)
      .where('outletId', '==', currOutletId)
      .where('userIdOpen', '==', firebaseUid)
      .orderBy('createdAt', 'desc')
      .limit(1)
      .onSnapshot(async (snapshot) => {
        global.emitter.emit(`${Collections.OutletShift}-snapshot-currOutletShift`, { snapshot });
      });
  }
  else {
    subscriberOutletShift = firestore()
      .collection(Collections.OutletShift)
      .where('outletId', '==', currOutletId)
      .orderBy('createdAt', 'desc')
      .limit(1)
      .onSnapshot(async (snapshot) => {
        global.emitter.emit(`${Collections.OutletShift}-snapshot-currOutletShift`, { snapshot });
      });
  }

  var subscriberCRMUser = () => { };
  var subscriberOutletSupplyItem = () => { };
  var subscriberPurchaseOrderProduct = () => { };
  // var subscriberOutletShift = () => { };
  var subscriberLoyaltyTier = () => { };
  var subscriberUserTaggableVoucher = () => { };
  var subscriberRecommendedLoyalty = () => { };

  setTimeout(() => {
    global.firstStartDict[FIRST_START_KEY.THREE] = true;

    subscriberCRMUser = firestore()
      .collection(Collections.CRMUser)
      .where('outletId', '==', currOutletId)
      // .where('merchantId', '==', merchantId)    
      .where('deletedAt', '==', null)
      .orderBy('updatedAt', 'desc')
      .limit(500) // put limit first
      .onSnapshot((snapshot) => {
        global.emitter.emit(`${Collections.CRMUser}-snapshot`, { snapshot });
      });
  }, global.firstStartDict[FIRST_START_KEY.THREE] ? 0 : (Platform.OS === 'ios' ? 5000 : 10000));

  setTimeout(() => {
    global.firstStartDict[FIRST_START_KEY.FOUR] = true;

    // optimizations
    subscriberOutletSupplyItem = firestore()
      .collection(Collections.OutletSupplyItem)
      .where('outletId', '==', currOutletId)
      .onSnapshot(async (snapshot) => {
        global.emitter.emit(`${Collections.OutletSupplyItem}-snapshot-outletSupplyItems`, { snapshot });
      });

    // optimizations
    // var subscriberPurchaseOrder = firestore()
    //   .collection(Collections.PurchaseOrder)
    //   .where('outletId', '==', currOutletId)
    //   .orderBy('updatedAt', 'desc')
    //   .limit(global.currOutlet.ciNum ? global.currOutlet.ciNum : 20)
    //   .onSnapshot(async (snapshot) => {
    //     global.emitter.emit(`${Collections.PurchaseOrder}-snapshot`, { snapshot });
    //   });

    subscriberPurchaseOrderProduct = firestore()
      .collection(Collections.PurchaseOrderProduct)
      .where('outletId', '==', currOutletId)
      .orderBy('updatedAt', 'desc')
      .limit(global.currOutlet.iNum ? global.currOutlet.iNum : 20)
      .onSnapshot(async (snapshot) => {
        global.emitter.emit(`${Collections.PurchaseOrderProduct}-snapshot`, { snapshot });
      });

    subscriberLoyaltyTier = firestore()
      .collection(Collections.LoyaltyTier)
      .where('outletId', '==', currOutletId)
      .onSnapshot(async (snapshot) => {
        global.emitter.emit(`${Collections.LoyaltyTier}-snapshot`, { snapshot });
      });

    subscriberUserTaggableVoucher = firestore()
      .collection(Collections.UserTaggableVoucher)
      // .where('outletIdList', 'array-contains', outlet.uniqueId)
      .where('outletId', '==', currOutletId)
      // .where('deletedAt', '==', null)
      .limit(100)
      .onSnapshot((snapshot) => {
        global.emitter.emit(`${Collections.UserTaggableVoucher}-snapshot`, { snapshot });
      });

    subscriberRecommendedLoyalty = firestore()
      .collection(Collections.RecommendedLoyalty)
      .where('outletId', '==', currOutletId)
      .orderBy('updatedAt', 'desc')
      .limit(1)
      .onSnapshot(async (snapshot) => {
        global.emitter.emit(`${Collections.RecommendedLoyalty}-snapshot`, { snapshot });
      });
  }, global.firstStartDict[FIRST_START_KEY.FOUR] ? 0 : (Platform.OS === 'ios' ? 8000 : 10000));

  var subscriberOutletPrinter = firestore()
    .collection(Collections.OutletPrinter)
    .where('outletId', '==', currOutletId)
    .onSnapshot(async (snapshot) => {
      global.emitter.emit(`${Collections.OutletPrinter}-snapshot`, { snapshot });
    });


  // var subscriberCRMUserNoLimit = firestore()
  //   .collection(Collections.CRMUser)
  //   .where('outletId', '==', currOutletId)
  //   .where('deletedAt', '==', null)
  //   .orderBy('updatedAt', 'desc')
  //   .onSnapshot((snapshot) => {
  //     global.emitter.emit(`${Collections.CRMUser}-snapshot-noLimit`, { snapshot });
  //   });
  // 2024-10-18 - not used
  // var subscriberOutletOrderNumber = firestore()
  //   .collection(Collections.OutletOrderNumber)
  //   .where('outletId', '==', currOutletId)
  //   .onSnapshot(async (snapshot) => {
  //     global.emitter.emit(`${Collections.OutletOrderNumber}-snapshot`, { snapshot });
  //   });

  // firestore()
  //   .collection(Collections.CRMUserTag)
  //   .where('outletId', '==', currOutletId)
  //   // .where('merchantId', '==', merchantId)
  //   // .where('deletedAt', '==', null)
  //   .onSnapshot((snapshot) => {
  //     // console.log(`${Collections.CRMUserTag} changed!`);

  //     var crmUserTags = [];
  //     var crmUserTagsDict = {};

  //     if (snapshot && !snapshot.empty) {
  //       for (var i = 0; i < snapshot.size; i++) {
  //         const record = snapshot.docs[i].data();

  //         crmUserTags.push(record);
  //         crmUserTagsDict[record.uniqueId] = record;
  //       }
  //     }

  //     crmUserTags.sort((a, b) => a.name.localeCompare(b.name));

  //     OutletStore.update((s) => {
  //       s.crmUserTags = crmUserTags;
  //       s.crmUserTagsDict = crmUserTagsDict;
  //     });
  //   });

  // firestore()
  //   .collection(Collections.CRMSegment)
  //   .where('outletId', '==', currOutletId)
  //   // .where('merchantId', '==', merchantId)
  //   // .where('deletedAt', '==', null)
  //   .onSnapshot((snapshot) => {
  //     // console.log(`${Collections.CRMSegment} changed!`);

  //     var crmSegments = [];
  //     var crmSegmentsDict = {};

  //     if (snapshot && !snapshot.empty) {
  //       for (var i = 0; i < snapshot.size; i++) {
  //         const record = snapshot.docs[i].data();

  //         crmSegments.push(record);
  //         crmSegmentsDict[record.uniqueId] = record;
  //       }
  //     }

  //     crmSegments.sort((a, b) => a.name.localeCompare(b.name));

  //     OutletStore.update((s) => {
  //       s.crmSegments = crmSegments;
  //       s.crmSegmentsDict = crmSegmentsDict;
  //     });
  //   });

  // optimizations
  // firestore()
  //   .collection(Collections.OutletTax)
  //   .where('merchantId', '==', merchantId)
  //   .onSnapshot((snapshot) => {
  //     // console.log(`${Collections.OutletTax} changed!`);

  //     var outletsTaxDict = {};
  //     var currOutletTaxes = [];

  //     if (snapshot && !snapshot.empty) {
  //       for (var i = 0; i < snapshot.size; i++) {
  //         const record = snapshot.docs[i].data();

  //         outletsTaxDict[record.outletId] = record;

  //         if (record.outletId === currOutletId) {
  //           currOutletTaxes.push(record);
  //         }
  //       }
  //     }

  //     CommonStore.update((s) => {
  //       s.outletsTaxDict = outletsTaxDict;
  //       s.currOutletTaxes = currOutletTaxes;
  //     });
  //   });

  // firestore()
  //   .collection(Collections.OutletItem)
  //   .where('merchantId', '==', merchantId)
  //   .onSnapshot((snapshot) => {
  //     // console.log(`${Collections.OutletItem} changed!`);

  //     var outletItems = [];
  //     var outletItemsDict = {};
  //     var outletItemsSkuDict = {};

  //     var allOutletsItems = [];
  //     var allOutletsItemsSkuDict = {};

  //     if (snapshot && !snapshot.empty) {
  //       for (var i = 0; i < snapshot.size; i++) {
  //         const record = snapshot.docs[i].data();

  //         if (record.outletId === currOutletId) {
  //           outletItems.push(record);
  //           outletItemsDict[record.uniqueId] = record;
  //           outletItemsSkuDict[record.sku] = record;
  //         }

  //         allOutletsItems.push(record);

  //         if (allOutletsItemsSkuDict[record.sku]) {
  //           allOutletsItemsSkuDict[record.sku].push(record);
  //         } else {
  //           allOutletsItemsSkuDict[record.sku] = [record];
  //         }
  //       }
  //     }

  //     outletItems.sort((a, b) => a.name.localeCompare(b.name));

  //     if (
  //       currOutletId
  //       // true
  //     ) {
  //       OutletStore.update((s) => {
  //         s.outletItems = outletItems;
  //         s.outletItemsDict = outletItemsDict;
  //         s.outletItemsSkuDict = outletItemsSkuDict;

  //         s.allOutletsItems = allOutletsItems;

  //         s.allOutletsItemsSkuDict = allOutletsItemsSkuDict;
  //       });
  //     }
  //   });

  // firestore()
  //   .collection(Collections.OutletItemCategory)
  //   .where('merchantId', '==', merchantId)
  //   .onSnapshot((snapshot) => {
  //     // console.log(`${Collections.OutletItemCategory} changed!`);

  //     var outletCategories = [];
  //     var outletCategoriesDict = {};

  //     var allOutletsCategories = [];
  //     var allOutletsCategoriesNameDict = {};
  //     var allOutletsCategoriesDict = {};

  //     var allOutletsCategoriesUnique = [];

  //     if (snapshot && !snapshot.empty) {
  //       // var selectedOutletItemCategory = {};

  //       for (var i = 0; i < snapshot.size; i++) {
  //         const record = snapshot.docs[i].data();

  //         if (record.outletId === currOutletId) {
  //           outletCategories.push(record);
  //           outletCategoriesDict[record.uniqueId] = record;
  //         }

  //         allOutletsCategories.push(record);

  //         if (allOutletsCategoriesNameDict[record.name]) {
  //           allOutletsCategoriesNameDict[record.name].push(record);
  //         } else {
  //           allOutletsCategoriesNameDict[record.name] = [record];
  //         }

  //         if (allOutletsCategoriesDict[record.uniqueId]) {
  //           allOutletsCategoriesDict[record.uniqueId] = record;
  //         } else {
  //           allOutletsCategoriesDict[record.uniqueId] = record;
  //         }

  //         var isExisted = false;

  //         for (var j = 0; j < allOutletsCategoriesUnique.length; j++) {
  //           if (allOutletsCategoriesUnique[j].name === record.name) {
  //             isExisted = true;
  //             break;
  //           }
  //         }

  //         if (!isExisted) {
  //           allOutletsCategoriesUnique.push(record);
  //         }
  //       }

  //       allOutletsCategoriesUnique.sort((a, b) => a.name.localeCompare(b.name));

  //       OutletStore.update((s) => {
  //         s.outletCategories = outletCategories;
  //         s.outletCategoriesDict = outletCategoriesDict;

  //         s.allOutletsCategories = allOutletsCategories;
  //         s.allOutletsCategoriesNameDict = allOutletsCategoriesNameDict;
  //         s.allOutletsCategoriesDict = allOutletsCategoriesDict;

  //         s.allOutletsCategoriesUnique = allOutletsCategoriesUnique;
  //       });

  //       CommonStore.update((s) => {
  //         s.selectedOutletItemCategory = outletCategories[0];
  //       });
  //     }
  //   });

  return () => {
    subscriberOutlet();
    subscriberOutletSupplyItem();
    // subscriberPurchaseOrder();
    subscriberPurchaseOrderProduct();
    subscriberOutletShift();
    subscriberOutletPrinter();
    subscriberLoyaltyTier();
    // subscriberOutletOrderNumber();
    subscriberUserTaggableVoucher();
    subscriberCRMUser();
    subscriberRecommendedLoyalty();
    // subscriberCRMUserNoLimit();
  };
};

export const listenToAllOutletsChangesMerchant = async (allOutlets) => {
  // var batchIndex = 0;
  // var batchLength = 10;
  // var promisesOutletTax = [];
  // var promisesUserOrder = [];
  // var batchOutletIdList = allOutlets.slice(batchIndex, batchLength).map(outlet => outlet.uniqueId);
  // while (batchOutletIdList.length > 0) {
  //     // console.log(batchOutletIdList);
  //     promisesOutletTax.push(new Promise(async (resolve, reject) => {
  //         firestore()
  //             .collection(Collections.OutletTax)
  //             .where('outletId', 'in', batchOutletIdList)
  //             .onSnapshot(snapshot => {
  //                 // console.log(`${Collections.OutletTax} changed!`);
  //                 if (snapshot && !snapshot.empty) {
  //                     var outletsTaxDict = {};
  //                     for (var i = 0; i < snapshot.size; i++) {
  //                         const record = snapshot.docs[i].data();
  //                         outletsTaxDict[record.outletId] = record;
  //                     }
  //                     resolve(outletsTaxDict);
  //                 }
  //                 else {
  //                     reject({});
  //                 }
  //             });
  //     }));
  //     promisesUserOrder.push(new Promise(async (resolve, reject) => {
  //         firestore()
  //             .collection(Collections.UserOrder)
  //             .where('outletId', 'in', batchOutletIdList)
  //             .where('orderStatus', '==', USER_ORDER_STATUS.ORDER_COMPLETED)
  //             // .orderBy('createdAt') // invalid query, need fix this in future
  //             .onSnapshot(snapshot => {
  //                 // console.log(`${Collections.UserOrder} changed!`);
  //                 if (snapshot && !snapshot.empty) {
  //                     var userOrders = [];
  //                     for (var i = 0; i < snapshot.size; i++) {
  //                         const record = snapshot.docs[i].data();
  //                         userOrders.push(record);
  //                     }
  //                     resolve(userOrders);
  //                 }
  //                 else {
  //                     reject([]);
  //                 }
  //             });
  //     }));
  //     batchIndex++;
  //     batchOutletIdList = allOutlets.slice(batchIndex * batchLength, (batchIndex * batchLength) + batchLength).map(outlet => outlet.uniqueId);
  // }
  // await Promise.all(promisesOutletTax).then(result => {
  //     // console.log('promise outlet tax');
  //     // console.log(result);
  //     if (result) {
  //         CommonStore.update(s => {
  //             s.outletsTaxDict = result.reduce((objDict, obj) => {
  //                 for (var key in obj) {
  //                     objDict[key] = obj[key];
  //                 }
  //                 return objDict;
  //             });
  //         });
  //     }
  // });
  // await Promise.all(promisesUserOrder).then(result => {
  //     // console.log('promise user order');
  //     // console.log(result);
  //     if (result) {
  //         OutletStore.update(s => {
  //             s.allOutletsUserOrdersDone = result.reduce((allArr, arr) => allArr.concat(arr));
  //         });
  //     }
  // });
};

export const listenToSelectedCustomerApplicableVoucherIdChangesMerchant = async (customer, voucherIdList) => {
  try {
    console.log('listenToSelectedCustomerApplicableVoucherIdChangesMerchant (v3) func (v2)');

    var subscriberUserTaggableVoucherList = [];

    if (customer.email && customer.outletId) {
      console.log(`listenToSelectedCustomerApplicableVoucherIdChangesMerchant (v3) > 1 (v2)`);

      CommonStore.update(s => {
        s.isLoadingApplicableVoucher = true;
      });

      var batchIndex = 0;
      var batchLength = 10;

      let promisesUserTaggableVoucher = [];

      var batchVoucherIdList = voucherIdList.slice(batchIndex, batchLength);

      console.log(`batchVoucherIdList`);
      console.log(batchVoucherIdList);

      while (batchVoucherIdList.length > 0) {
        promisesUserTaggableVoucher.push(new Promise(async (resolve, reject) => {
          console.log(`listenToSelectedCustomerApplicableVoucherIdChangesMerchant (v3) > promise (v2)`);
          console.log(customer.outletId);
          console.log(customer.email);
          console.log(customer.merchantId);
          console.log(customer.number);
          console.log(batchVoucherIdList);
          console.log(moment().valueOf());

          if (customer && customer.merchantId && customer.number) {
            console.log(`listenToSelectedCustomerApplicableVoucherIdChangesMerchant (v3) > enter subscriber)`);

            let subscriberUserTaggableVoucher = await firestore()
              .collection(Collections.UserTaggableVoucher)
              // .where('outletId', '==', customer.outletId)
              // .where('email', '==', customer.email)
              // .where('redeemDate', '==', null)
              // .where('voucherId', 'in', batchVoucherIdList)
              // .where('expirationDate', '>', moment().valueOf())
              // .orderBy('expirationDate')
              // 2025-02-12 - use query from web order
              .where('merchantId', '==', customer.merchantId)
              .where('phone', '==', customer.number)
              .where('redeemDate', '==', null)
              .where('voucherId', 'in', batchVoucherIdList)
              // .where('expirationDate', '>', moment().subtract(6, 'months').valueOf()) // 2025
              .where('expirationDate', '>', moment().add(-1, 'day').endOf('day').valueOf())
              .where('expirationDate', '<', moment().add(6, 'months').valueOf())
              .orderBy('expirationDate', 'desc')
              // .where('voucherId', '==', batchVoucherIdList[0])
              .limit(20)
              // .where('orderStatus', '==', USER_ORDER_STATUS.ORDER_COMPLETED)
              // .orderBy('createdAt') // invalid query, need fix this in future
              .onSnapshot(snapshot => {
                global.isSnapshotChanging = true;

                console.log(`listenToSelectedCustomerApplicableVoucherIdChangesMerchant (v3) > snapshot (v2)`);

                // console.log(`${Collections.UserOrder} changed!`);
                if (snapshot && !snapshot.empty) {
                  var userOrders = [];
                  for (var i = 0; i < snapshot.size; i++) {
                    const record = snapshot.docs[i].data();

                    if (record.outletId === global.currOutlet.uniqueId ||
                      record.isMasterAccount
                    ) {
                      userOrders.push(record);
                    }
                  }

                  console.log(`listenToSelectedCustomerApplicableVoucherIdChangesMerchant (v3) > got data (v2)`);
                  console.log(userOrders);

                  resolve(userOrders);
                }
                else {
                  // reject([]);

                  console.log(`listenToSelectedCustomerApplicableVoucherIdChangesMerchant (v3) > empty (v2)`);

                  resolve([]);
                }

                global.isSnapshotChanging = false;
              });

            subscriberUserTaggableVoucherList.push(subscriberUserTaggableVoucher);
          }
          else {
            console.log(`listenToSelectedCustomerApplicableVoucherIdChangesMerchant (v3) > skip subscriber`);

            // resolve([]);
          }
        }));
        batchIndex++;
        batchVoucherIdList = voucherIdList.slice(batchIndex * batchLength, (batchIndex * batchLength) + batchLength);
      }

      setTimeout(() => {
        CommonStore.update(s => {
          s.isLoadingApplicableVoucher = false;
        });
      }, 8000);

      await Promise.all(promisesUserTaggableVoucher).then(result => {
        // console.log('promise user order');
        // console.log(result);

        console.log(`listenToSelectedCustomerApplicableVoucherIdChangesMerchant (v3) > result (v2)`);

        if (result) {
          OutletStore.update(s => {
            s.selectedCustomerUserTaggableVouchers = result.reduce((allArr, arr) => allArr.concat(arr));
          });
        }

        CommonStore.update(s => {
          s.isLoadingApplicableVoucher = false;
        });
      }).catch(ex => {
        console.log(`listenToSelectedCustomerApplicableVoucherIdChangesMerchant (v3) ex inner (v2)`);

        console.log(ex);

        CommonStore.update(s => {
          s.isLoadingApplicableVoucher = false;
        });
      });
    }

    return () => {
      console.log(`listenToSelectedCustomerApplicableVoucherIdChangesMerchant (v3) callback (v2)`);

      for (let i = 0; i < subscriberUserTaggableVoucherList.length; i++) {
        subscriberUserTaggableVoucherList[i]();
      }
    }
  }
  catch (ex) {
    console.log(`listenToSelectedCustomerApplicableVoucherIdChangesMerchant (v3) ex (v2)`);
    console.log(ex);

    CommonStore.update(s => {
      s.isLoadingApplicableVoucher = false;
    });
  }
};

// 2025-03-03 - improved version
// export const listenToSelectedCustomerApplicableVoucherIdChangesMerchant = async (customer, voucherIdList) => {
//   console.log('listenToSelectedCustomerApplicableVoucherIdChangesMerchant (v3) func (v2)');

//   // Array to store all snapshot unsubscribe functions
//   let subscriberUserTaggableVoucherList = [];

//   try {
//     // Validate required customer data early
//     if (!customer?.email || !customer?.outletId || !customer?.merchantId || !customer?.number) {
//       console.log('Missing required customer data', { 
//         email: !!customer?.email, 
//         outletId: !!customer?.outletId,
//         merchantId: !!customer?.merchantId,
//         number: !!customer?.number
//       });
//       return () => {}; // Return empty cleanup function
//     }

//     console.log(`listenToSelectedCustomerApplicableVoucherIdChangesMerchant (v3) > 1 (v2)`);

//     CommonStore.update(s => {
//       s.isLoadingApplicableVoucher = true;
//     });

//     // Batch processing of voucher IDs
//     const batchLength = 10;
//     const batches = [];

//     // Create batches of voucher IDs
//     for (let i = 0; i < voucherIdList.length; i += batchLength) {
//       batches.push(voucherIdList.slice(i, i + batchLength));
//     }

//     console.log(`Created ${batches.length} batches of voucherIds`);

//     // Create a promise for each batch
//     const promisesUserTaggableVoucher = batches.map((batchVoucherIds, index) => {
//       return new Promise((resolve, reject) => {
//         console.log(`Processing batch ${index + 1}/${batches.length}`);
//         console.log(`Batch data:`, {
//           merchantId: customer.merchantId,
//           phone: customer.number,
//           outletId: customer.outletId,
//           voucherIds: batchVoucherIds,
//           timestamp: moment().valueOf()
//         });

//         try {
//           console.log(`listenToSelectedCustomerApplicableVoucherIdChangesMerchant (v3) > enter subscriber for batch ${index + 1}`);

//           // Make sure we have valid voucher IDs in the batch
//           if (!batchVoucherIds.length) {
//             console.log(`Empty batch, resolving with empty array`);
//             resolve([]);
//             return;
//           }

//           // Set a timeout to handle hanging promises
//           const timeoutId = setTimeout(() => {
//             console.log(`Query timeout for batch ${index + 1} - resolving with empty array`);
//             resolve([]);
//           }, 10000); // 10 second timeout

//           // Create Firestore query
//           const unsubscribe = firestore()
//             .collection(Collections.UserTaggableVoucher)
//             .where('merchantId', '==', customer.merchantId)
//             .where('phone', '==', customer.number)
//             .where('redeemDate', '==', null)
//             .where('voucherId', 'in', batchVoucherIds)
//             .where('expirationDate', '>', moment().valueOf())
//             .orderBy('expirationDate')
//             .limit(20)
//             .onSnapshot(
//               // Success handler
//               snapshot => {
//                 global.isSnapshotChanging = true;
//                 clearTimeout(timeoutId); // Clear the timeout since we got a response

//                 console.log(`listenToSelectedCustomerApplicableVoucherIdChangesMerchant (v3) > snapshot for batch ${index + 1}`);

//                 if (snapshot && !snapshot.empty) {
//                   const userOrders = [];
//                   for (let i = 0; i < snapshot.size; i++) {
//                     const record = snapshot.docs[i].data();

//                     if (record.outletId === global.currOutlet.uniqueId || record.isMasterAccount) {
//                       userOrders.push(record);
//                     }
//                   }

//                   console.log(`listenToSelectedCustomerApplicableVoucherIdChangesMerchant (v3) > got data (v2)`);
//                   console.log(`Found ${userOrders.length} matching orders in batch ${index + 1}`);

//                   resolve(userOrders);
//                 } else {
//                   console.log(`listenToSelectedCustomerApplicableVoucherIdChangesMerchant (v3) > empty for batch ${index + 1}`);
//                   resolve([]);
//                 }

//                 global.isSnapshotChanging = false;
//               },
//               // Error handler
//               error => {
//                 console.error(`Firestore query error for batch ${index + 1}:`, error);
//                 clearTimeout(timeoutId);
//                 resolve([]); // Resolve with empty array instead of rejecting to keep other batches processing
//               }
//             );

//           // Add unsubscribe function to the list
//           subscriberUserTaggableVoucherList.push(unsubscribe);
//         } catch (error) {
//           console.error(`Error setting up listener for batch ${index + 1}:`, error);
//           resolve([]);
//         }
//       });
//     });

//     // Set a timeout to clear loading state if queries take too long
//     const loadingTimeoutId = setTimeout(() => {
//       console.log('Loading timeout reached - setting isLoadingApplicableVoucher to false');
//       CommonStore.update(s => {
//         s.isLoadingApplicableVoucher = false;
//       });
//     }, 15000); // 15 seconds timeout

//     try {
//       // Wait for all promises to complete
//       const results = await Promise.all(promisesUserTaggableVoucher);
//       clearTimeout(loadingTimeoutId);

//       console.log(`listenToSelectedCustomerApplicableVoucherIdChangesMerchant (v3) > all results received`);

//       if (results && results.length > 0) {
//         // Flatten the array of arrays
//         const flattenedResults = results.flat();
//         console.log(`Total taggable vouchers found: ${flattenedResults.length}`);

//         OutletStore.update(s => {
//           s.selectedCustomerUserTaggableVouchers = flattenedResults;
//         });
//       } else {
//         console.log('No taggable vouchers found');
//         OutletStore.update(s => {
//           s.selectedCustomerUserTaggableVouchers = [];
//         });
//       }
//     } catch (error) {
//       console.error('Error processing voucher batches:', error);
//     } finally {
//       // Ensure loading state is cleared
//       CommonStore.update(s => {
//         s.isLoadingApplicableVoucher = false;
//       });
//     }

//     // Return cleanup function
//     return () => {
//       console.log(`listenToSelectedCustomerApplicableVoucherIdChangesMerchant (v3) callback (v2)`);
//       console.log(`Unsubscribing from ${subscriberUserTaggableVoucherList.length} Firestore listeners`);

//       // Clean up all Firestore listeners
//       for (let i = 0; i < subscriberUserTaggableVoucherList.length; i++) {
//         try {
//           subscriberUserTaggableVoucherList[i]();
//         } catch (error) {
//           console.error(`Error unsubscribing from listener ${i}:`, error);
//         }
//       }
//     };
//   } catch (ex) {
//     console.error(`listenToSelectedCustomerApplicableVoucherIdChangesMerchant (v3) ex (v2):`, ex);

//     CommonStore.update(s => {
//       s.isLoadingApplicableVoucher = false;
//     });

//     // Return cleanup function for any listeners that might have been created
//     return () => {
//       console.log(`Cleanup after error - unsubscribing from ${subscriberUserTaggableVoucherList.length} listeners`);
//       for (let i = 0; i < subscriberUserTaggableVoucherList.length; i++) {
//         try {
//           subscriberUserTaggableVoucherList[i]();
//         } catch (error) {
//           console.error(`Error unsubscribing from listener ${i}:`, error);
//         }
//       }
//     };
//   }
// };

export const listenToSelectedCustomerChangesMerchant = (customer, outletId) => {
  CommonStore.update((s) => {
    s.isLoadingCustomerInfo = true;
  });

  var subscriberUserOrderSelectedCustomerOrders = () => { };
  var subscriberUserAddress = () => { };
  var subscriberUserVoucherRedemption = () => { };
  var subscriberUserBeerDocketSelectedCustomerUserBeerDocketsUserId = () => { };
  var subscriberUserOrderSelectedCustomerOrdersPhone = () => { };
  var subscriberLoyaltyCampaignCreditTransactionSelectedCustomerLCCTransactionsEmail = () => { };
  var subscriberUserPointsTransactionSelectedCustomerPointsTransactionsEmail = () => { };
  var subscriberUserBeerDocketSelectedCustomerUserBeerDocketsEmail = () => { };
  var subscriberLoyaltyCampaignCreditTransactionSelectedCustomerLCCTransactionsPhone = () => { };
  var subscriberUserPointsTransactionSelectedCustomerPointsTransactionsPhone = () => { };
  var subscriberUserLoyaltyCampaignSelectedCustomerUserLoyaltyCampaigns = () => { };
  var subscriberUserTaggableVoucherSelectedCustomerUserTaggableVouchersView = () => { };
  var subscriberUserLoyaltyStampSelectedCustomerUserLoyaltyStamps = () => { };

  if (customer.firebaseUid) {
    // subscriberUserOrderSelectedCustomerOrders = firestore()
    //   .collection(Collections.UserOrder)
    //   .where('userId', '==', customer.firebaseUid)
    //   // .where('orderType', '==', ORDER_TYPE.DINEIN)
    //   .where('outletId', '==', outletId)
    //   .where('deletedAt', '==', null)
    //   .onSnapshot((snapshot) => {
    //     global.emitter.emit(`${Collections.UserOrder}-snapshot-selectedCustomerOrders`, { snapshot });
    //   });

    // subscriberUserAddress = firestore()
    //   .collection(Collections.UserAddress)
    //   .where('userId', '==', customer.firebaseUid)
    //   .where('deletedAt', '==', null)
    //   .limit(1)
    //   .onSnapshot((snapshot) => {
    //     global.emitter.emit(`${Collections.UserAddress}-snapshot`, { snapshot });
    //   });

    // firestore()
    //   .collection(Collections.UserPointsTransaction)
    //   .where('userId', '==', customer.firebaseUid)
    //   .where('outletId', '==', outletId)
    //   .where('deletedAt', '==', null)
    //   .onSnapshot((snapshot) => {
    //     // console.log(`${Collections.UserPointsTransaction} changed!`);

    //     var selectedCustomerPointsTransactions = [];
    //     var selectedCustomerPointsBalance = 0;

    //     if (snapshot && !snapshot.empty) {
    //       for (var i = 0; i < snapshot.size; i++) {
    //         const record = snapshot.docs[i].data();

    //         selectedCustomerPointsTransactions.push(record);
    //         selectedCustomerPointsBalance += record.amount;
    //       }
    //     }

    //     selectedCustomerPointsTransactions.sort(
    //       (a, b) => b.createdAt - a.createdAt,
    //     );

    //     OutletStore.update((s) => {
    //       s.selectedCustomerPointsTransactions =
    //         selectedCustomerPointsTransactions;
    //       s.selectedCustomerPointsBalance = selectedCustomerPointsBalance;
    //     });
    //   });

    // 2023-04-30 - No need first
    // subscriberUserVoucherRedemption = firestore()
    //   .collection(Collections.UserVoucherRedemption)
    //   .where('userId', '==', customer.firebaseUid)
    //   .where('deletedAt', '==', null)
    //   .limit(1)
    //   .onSnapshot((snapshot) => {
    //     global.emitter.emit(`${Collections.UserVoucherRedemption}-snapshot`, { snapshot: snapshot });
    //   });

    if (!customer.skipData) {
      subscriberUserBeerDocketSelectedCustomerUserBeerDocketsUserId = firestore()
        .collection(Collections.UserBeerDocket)
        .where('userId', '==', customer.firebaseUid)
        .where('deletedAt', '==', null)
        .onSnapshot((snapshot) => {
          global.emitter.emit(`${Collections.UserBeerDocket}-snapshot-selectedCustomerUserBeerDocketsUserId`, { snapshot });
        });
    }
    else {
      OutletStore.update((s) => {
        s.selectedCustomerUserBeerDocketsUserId = [];
      });
    }
  } else if (customer.number) {
    OutletStore.update((s) => {
      s.selectedCustomerAddresses = [];

      // s.selectedCustomerPointsTransactions = [];
      // s.selectedCustomerPointsBalance = 0;

      s.selectedCustomerVoucherRedemptions = [];

      s.selectedCustomerUserBeerDockets = [];
    });

    if (!customer.skipData) {
      subscriberUserOrderSelectedCustomerOrdersPhone = firestore()
        .collection(Collections.UserOrder)
        .where('userPhone', '==', customer.number)
        .where('outletId', '==', outletId)
        // .where('orderType', '==', ORDER_TYPE.DINEIN)
        .where('deletedAt', '==', null)
        .limit(50)
        .onSnapshot((snapshot) => {
          global.emitter.emit(`${Collections.UserOrder}-snapshot-selectedCustomerOrders-phone`, { snapshot });
        });
    }
    else {
      OutletStore.update((s) => {
        s.selectedCustomerDineInOrders = [];
        s.selectedCustomerOrders = [];
      });
    }
  }

  if (customer.email) {
    subscriberLoyaltyCampaignCreditTransactionSelectedCustomerLCCTransactionsEmail = firestore()
      .collection(Collections.LoyaltyCampaignCreditTransaction)
      .where('email', '==', customer.email)
      .where('outletId', '==', customer.outletId)
      .where('deletedAt', '==', null)
      .onSnapshot((snapshot) => {
        global.emitter.emit(`${Collections.LoyaltyCampaignCreditTransaction}-snapshot-selectedCustomerLCCTransactionsEmail`, { snapshot });
      });

    subscriberUserPointsTransactionSelectedCustomerPointsTransactionsEmail = firestore()
      .collection(Collections.UserPointsTransaction)
      .where('email', '==', customer.email)
      .where('outletId', '==', customer.outletId)
      .where('deletedAt', '==', null)
      .onSnapshot((snapshot) => {
        global.emitter.emit(`${Collections.UserPointsTransaction}-snapshot--selectedCustomerPointsTransactionsEmail`, { snapshot });
      });

    if (!customer.skipData) {
      subscriberUserBeerDocketSelectedCustomerUserBeerDocketsEmail = firestore()
        .collection(Collections.UserBeerDocket)
        .where('userId', '==', customer.email)
        .where('deletedAt', '==', null)
        .onSnapshot((snapshot) => {
          global.emitter.emit(`${Collections.UserBeerDocket}-snapshot-selectedCustomerUserBeerDocketsEmail`, { snapshot });
        });
    }
    else {
      OutletStore.update((s) => {
        s.selectedCustomerUserBeerDocketsEmail = [];
      });
    }
  } else {
    OutletStore.update((s) => {
      s.selectedCustomerLCCTransactionsEmail = [];
      s.selectedCustomerLCCBalanceEmail = 0;

      s.selectedCustomerPointsTransactionsEmail = [];
      s.selectedCustomerPointsBalanceEmail = 0;

      s.selectedCustomerUserBeerDocketsEmail = [];
    });
  }

  if (customer.number) {
    subscriberLoyaltyCampaignCreditTransactionSelectedCustomerLCCTransactionsPhone = firestore()
      .collection(Collections.LoyaltyCampaignCreditTransaction)
      .where('phone', '==', customer.number)
      .where('outletId', '==', customer.outletId)
      .where('deletedAt', '==', null)
      .onSnapshot((snapshot) => {
        global.emitter.emit(`${Collections.LoyaltyCampaignCreditTransaction}-snapshot-selectedCustomerLCCTransactionsPhone`, { snapshot });
      });

    subscriberUserPointsTransactionSelectedCustomerPointsTransactionsPhone = firestore()
      .collection(Collections.UserPointsTransaction)
      .where('phone', '==', customer.number)
      .where('outletId', '==', customer.outletId)
      .where('deletedAt', '==', null)
      .onSnapshot((snapshot) => {
        global.emitter.emit(`${Collections.UserPointsTransaction}-snapshot-selectedCustomerPointsTransactionsPhone`, { snapshot });
      });
  } else {
    OutletStore.update((s) => {
      s.selectedCustomerPointsTransactionsPhone = [];
      s.selectedCustomerPointsBalancePhone = 0;
    });
  }

  // 2023-04-30 - No need first
  // subscriberUserLoyaltyCampaignSelectedCustomerUserLoyaltyCampaigns = firestore()
  //   .collection(Collections.UserLoyaltyCampaign)
  //   // .where('outletIdList', 'array-contains', outlet.uniqueId)
  //   .where('outletId', '==', customer.outletId)
  //   .where('email', '==', customer.email)
  //   // .where('deletedAt', '==', null)
  //   .onSnapshot((snapshot) => {
  //     global.emitter.emit(`${Collections.UserLoyaltyCampaign}-snapshot-selectedCustomerUserLoyaltyCampaigns`, { snapshot: snapshot });
  //   });

  if (!customer.skipData) {
    subscriberUserTaggableVoucherSelectedCustomerUserTaggableVouchersView = firestore()
      .collection(Collections.UserTaggableVoucher)
      // .where('outletId', '==', customer.outletId)
      // .where('email', '==', customer.email)
      // .where('expirationDate', '>', moment().valueOf())
      // .orderBy('expirationDate')
      // 2025-02-12 - use query from web order
      .where('merchantId', '==', customer.merchantId)
      .where('phone', '==', customer.number)
      // .where('redeemDate', '==', null)
      .where('expirationDate', '>', moment().subtract(6, 'months').valueOf())
      .where('expirationDate', '<', moment().add(6, 'months').valueOf())
      .orderBy('expirationDate', 'desc')
      .limit(50)
      .onSnapshot((snapshot) => {
        global.emitter.emit(`${Collections.UserTaggableVoucher}-snapshot-selectedCustomerUserTaggableVouchersView`, { snapshot });
      });

    subscriberUserLoyaltyStampSelectedCustomerUserLoyaltyStamps = firestore()
      .collection(Collections.UserLoyaltyStamp)
      // .where('outletIdList', 'array-contains', outlet.uniqueId)
      .where('outletId', '==', customer.outletId)
      .where('email', '==', customer.email)
      // .where('redeemDate', '==', null)
      .where('deletedAt', '==', null) // no need first
      // .where('expirationDate', '>', moment().valueOf())
      // .orderBy('expirationDate')
      .limit(50)
      .onSnapshot((snapshot) => {
        global.emitter.emit(`${Collections.UserLoyaltyStamp}-snapshot-selectedCustomerUserLoyaltyStamps`, { snapshot });
      });
  }
  else {
    OutletStore.update((s) => {
      s.selectedCustomerUserTaggableVouchersView =
        [];

      s.selectedCustomerUserLoyaltyStamps = [];
    });
  }

  // 2023-04-21 - For some reason, under unstable network, code in 'snapshotListener.js' didn't executed, thus isLoading won't turn to 'false'

  setTimeout(() => {
    CommonStore.update(s => {
      s.isLoadingCustomerInfo = false;
    });
  }, 5000);

  return () => {
    subscriberUserOrderSelectedCustomerOrders();
    subscriberUserAddress();
    subscriberUserVoucherRedemption();
    subscriberUserBeerDocketSelectedCustomerUserBeerDocketsUserId();
    subscriberUserOrderSelectedCustomerOrdersPhone();
    subscriberLoyaltyCampaignCreditTransactionSelectedCustomerLCCTransactionsEmail();
    subscriberUserPointsTransactionSelectedCustomerPointsTransactionsEmail();
    subscriberUserBeerDocketSelectedCustomerUserBeerDocketsEmail();
    subscriberLoyaltyCampaignCreditTransactionSelectedCustomerLCCTransactionsPhone();
    subscriberUserPointsTransactionSelectedCustomerPointsTransactionsPhone();
    subscriberUserLoyaltyCampaignSelectedCustomerUserLoyaltyCampaigns();
    subscriberUserTaggableVoucherSelectedCustomerUserTaggableVouchersView();
    subscriberUserLoyaltyStampSelectedCustomerUserLoyaltyStamps();
  };
};

export const listenToSelectedOrderToPayUserIdChanges = (
  selectedOrderToPayUserId,
) => {
  // firestore()
  //   .collection(Collections.UserVoucherRedemption)
  //   // .where('startAt', '<=', moment().valueOf())
  //   // .where('endAt', '>', moment().valueOf())
  //   .where('userId', '==', selectedOrderToPayUserId)
  //   .onSnapshot(async (snapshot) => {
  //     // console.log('asd')
  //     // console.log(`${Collections.UserVoucherRedemption} changed!`);
  //     // console.log('das', snapshot)
  //     var voucherIdRedemptionList = [];
  //     if (snapshot && !snapshot.empty) {
  //       const userVoucherRedemption =
  //         snapshot.docs[0].data();
  //       // // console.log(userVoucherRedemption.redemptions, 'balabala1');
  //       voucherIdRedemptionList = userVoucherRedemption.redemptions;
  //     }
  //     // // console.log('balabala', voucherIdRedemptionList)
  //     CommonStore.update((s) => {
  //       s.selectedOrderToPayUserIdVoucherIdRedemptionList =
  //         voucherIdRedemptionList;
  //     });
  //   });
};

export const listenToCommonChangesMerchant = () => {
  // 2024-10-18 - no longer used
  // var subscriberSegment = firestore()
  //   .collection(Collections.Segment)
  //   .onSnapshot((snapshot) => {
  //     global.emitter.emit(`${Collections.Segment}-snapshot`, { snapshot });
  //   });

  // 2024-10-18 - no longer used
  // var subscriberAccumulator = firestore()
  //   .collection(Collections.Accumulator)
  //   .where('uniqueId', '==', ACCUMULATOR_ID.GLOBAL)
  //   .onSnapshot((snapshot) => {
  //     global.emitter.emit(`${Collections.Accumulator}-snapshot`, { snapshot });
  //   });

  return () => {
    // subscriberSegment();
    // subscriberAccumulator();
  };
};

////////////////////////////////////////////////////////////////////////////////////

export const listenToUserChangesWaiter = (firebaseUid) => {
  // const firebaseUid = await AsyncStorage.getItem('firebaseUid');
  // const merchantId = await AsyncStorage.getItem('merchantId');
  // const role = await AsyncStorage.getItem('role');

  // // console.log(firebaseUid);
  // // console.log(merchantId);
  // // console.log(role);

  var merchantId = '';

  // Get user details
  var subscriberUserListenToUserChangesWaiter = firestore()
    .collection(Collections.User)
    .where('firebaseUid', '==', firebaseUid)
    .onSnapshot((snapshot) => {
      global.emitter.emit(`${Collections.User}-snapshot-listenToUserChangesWaiter`, { snapshot });
    });

  // Get merchant info
  var subscriberMerchantListenToUserChangesWaiter = firestore()
    .collection(Collections.Merchant)
    .where('uniqueId', '==', merchantId)
    .onSnapshot((snapshot) => {
      global.emitter.emit(`${Collections.Merchant}-snapshot-listenToUserChangesWaiter`, { snapshot });
    });

  return () => {
    subscriberUserListenToUserChangesWaiter();
    subscriberMerchantListenToUserChangesWaiter();
  };
};

export const listenToCurrOutletIdChangesWaiter = (
  role,
  currOutletId,
  isWaiterApp = false,
  tokenFcm,

  toggleOpenOrder,
  openOrderDays,
) => {
  // Get outlets info

  // if (role !== ROLE_TYPE.STORE_MANAGER && role !== ROLE_TYPE.ADMIN && role !== ROLE_TYPE.LEGACY) {
  // if (isWaiterApp) {
  //   firestore()
  //     .collection(Collections.Outlet)
  //     .where('uniqueId', '==', currOutletId)
  //     .limit(1)
  //     .onSnapshot((snapshot) => {
  //       // console.log(`${Collections.Outlet} changed!`);

  //       var allOutlets = [];
  //       var currOutlet = {};

  //       if (snapshot && !snapshot.empty) {
  //         for (var i = 0; i < snapshot.size; i++) {
  //           const record = snapshot.docs[i].data();

  //           allOutlets.push(record);
  //         }

  //         currOutlet = allOutlets[0];

  //         MerchantStore.update((s) => {
  //           s.allOutlets = allOutlets;
  //           s.currOutletId = currOutlet.uniqueId;
  //           s.currOutlet = currOutlet;
  //         });

  //         updateOutletAsyncStorage(currOutlet);
  //       }
  //     });

  //   firestore()
  //     .collection(Collections.OutletItem)
  //     .where('outletId', '==', currOutletId)
  //     .onSnapshot((snapshot) => {
  //       // console.log(`${Collections.OutletItem} changed!`);

  //       var outletItems = [];
  //       var outletItemsDict = {};

  //       if (snapshot && !snapshot.empty) {
  //         for (var i = 0; i < snapshot.size; i++) {
  //           const record = snapshot.docs[i].data();

  //           outletItems.push(record);
  //           outletItemsDict[record.uniqueId] = record;
  //         }
  //       }

  //       OutletStore.update((s) => {
  //         s.outletItems = outletItems;
  //         s.outletItemsDict = outletItemsDict;
  //       });
  //     });

  //   firestore()
  //     .collection(Collections.OutletItemCategory)
  //     .where('outletId', '==', currOutletId)
  //     .onSnapshot((snapshot) => {
  //       // console.log(`${Collections.OutletItemCategory} changed!`);

  //       var outletCategories = [];
  //       var outletCategoriesDict = {};

  //       if (snapshot && !snapshot.empty) {
  //         // var selectedOutletItemCategory = {};

  //         for (var i = 0; i < snapshot.size; i++) {
  //           const record = snapshot.docs[i].data();

  //           outletCategories.push(record);
  //           outletCategoriesDict[record.uniqueId] = record;
  //         }

  //         OutletStore.update((s) => {
  //           s.outletCategories = outletCategories;
  //           s.outletCategoriesDict = outletCategoriesDict;
  //         });

  //         CommonStore.update((s) => {
  //           s.selectedOutletItemCategory = outletCategories[0];
  //         });
  //       }
  //     });

  //   // firestore()
  //   //   .collection(Collections.OutletTax)
  //   //   .where('outletId', '==', currOutletId)
  //   //   .limit(1)
  //   //   .onSnapshot((snapshot) => {
  //   //     // console.log(`${Collections.OutletTax} changed!`);

  //   //     var outletsTaxDict = {};

  //   //     if (snapshot && !snapshot.empty) {
  //   //       for (var i = 0; i < snapshot.size; i++) {
  //   //         const record = snapshot.docs[i].data();

  //   //         outletsTaxDict[record.outletId] = record;
  //   //       }

  //   //       CommonStore.update((s) => {
  //   //         s.outletsTaxDict = outletsTaxDict;
  //   //       });
  //   //     }
  //   //   });
  // }

  // optimizations
  // firestore()
  //   .collection(Collections.UserOrder)
  //   .where('outletId', '==', currOutletId)
  //   // .where('orderStatus', '!=', USER_ORDER_STATUS.ORDER_COMPLETED)
  //   // .orderBy('createdAt') // invalid query, need fix this in future
  //   .onSnapshot((snapshot) => {
  //     // console.log(`${Collections.UserOrder} changed!`);

  //     var userOrders = [];
  //     var userOrdersDict = {};
  //     var userOrdersTableDict = {};

  //     var userOrdersAllStatus = [];

  //     if (snapshot && !snapshot.empty) {
  //       for (var i = 0; i < snapshot.size; i++) {
  //         const record = snapshot.docs[i].data();

  //         // currently filtered out cancelled orders
  //         if (
  //           record.orderStatus !==
  //           USER_ORDER_STATUS.ORDER_CANCELLED_BY_MERCHANT &&
  //           record.orderStatus !==
  //           USER_ORDER_STATUS.ORDER_CANCELLED_BY_USER
  //         ) {
  //           userOrders.push(record);
  //           userOrdersDict[record.uniqueId] = record;

  //           if (record.tableId && record.tableId.length > 0) {
  //             if (userOrdersTableDict[record.tableId] === undefined) {
  //               userOrdersTableDict[record.tableId] = [record];
  //             } else {
  //               userOrdersTableDict[record.tableId] = [
  //                 ...userOrdersTableDict[record.tableId],
  //                 record,
  //               ];
  //             }
  //           }
  //         }

  //         userOrdersAllStatus.push(record);
  //       }

  //       // // console.log('userOrders');
  //       // // console.log(userOrders);
  //       // // console.log('userOrdersTableDict');
  //       // // console.log(userOrdersTableDict);
  //     }

  //     userOrdersAllStatus.sort((a, b) => b.updatedAt - a.updatedAt);

  //     OutletStore.update((s) => {
  //       s.userOrders = userOrders;
  //       s.userOrdersDict = userOrdersDict;
  //       s.userOrdersTableDict = userOrdersTableDict;

  //       s.userOrdersAllStatus = userOrdersAllStatus;
  //     });
  //   });

  // 2023-05-24 - active orders here
  // optimizations
  // 2025-05-28 - no longer needed
  // var subscriberUserOrderUserOrders = firestore()
  //   .collection(Collections.UserOrder)
  //   .where('outletId', '==', currOutletId)
  //   // .where('orderStatus', '!=', USER_ORDER_STATUS.ORDER_COMPLETED)
  //   .where('orderStatus', 'in', [
  //     USER_ORDER_STATUS.ORDER_RECEIVED,
  //     USER_ORDER_STATUS.ORDER_AUTHORIZED,
  //     USER_ORDER_STATUS.ORDER_PREPARING,
  //     USER_ORDER_STATUS.ORDER_PREPARED,
  //     USER_ORDER_STATUS.ORDER_DELIVERED,

  //     USER_ORDER_STATUS.ORDER_REJECTED_BY_MERCHANT,
  //     USER_ORDER_STATUS.ORDER_CANCELLED_BY_MERCHANT,
  //     USER_ORDER_STATUS.ORDER_CANCELLED_BY_USER,

  //     USER_ORDER_STATUS.ORDER_SENDER_REJECTED,
  //     USER_ORDER_STATUS.ORDER_SENDER_CANCELED,
  //   ])

  //   .where(
  //     'createdAt',
  //     '>=',
  //     !toggleOpenOrder
  //       ?
  //       moment().subtract(1, 'day').startOf('day').valueOf()
  //       :
  //       moment().subtract(openOrderDays, 'day').startOf('day').valueOf(),
  //   ) // to limit to recent orders only

  //   // .orderBy('createdAt') // invalid query, need fix this in future
  //   .onSnapshot((snapshot) => {
  //     global.emitter.emit(`${Collections.UserOrder}-snapshot-userOrders`, { snapshot });
  //   });

  /////////////////////////////////////////////////////////////////////////

  // 2023-06-09 - To retrieve for active reservation orders (haven't completed yet)
  // 2025-05-28 - no longer needed
  // var subscriberUserOrderReservation = firestore()
  //   .collection(Collections.UserOrder)
  //   .where('outletId', '==', currOutletId)
  //   // .where('orderStatus', '!=', USER_ORDER_STATUS.ORDER_COMPLETED)
  //   .where('isReservationOrder', '==', true)
  //   .where('orderStatus', 'in', [
  //     USER_ORDER_STATUS.ORDER_RECEIVED,
  //     USER_ORDER_STATUS.ORDER_AUTHORIZED,
  //     USER_ORDER_STATUS.ORDER_PREPARING,
  //     USER_ORDER_STATUS.ORDER_PREPARED,
  //     USER_ORDER_STATUS.ORDER_DELIVERED,

  //     USER_ORDER_STATUS.ORDER_REJECTED_BY_MERCHANT,
  //     USER_ORDER_STATUS.ORDER_CANCELLED_BY_MERCHANT,
  //     USER_ORDER_STATUS.ORDER_CANCELLED_BY_USER,

  //     USER_ORDER_STATUS.ORDER_SENDER_REJECTED,
  //     USER_ORDER_STATUS.ORDER_SENDER_CANCELED,
  //   ])
  //   .where(
  //     'createdAt',
  //     '>=',
  //     moment().subtract(30, 'day').startOf('day').valueOf(),
  //   ) // to limit to recent orders only

  //   // .orderBy('createdAt') // invalid query, need fix this in future
  //   .onSnapshot((snapshot) => {
  //     global.emitter.emit(`${Collections.UserOrder}-snapshot-reservation`, { snapshot });
  //   });

  /////////////////////////////////////////////////////////////////////////

  // reason to not mix with snapshot is to prevent multiple printing when changes occurred
  // and doing 1 by 1?
  // firestore()
  //   .collection(Collections.UserOrder)
  //   .where('outletId', '==', currOutletId)
  //   .where('orderStatus', '!=', USER_ORDER_STATUS.ORDER_COMPLETED)
  //   // .where('orderStatus', '==', [USER_ORDER_STATUS.ORDER_RECEIVED, USER_ORDER_STATUS.ORDER_AUTHORIZED])
  //   // .where('triggerPrintingTimes', '<=', 0)
  //   // .where('printedTokenList', 'not-in', [tokenFcm || 'koodoo'])
  //   .where('printedTokenList', 'not-in', [tokenFcm])
  //   // .orderBy('createdAt') // invalid query, need fix this in future
  //   .onSnapshot((snapshot) => {
  //     // console.log(`${Collections.UserOrder} changed!`);

  //     var userOrdersPrinting = [];

  //     if (snapshot && !snapshot.empty) {
  //       for (var i = 0; i < snapshot.size; i++) {
  //         const record = snapshot.docs[i].data();

  //         if (record.orderStatus === USER_ORDER_STATUS.ORDER_RECEIVED ||
  //           record.orderStatus === USER_ORDER_STATUS.ORDER_AUTHORIZED) {
  //           userOrdersPrinting.push(record);
  //         }
  //       }
  //     }

  //     OutletStore.update((s) => {
  //       s.userOrdersPrinting = userOrdersPrinting;
  //     });
  //   });

  var subscriberOutletSection = firestore()
    .collection(Collections.OutletSection)
    .where('outletId', '==', currOutletId)
    .orderBy('createdAt')
    .onSnapshot((snapshot) => {
      global.emitter.emit(`${Collections.OutletSection}-snapshot`, { snapshot });
    });

  var subscriberOutletTable = firestore()
    .collection(Collections.OutletTable)
    .where('outletId', '==', currOutletId)
    .orderBy('createdAt')
    .onSnapshot((snapshot) => {
      global.emitter.emit(`${Collections.OutletTable}-snapshot`, { snapshot });
    });

  var subscriberOutletTableCombination = firestore()
    .collection(Collections.OutletTableCombination)
    .where('outletId', '==', currOutletId)
    // .orderBy('createdAt')
    .onSnapshot((snapshot) => {
      global.emitter.emit(`${Collections.OutletTableCombination}-snapshot`, { snapshot });
    });

  // Get current active orders, include completed

  // Get current active orders only
  // firestore()
  //   .collection(Collections.UserOrder)
  //   .where('outletId', '==', currOutletId)
  //   .where('orderStatus', '!=', USER_ORDER_STATUS.ORDER_COMPLETED)
  //   // .orderBy('createdAt') // invalid query, need fix this in future
  //   .onSnapshot((snapshot) => {
  //     // console.log(`${Collections.UserOrder} changed!`);

  //     var userOrders = [];
  //     var userOrdersDict = {};
  //     var userOrdersTableDict = {};

  //     if (snapshot && !snapshot.empty) {
  //       for (var i = 0; i < snapshot.size; i++) {
  //         const record = snapshot.docs[i].data();

  //         // currently filtered out cancelled orders
  //         if (
  //           record.orderStatus !==
  //           USER_ORDER_STATUS.ORDER_CANCELLED_BY_MERCHANT &&
  //           record.orderStatus !==
  //           USER_ORDER_STATUS.ORDER_CANCELLED_BY_USER
  //         ) {
  //           userOrders.push(record);
  //           userOrdersDict[record.uniqueId] = record;

  //           if (record.tableId && record.tableId.length > 0) {
  //             if (userOrdersTableDict[record.tableId] === undefined) {
  //               userOrdersTableDict[record.tableId] = [record];
  //             } else {
  //               userOrdersTableDict[record.tableId] = [
  //                 ...userOrdersTableDict[record.tableId],
  //                 record,
  //               ];
  //             }
  //           }
  //         }
  //       }
  //     }

  //     OutletStore.update((s) => {
  //       s.userOrders = userOrders;
  //       s.userOrdersDict = userOrdersDict;
  //       s.userOrdersTableDict = userOrdersTableDict;
  //     });
  //   });

  // var subscriberUserReservation = firestore()
  //   .collection(Collections.UserReservation)
  //   .where('outletId', '==', currOutletId)
  //   // .where('status', '!=', USER_RESERVATION_STATUS.SEATED)
  //   // .where('status', '!=', USER_RESERVATION_STATUS.SEATED)
  //   // .orderBy('createdAt')
  //   .onSnapshot((snapshot) => {
  //     global.emitter.emit(`${Collections.UserReservation}-snapshot`, { snapshot });
  //   });

  var subscriberUserReservationWaitList = firestore()
    .collection(Collections.UserReservationWaitList)
    .where('outletId', '==', currOutletId)
    // .where('status', '!=', USER_RESERVATION_STATUS.SEATED)
    // .where('status', '!=', USER_RESERVATION_STATUS.SEATED)
    // .orderBy('createdAt')
    .onSnapshot((snapshot) => {
      global.emitter.emit(`${Collections.UserReservationWaitList}-snapshot`, { snapshot });
    });

  var subscriberUserQueue = firestore()
    .collection(Collections.UserQueue)
    .where('outletId', '==', currOutletId)
    // .where('status', '!=', USER_QUEUE_STATUS.SERVED)
    // .where('status', '!=', USER_QUEUE_STATUS.SEATED)
    // .orderBy('createdAt')
    .onSnapshot((snapshot) => {
      global.emitter.emit(`${Collections.UserQueue}-snapshot`, { snapshot });
    });

  var subscriberUserRing = firestore()
    .collection(Collections.UserRing)
    .where('outletId', '==', currOutletId)
    // .where('status', '!=', USER_RING_STATUS.CANCELED)
    // .where('status', '!=', USER_RING_STATUS.ATTENDED)
    // .orderBy('createdAt')
    .onSnapshot((snapshot) => {
      global.emitter.emit(`${Collections.UserRing}-snapshot`, { snapshot });
    });

  var subscriberReservationConfig = firestore()
    .collection(Collections.ReservationConfig)
    .where('outletId', '==', currOutletId)
    .onSnapshot((snapshot) => {
      global.emitter.emit(`${Collections.ReservationConfig}-snapshot`, { snapshot });
    });

  var subscriberQueueConfig = firestore()
    .collection(Collections.QueueConfig)
    .where('outletId', '==', currOutletId)
    .onSnapshot((snapshot) => {
      global.emitter.emit(`${Collections.QueueConfig}-snapshot`, { snapshot });
    });

  // firestore()
  //     .collection(Collections.OutletItem)
  //     .where('outletId', '==', currOutletId)
  //     .onSnapshot(snapshot => {
  //         // console.log(`${Collections.OutletItem} changed!`);

  //         var selectedOutletItems = [];

  //         if (snapshot && !snapshot.empty) {
  //             for (var i = 0; i < snapshot.size; i++) {
  //                 const record = snapshot.docs[i].data();

  //                 selectedOutletItems.push(record);
  //             }
  //         }

  //         CommonStore.update(s => {
  //             s.selectedOutletItems = selectedOutletItems;
  //         });
  //     });

  // firestore()
  //     .collection(Collections.OutletItemCategory)
  //     .where('outletId', '==', currOutletId)
  //     .onSnapshot(snapshot => {
  //         // console.log(`${Collections.OutletItemCategory} changed!`);

  //         var selectedOutletItemCategories = [];
  //         var selectedOutletItemCategory = {};

  //         if (snapshot && !snapshot.empty) {
  //             for (var i = 0; i < snapshot.size; i++) {
  //                 const record = snapshot.docs[i].data();

  //                 selectedOutletItemCategories.push(record);
  //             }

  //             selectedOutletItemCategory = selectedOutletItemCategories[0];
  //         }

  //         CommonStore.update(s => {
  //             s.selectedOutletItemCategories = selectedOutletItemCategories;
  //             s.selectedOutletItemCategory = selectedOutletItemCategory;
  //         });
  //     });

  /////////////////////////////////////////////////////
  // Share from user app

  // disabled, as not in snapshot
  // var selectedOutletItems = [];
  // var selectedOutletItemCategories = [];
  // var selectedOutletItemCategory = {};

  // const outletItemSnapshot = await firestore().collection(Collections.OutletItem)
  //     .where('outletId', '==', currOutletId)
  //     .get();

  // if (!outletItemSnapshot.empty) {
  //     for (var i = 0; i < outletItemSnapshot.size; i++) {
  //         selectedOutletItems.push(outletItemSnapshot.docs[i].data());
  //     }
  // }

  // const outletItemCategorySnapshot = await firestore().collection(Collections.OutletItemCategory)
  //     .where('outletId', '==', currOutletId)
  //     .get();

  // if (!outletItemCategorySnapshot.empty) {
  //     for (var i = 0; i < outletItemCategorySnapshot.size; i++) {
  //         selectedOutletItemCategories.push(outletItemCategorySnapshot.docs[i].data());
  //     }

  //     selectedOutletItemCategory = selectedOutletItemCategories[0];
  // }

  // CommonStore.update(s => {
  //     s.selectedOutletItems = selectedOutletItems;
  //     s.selectedOutletItemCategories = selectedOutletItemCategories;
  //     s.selectedOutletItemCategory = selectedOutletItemCategory;
  // });

  return () => {
    // 2025-05-28 - no longer needed
    // subscriberUserOrderUserOrders();
    subscriberOutletSection();
    subscriberOutletTable();
    subscriberOutletTableCombination();
    // subscriberUserReservation();
    subscriberUserReservationWaitList();
    subscriberUserQueue();
    subscriberUserRing();
    // 2025-05-28 - no longer needed
    // subscriberUserOrderReservation();
    subscriberReservationConfig();
    subscriberQueueConfig();
  };
};

export const listenToCurrOutletIdReservationChanges = (
  role,
  currOutletId,
  isWaiterApp = false,
  tokenFcm,

  selectedCalendarData,
  historyStartDate,
  historyEndDate,
) => {
  let startDateTime = moment(selectedCalendarData).startOf('month').startOf('day').valueOf();
  let endDateTime = moment(selectedCalendarData).endOf('month').endOf('day').valueOf();

  console.log('=========================');
  console.log('listenToCurrOutletIdReservationChanges');
  console.log(moment(selectedCalendarData).format('YYYY-MM-DD'));
  console.log(moment(startDateTime).format('YYYY-MM-DD'));
  console.log(moment(endDateTime).format('YYYY-MM-DD'));
  console.log('=========================');

  // if (moment(historyStartDate).isSameOrBefore(startDateTime)) {
  //   startDateTime = historyStartDate;
  // }

  // if (moment(historyEndDate).isSameOrBefore(endDateTime)) {
  //   endDateTime = historyEndDate;
  // }

  // CommonStore.update(s => {
  //   s.historyStartDate = startDateTime;
  //   s.historyEndDate = endDateTime;
  // });

  console.log('=========================');
  console.log(moment(historyStartDate).format('YYYY-MM-DD'));
  console.log(moment(historyEndDate).format('YYYY-MM-DD'));
  console.log(moment(startDateTime).format('YYYY-MM-DD'));
  console.log(moment(endDateTime).format('YYYY-MM-DD'));
  console.log('=========================');

  var subscriberUserReservation = firestore()
    .collection(Collections.UserReservation)
    .where('outletId', '==', currOutletId)
    .where('reservationTime', '>=', startDateTime)
    .where('reservationTime', '<', endDateTime)
    // .where('status', '!=', USER_RESERVATION_STATUS.SEATED)
    // .where('status', '!=', USER_RESERVATION_STATUS.SEATED)
    // .orderBy('createdAt')
    .onSnapshot((snapshot) => {
      global.emitter.emit(`${Collections.UserReservation}-snapshot`, { snapshot });
    });

  return () => {
    subscriberUserReservation();
  };
};

// Share from user app
export const listenToSelectedOutletItemChanges = async (item) => {
  // var outletsItemAddOnDict = {};
  // var outletsItemAddOnChoiceDict = {};
  // CommonStore.update(s => {
  //     // outletsItemAddOnDict = {
  //     //     ...s.outletsItemAddOnDict,
  //     // };
  //     // outletsItemAddOnChoiceDict = {
  //     //     ...s.outletsItemAddOnChoiceDict,
  //     // };
  //     s.isLoading = true;
  // });
  // if (outletsItemAddOnDict[item.uniqueId] === undefined) {
  //     // console.log('item');
  //     // console.log(item);
  //     const outletItemAddOnSnapshot = await firestore().collection(Collections.OutletItemAddOn)
  //         .where('outletItemId', '==', item.uniqueId)
  //         .get();
  //     if (!outletItemAddOnSnapshot.empty) {
  //         var tempOutletItemAddOnList = [];
  //         for (var i = 0; i < outletItemAddOnSnapshot.size; i++) {
  //             tempOutletItemAddOnList.push(outletItemAddOnSnapshot.docs[i].data());
  //             outletsItemAddOnDict[item.uniqueId] = tempOutletItemAddOnList;
  //         }
  //     }
  // }
  // if (outletsItemAddOnDict[item.uniqueId] !== undefined) {
  //     const tempOutletItemAddOnList = outletsItemAddOnDict[item.uniqueId];
  //     for (var i = 0; i < tempOutletItemAddOnList.length; i++) {
  //         if (outletsItemAddOnChoiceDict[tempOutletItemAddOnList[i].uniqueId] === undefined) {
  //             // console.log('tempOutletItemAddOnList[i].uniqueId');
  //             // console.log(tempOutletItemAddOnList[i].uniqueId);
  //             const outletItemAddOnChoiceSnapshot = await firestore().collection(Collections.OutletItemAddOnChoice)
  //                 .where('outletItemAddOnId', '==', tempOutletItemAddOnList[i].uniqueId)
  //                 .get();
  //             if (!outletItemAddOnChoiceSnapshot.empty)
  //                 var tempOutletItemAddOnChoiceList = [];
  //             for (var j = 0; j < outletItemAddOnChoiceSnapshot.size; j++) {
  //                 tempOutletItemAddOnChoiceList.push(outletItemAddOnChoiceSnapshot.docs[j].data());
  //                 outletsItemAddOnChoiceDict[tempOutletItemAddOnList[i].uniqueId] = tempOutletItemAddOnChoiceList;
  //             }
  //         }
  //     }
  // }
  // CommonStore.update(s => {
  //     // s.outletsItemAddOnDict = {
  //     //     ...s.outletsItemAddOnDict,
  //     //     ...outletsItemAddOnDict,
  //     // };
  //     // s.outletsItemAddOnChoiceDict = {
  //     //     ...s.outletsItemAddOnChoiceDict,
  //     //     ...outletsItemAddOnChoiceDict,
  //     // };
  //     s.outletsItemAddOnDict = outletsItemAddOnDict;
  //     s.outletsItemAddOnChoiceDict = outletsItemAddOnChoiceDict;
  //     s.isLoading = false;
  // });
};

export const listenToSelectedOutletTableIdChanges = (
  userId,
  selectedOutletTableId,
  selectedOutletId,
) => {
  // Get outlets
  // // console.log('listenToSelectedOutletTableIdChanges');
  // // console.log(selectedOutletTableId);
  // // console.log(selectedOutletId);
  // // console.log(userId);
  // firestore()
  //     .collection(Collections.UserCart)
  //     .where('tableId', '==', selectedOutletTableId)
  //     .where('outletId', '==', selectedOutletId)
  //     .where('waiterId', '==', userId)
  //     // .where('userId', '==', userId)
  //     .limit(1)
  //     .onSnapshot(async snapshot => {
  //         // console.log(`${Collections.UserCart} changed!`);
  //         // console.log(snapshot);
  //         var userCart = {};
  //         var cartItems = [];
  //         if (snapshot && !snapshot.empty) {
  //             const record = snapshot.docs[0].data();
  //             userCart = record;
  //             cartItems = record.cartItems;
  //             // // console.log(moment().diff(moment(userCart.updatedAt), 'minute') >= 1);
  //             // if (moment().diff(moment(userCart.updatedAt), 'minute') >= 1) {
  //             //     await deleteUserCart(userCart.uniqueId);
  //             // }
  //         }
  //         if (userCart && userCart.userId) {
  //             await AsyncStorage.setItem(`${userId}.cartItems`, JSON.stringify(cartItems));
  //             // await AsyncStorage.setItem(`${userId}.cartOutletId`, selectedOutletId);
  //         }
  //         else {
  //             await AsyncStorage.removeItem(`${userId}.cartItems`);
  //             // await AsyncStorage.removeItem(`${userId}.cartOutletId`);
  //         }
  //         CommonStore.update(s => {
  //             s.userCart = userCart;
  //             s.cartItems = cartItems;
  //         });
  //     });
};

// export const listenToSelectedOrderToPayUserIdChanges = async (firebaseUid) => {
//     firestore()
//         .collection(Collections.UserVoucherRedemption)
//         // .where('startAt', '<=', moment().valueOf())
//         // .where('endAt', '>', moment().valueOf())
//         .where('userId', '==', firebaseUid)
//         .onSnapshot(async snapshot => {
//             // console.log(`${Collections.UserVoucherRedemption} changed!`);

//             var voucherIdRedemptionList = [];
//             //var selectedCustomerVouchers = [];

//             if (snapshot && !snapshot.empty) {
//                 const userVoucherRedemptionSnapshot = await firestore().collection(Collections.UserVoucherRedemption)
//                     .where('userId', '==', firebaseUid)
//                     .limit(1)
//                     .get();

//                 if (!userVoucherRedemptionSnapshot.empty) {
//                     const userVoucherRedemption = userVoucherRedemptionSnapshot.docs[0].data();

//                     voucherIdRedemptionList = userVoucherRedemption.redemptions;
//                 }
//             }

//             CommonStore.update(s => {
//                 s.orderToPayUserVoucherIdRedemptionList = voucherIdRedemptionList;
//                 // s.voucherIdRedemptionList = voucherIdRedemptionList;

//             });
//         });
// };

// 2024-11-26 - host & display support
export const listenToDisplayScreenChanges = (pairingType, currOutletId) => {
  var subscriberOutletDisplay = firestore()
    .collection(Collections.OutletDisplay)
    .where('pairingType', '==', pairingType)
    .where('outletId', '==', currOutletId)
    .onSnapshot((snapshot) => {
      global.emitter.emit(`${Collections.OutletDisplay}-snapshot`, { snapshot });
    });

  return () => {
    subscriberOutletDisplay();
  };
};

export const requestNotificationsPermission = async () => {
  const authStatus = await messaging().requestPermission();
  const enabled =
    authStatus === messaging.AuthorizationStatus.AUTHORIZED ||
    authStatus === messaging.AuthorizationStatus.PROVISIONAL;

  if (enabled) {
    // console.log('Authorization status:', authStatus);
  }
};

const pad = (n) => {
  return (`00000000${n}`).substr(-8);
};
const naturalExpand = (a) => {
  return a.replace(/\d+/g, pad);
};
export const naturalCompare = (a, b) => {
  return naturalExpand(a).localeCompare(naturalExpand(b));
};

export const sortPaymentDataList = (dataList, paymentSortFieldType) => {
  var dataListTemp = [...dataList];

  const paymentSortFieldTypeValue =
    PAYMENT_SORT_FIELD_TYPE_VALUE[paymentSortFieldType];

  const paymentSortFieldTypeCompare =
    PAYMENT_SORT_FIELD_TYPE_COMPARE[paymentSortFieldType];

  if (paymentSortFieldTypeCompare === PAYMENT_SORT_COMPARE_OPERATOR.ASC) {
    if (
      paymentSortFieldType ===
      PAYMENT_SORT_FIELD_TYPE.PAYMENT_CREATED_DATE_ASC ||
      paymentSortFieldType === PAYMENT_SORT_FIELD_TYPE.PAYMENT_UPDATED_DATE_ASC
    ) {
      dataListTemp.sort(
        (a, b) =>
          moment(a[paymentSortFieldTypeValue]).valueOf() -
          moment(b[paymentSortFieldTypeValue]).valueOf(),
      );
    } else if (
      paymentSortFieldType === PAYMENT_SORT_FIELD_TYPE.PAYMENT_NAME_ASC
    ) {
      dataListTemp.sort((a, b) =>
        (a[paymentSortFieldTypeValue]
          ? a[paymentSortFieldTypeValue]
          : ''
        ).localeCompare(
          b[paymentSortFieldTypeValue] ? b[paymentSortFieldTypeValue] : '',
        ),
      );
    } else if (
      paymentSortFieldType === PAYMENT_SORT_FIELD_TYPE.PAYMENT_STATUS_ASC
    ) {
      dataListTemp.sort(
        (a, b) => a[paymentSortFieldTypeValue] - b[paymentSortFieldTypeValue],
      );
    } else {
      dataListTemp.sort(
        (a, b) => a[paymentSortFieldTypeValue] - b[paymentSortFieldTypeValue],
      );
    }
  } else if (
    paymentSortFieldTypeCompare === PAYMENT_SORT_COMPARE_OPERATOR.DESC
  ) {
    if (
      paymentSortFieldType ===
      PAYMENT_SORT_FIELD_TYPE.PAYMENT_CREATED_DATE_DESC ||
      paymentSortFieldType === PAYMENT_SORT_FIELD_TYPE.PAYMENT_UPDATED_DATE_DESC
    ) {
      dataListTemp.sort(
        (a, b) => b[paymentSortFieldTypeValue] - a[paymentSortFieldTypeValue],
      );
    } else if (
      paymentSortFieldType === PAYMENT_SORT_FIELD_TYPE.PAYMENT_NAME_DESC
    ) {
      dataListTemp.sort((a, b) =>
        (b[paymentSortFieldTypeValue]
          ? b[paymentSortFieldTypeValue]
          : ''
        ).localeCompare(
          a[paymentSortFieldTypeValue] ? a[paymentSortFieldTypeValue] : '',
        ),
      );
    } else if (
      paymentSortFieldType === PAYMENT_SORT_FIELD_TYPE.PAYMENT_STATUS_DESC
    ) {
      dataListTemp.sort(
        (a, b) => b[paymentSortFieldTypeValue] - a[paymentSortFieldTypeValue],
      );
    } else {
      dataListTemp.sort(
        (a, b) =>
          moment(b[paymentSortFieldTypeValue]).valueOf() -
          moment(a[paymentSortFieldTypeValue]).valueOf(),
      );
    }
  }

  return dataListTemp;
};

export const sortReportDataList = (dataList, reportSortFieldType) => {
  var dataListTemp = [...dataList];

  const reportSortFieldTypeValue =
    REPORT_SORT_FIELD_TYPE_VALUE[reportSortFieldType];

  const reportSortFieldTypeCompare =
    REPORT_SORT_FIELD_TYPE_COMPARE[reportSortFieldType];

  if (reportSortFieldTypeCompare === REPORT_SORT_COMPARE_OPERATOR.ASC) {
    if (
      reportSortFieldType === REPORT_SORT_FIELD_TYPE.DATE_TIME_ASC ||
      reportSortFieldType ===
      REPORT_SORT_FIELD_TYPE.VOUCHER_REDEEMED_DATE_ASC ||
      reportSortFieldType === REPORT_SORT_FIELD_TYPE.VOUCHER_USED_DATE_ASC
    ) {
      dataListTemp.sort(
        (a, b) =>
          moment(a[reportSortFieldTypeValue]).valueOf() -
          moment(b[reportSortFieldTypeValue]).valueOf(),
      );
    } else if (
      reportSortFieldType ===
      REPORT_SORT_FIELD_TYPE.USER_ORDER_OUTLET_NAME_ASC ||
      reportSortFieldType ===
      REPORT_SORT_FIELD_TYPE.USER_ORDER_EMPLOYEE_NAME_ASC ||
      reportSortFieldType === REPORT_SORT_FIELD_TYPE.USER_ORDER_USER_NAME_ASC ||
      reportSortFieldType === REPORT_SORT_FIELD_TYPE.USER_ORDER_TYPE_ASC ||
      reportSortFieldType ===
      REPORT_SORT_FIELD_TYPE.USER_ORDER_COURIER_NAME_ASC ||
      reportSortFieldType === REPORT_SORT_FIELD_TYPE.CART_ITEM_NAME_ASC ||
      reportSortFieldType === REPORT_SORT_FIELD_TYPE.CART_ITEM_ORDER_TYPE_ASC ||
      reportSortFieldType === REPORT_SORT_FIELD_TYPE.CART_ITEM_REMARKS_ASC ||
      reportSortFieldType === REPORT_SORT_FIELD_TYPE.PAYMENT_METHOD_ASC ||
      reportSortFieldType === REPORT_SORT_FIELD_TYPE.PRODUCT_NAME_ASC ||
      reportSortFieldType === REPORT_SORT_FIELD_TYPE.PRODUCT_CATEGORY_ASC ||
      reportSortFieldType === REPORT_SORT_FIELD_TYPE.PRODUCT_SKU_ASC ||
      reportSortFieldType === REPORT_SORT_FIELD_TYPE.VOUCHER_NAME_ASC ||
      reportSortFieldType === REPORT_SORT_FIELD_TYPE.VOUCHER_TYPE_ASC ||
      reportSortFieldType === REPORT_SORT_FIELD_TYPE.VOUCHER_USERNAME_ASC ||
      reportSortFieldType === REPORT_SORT_FIELD_TYPE.PROMOTION_TITLE_ASC ||
      reportSortFieldType === REPORT_SORT_FIELD_TYPE.PROMOTION_TYPE_ASC
    ) {
      dataListTemp.sort((a, b) =>
        (a[reportSortFieldTypeValue]
          ? a[reportSortFieldTypeValue]
          : ''
        ).localeCompare(
          b[reportSortFieldTypeValue] ? b[reportSortFieldTypeValue] : '',
        ),
      );
    } else {
      dataListTemp.sort(
        (a, b) => a[reportSortFieldTypeValue] - b[reportSortFieldTypeValue],
      );
    }
  } else if (reportSortFieldTypeCompare === REPORT_SORT_COMPARE_OPERATOR.DESC) {
    if (
      reportSortFieldType === REPORT_SORT_FIELD_TYPE.DATE_TIME_DESC ||
      reportSortFieldType ===
      REPORT_SORT_FIELD_TYPE.VOUCHER_REDEEMED_DATE_DESC ||
      reportSortFieldType === REPORT_SORT_FIELD_TYPE.VOUCHER_USED_DATE_DESC
    ) {
      dataListTemp.sort(
        (a, b) =>
          moment(b[reportSortFieldTypeValue]).valueOf() -
          moment(a[reportSortFieldTypeValue]).valueOf(),
      );
    } else if (
      reportSortFieldType ===
      REPORT_SORT_FIELD_TYPE.USER_ORDER_OUTLET_NAME_DESC ||
      reportSortFieldType ===
      REPORT_SORT_FIELD_TYPE.USER_ORDER_EMPLOYEE_NAME_DESC ||
      reportSortFieldType ===
      REPORT_SORT_FIELD_TYPE.USER_ORDER_USER_NAME_DESC ||
      reportSortFieldType === REPORT_SORT_FIELD_TYPE.USER_ORDER_TYPE_DESC ||
      reportSortFieldType ===
      REPORT_SORT_FIELD_TYPE.USER_ORDER_COURIER_NAME_DESC ||
      reportSortFieldType === REPORT_SORT_FIELD_TYPE.CART_ITEM_NAME_DESC ||
      reportSortFieldType ===
      REPORT_SORT_FIELD_TYPE.CART_ITEM_ORDER_TYPE_DESC ||
      reportSortFieldType === REPORT_SORT_FIELD_TYPE.CART_ITEM_REMARKS_DESC ||
      reportSortFieldType === REPORT_SORT_FIELD_TYPE.PAYMENT_METHOD_DESC ||
      reportSortFieldType === REPORT_SORT_FIELD_TYPE.PRODUCT_NAME_DESC ||
      reportSortFieldType === REPORT_SORT_FIELD_TYPE.PRODUCT_CATEGORY_DESC ||
      reportSortFieldType === REPORT_SORT_FIELD_TYPE.PRODUCT_SKU_DESC ||
      reportSortFieldType === REPORT_SORT_FIELD_TYPE.VOUCHER_NAME_DESC ||
      reportSortFieldType === REPORT_SORT_FIELD_TYPE.VOUCHER_TYPE_DESC ||
      reportSortFieldType === REPORT_SORT_FIELD_TYPE.VOUCHER_USERNAME_DESC ||
      reportSortFieldType === REPORT_SORT_FIELD_TYPE.PRODUCT_SKU_DESC ||
      reportSortFieldType === REPORT_SORT_FIELD_TYPE.PROMOTION_TITLE_DESC ||
      reportSortFieldType === REPORT_SORT_FIELD_TYPE.PROMOTION_TYPE_DESC
    ) {
      dataListTemp.sort((a, b) =>
        (b[reportSortFieldTypeValue]
          ? b[reportSortFieldTypeValue]
          : ''
        ).localeCompare(
          a[reportSortFieldTypeValue] ? a[reportSortFieldTypeValue] : '',
        ),
      );
    } else {
      dataListTemp.sort(
        (a, b) => b[reportSortFieldTypeValue] - a[reportSortFieldTypeValue],
      );
    }
  }

  return dataListTemp;
};

export const sortWOIList = (dataList, WOIListSortFieldType) => {
  var dataListTemp = [...dataList];

  const WOIListSortFieldTypeValue =
    WOILIST_SORT_FIELD_TYPE_VALUE[WOIListSortFieldType];

  const WOIListSortFieldTypeCompare =
    WOILIST_SORT_FIELD_TYPE_COMPARE[WOIListSortFieldType];

  if (WOIListSortFieldTypeCompare === WOILIST_SORT_COMPARE_OPERATOR.ASC) {
    // if (
    //   WOIListSortFieldType === WOILIST_SORT_FIELD_TYPE.DATE_TIME_ASC ||
    //   WOIListSortFieldType ===
    //   WOILIST_SORT_FIELD_TYPE.VOUCHER_REDEEMED_DATE_ASC ||
    //   WOIListSortFieldType === WOILIST_SORT_FIELD_TYPE.VOUCHER_USED_DATE_ASC
    // ) {
    //   dataListTemp.sort(
    //     (a, b) =>
    //       moment(a[WOIListSortFieldTypeValue]).valueOf() -
    //       moment(b[WOIListSortFieldTypeValue]).valueOf(),
    //   );
    // } else 
    if (
      WOIListSortFieldType ===
      WOILIST_SORT_FIELD_TYPE.SALES_ORDER_NO_ASC ||
      WOIListSortFieldType ===
      WOILIST_SORT_FIELD_TYPE.UNIT_ASC ||
      WOIListSortFieldType ===
      WOILIST_SORT_FIELD_TYPE.PRODUCT_ASC ||
      WOIListSortFieldType ===
      WOILIST_SORT_FIELD_TYPE.OUTLET_ASC ||
      WOIListSortFieldType ===
      WOILIST_SORT_FIELD_TYPE.INGREDIENTS_ASC ||
      WOIListSortFieldType ===
      WOILIST_SORT_FIELD_TYPE.STATUS_ASC
    ) {
      dataListTemp.sort((a, b) =>
        (a[WOIListSortFieldTypeValue]
          ? a[WOIListSortFieldTypeValue]
          : ''
        ).localeCompare(
          b[WOIListSortFieldTypeValue] ? b[WOIListSortFieldTypeValue] : '',
        ),
      );
    } else {
      dataListTemp.sort(
        (a, b) => a[WOIListSortFieldTypeValue] - b[WOIListSortFieldTypeValue],
      );
    }
  } else if (WOIListSortFieldTypeCompare === WOILIST_SORT_COMPARE_OPERATOR.DESC) {
    // if (
    //   WOIListSortFieldType === WOILIST_SORT_FIELD_TYPE.DATE_TIME_DESC ||
    //   WOIListSortFieldType ===
    //   WOILIST_SORT_FIELD_TYPE.VOUCHER_REDEEMED_DATE_DESC ||
    //   WOIListSortFieldType === WOILIST_SORT_FIELD_TYPE.VOUCHER_USED_DATE_DESC
    // ) {
    //   dataListTemp.sort(
    //     (a, b) => b[WOIListSortFieldTypeValue] - a[WOIListSortFieldTypeValue],
    //   );
    // } else 
    if (
      WOIListSortFieldType ===
      WOILIST_SORT_FIELD_TYPE.SALES_ORDER_NO_DESC ||
      WOIListSortFieldType ===
      WOILIST_SORT_FIELD_TYPE.UNIT_DESC ||
      WOIListSortFieldType ===
      WOILIST_SORT_FIELD_TYPE.UNIT_DESC ||
      WOIListSortFieldType ===
      WOILIST_SORT_FIELD_TYPE.PRODUCT_DESC ||
      WOIListSortFieldType ===
      WOILIST_SORT_FIELD_TYPE.OUTLET_DESC ||
      WOIListSortFieldType ===
      WOILIST_SORT_FIELD_TYPE.INGREDIENTS_DESC ||
      WOIListSortFieldType ===
      WOILIST_SORT_FIELD_TYPE.STATUS_DESC
    ) {
      dataListTemp.sort((a, b) =>
        (b[WOIListSortFieldTypeValue]
          ? b[WOIListSortFieldTypeValue]
          : ''
        ).localeCompare(
          a[WOIListSortFieldTypeValue] ? a[WOIListSortFieldTypeValue] : '',
        ),
      );
    } else {
      dataListTemp.sort(
        (a, b) =>
          moment(b[WOIListSortFieldTypeValue]).valueOf() -
          moment(a[WOIListSortFieldTypeValue]).valueOf(),
      );
    }
  }

  return dataListTemp;
};

export const sortWOList = (dataList, WOListSortFieldType) => {
  var dataListTemp = [...dataList];

  const WOListSortFieldTypeValue =
    WOLIST_SORT_FIELD_TYPE_VALUE[WOListSortFieldType];

  const WOListSortFieldTypeCompare =
    WOLIST_SORT_FIELD_TYPE_COMPARE[WOListSortFieldType];

  if (WOListSortFieldTypeCompare === WOLIST_SORT_COMPARE_OPERATOR.ASC) {
    if (
      WOListSortFieldType === WOLIST_SORT_FIELD_TYPE.ISSUES_DATE_ASC
    ) {
      dataListTemp.sort(
        (a, b) =>
          moment(a[WOListSortFieldTypeValue]).valueOf() -
          moment(b[WOListSortFieldTypeValue]).valueOf(),
      );
    } else if (
      WOListSortFieldType ===
      WOLIST_SORT_FIELD_TYPE.DOCUMENT_NO_ASC ||
      WOListSortFieldType ===
      WOLIST_SORT_FIELD_TYPE.DESCRIPTION_ASC ||
      WOListSortFieldType ===
      WOLIST_SORT_FIELD_TYPE.STATUS_ASC
    ) {
      dataListTemp.sort((a, b) =>
        (a[WOListSortFieldTypeValue]
          ? a[WOListSortFieldTypeValue]
          : ''
        ).localeCompare(
          b[WOListSortFieldTypeValue] ? b[WOListSortFieldTypeValue] : '',
        ),
      );
    } else {
      dataListTemp.sort(
        (a, b) => a[WOListSortFieldTypeValue] - b[WOListSortFieldTypeValue],
      );
    }
  } else if (WOListSortFieldTypeCompare === WOLIST_SORT_COMPARE_OPERATOR.DESC) {
    if (
      WOListSortFieldType === WOLIST_SORT_FIELD_TYPE.ISSUES_DATE_DESC
    ) {
      dataListTemp.sort(
        (a, b) => b[WOListSortFieldTypeValue] - a[WOListSortFieldTypeValue],
      );
    } else
      if (
        WOListSortFieldType ===
        WOLIST_SORT_FIELD_TYPE.DOCUMENT_NO_DESC ||
        WOListSortFieldType ===
        WOLIST_SORT_FIELD_TYPE.DESCRIPTION_DESC ||
        WOListSortFieldType ===
        WOLIST_SORT_FIELD_TYPE.STATUS_DESC
      ) {
        dataListTemp.sort((a, b) =>
          (b[WOListSortFieldTypeValue]
            ? b[WOListSortFieldTypeValue]
            : ''
          ).localeCompare(
            a[WOListSortFieldTypeValue] ? a[WOListSortFieldTypeValue] : '',
          ),
        );
      } else {
        dataListTemp.sort(
          (a, b) =>
            moment(b[WOListSortFieldTypeValue]).valueOf() -
            moment(a[WOListSortFieldTypeValue]).valueOf(),
        );
      }
  }

  return dataListTemp;
};

export const removeOrdinalFromDate = (date) => {
  return moment(date).format('Do').replace(/\D/g, '');
};

export const getOrdinalFromDate = (date) => {
  return moment(date).format('Do').replace(/[0-9]/g, '');
};

export const subText = (base, exponent) => {
  return (
    <View style={{ flexDirection: 'row' }}>
      <View style={{ alignItems: 'flex-end' }}>
        <Text style={{ fontSize: 13 }}>{base}</Text>
      </View>
      <View style={{ alignItems: 'flex-start' }}>
        <Text style={{ fontSize: 10 }}>{exponent}</Text>
      </View>
    </View>
  );
};

export const autofitColumns = (json, worksheet) => {
  var objectMaxLength = [];

  json.map((jsonData) => {
    Object.entries(jsonData).map(([, v], idx) => {
      const columnValue = v;
      objectMaxLength[idx] =
        objectMaxLength[idx] >= columnValue.length
          ? objectMaxLength[idx]
          : columnValue.length;
    });
  });

  const wscols = objectMaxLength.map((w) => ({ width: w }));
  worksheet['!cols'] = wscols;

  return worksheet;
};

export const _autoFitColumns = (json, worksheet, header) => {
  const jsonKeys = header || Object.keys(json[0]);

  const objectMaxLength = [];
  jsonKeys.forEach((key) => {
    objectMaxLength.push(
      pixelWidth(key, {
        size: 5,
      }),
    );
  });

  json.forEach((data, i) => {
    const value = json[i];
    jsonKeys.forEach((key, j) => {
      const l = value[jsonKeys[j]]
        ? pixelWidth(value[jsonKeys[j]], {
          size: 5,
        })
        : 0;
      objectMaxLength[j] = objectMaxLength[j] >= l ? objectMaxLength[j] : l;
    });
  });

  return objectMaxLength.map((w) => {
    return { width: w };
  });
};

export const getPathForFirebaseStorageFromBlob = async (res) => {
  if (Platform.OS === 'ios') {
    // return res.uri;
    return res;
  } else if (res.uri && res.uri.startsWith('content://')) {
    // const uriComponents = uri.split('/')
    // const fileNameAndExtension = uriComponents[uriComponents.length - 1]
    // const destPath = `${RNFS.TemporaryDirectoryPath}/${fileNameAndExtension}`
    // await RNFS.copyFile(uri, 'file://' + destPath);
    // return 'file://' + destPath;

    return `file://${decodeURIComponent(res.fileCopyUri)}`;
  } else {
    var stat;
    if (res.uri) {
      stat = await RNFetchBlob.fs.stat(res.uri);
    } else if (res) {
      stat = await RNFetchBlob.fs.stat(res);
    }
    // else {
    //   console.error('EMPTY res & res.uri in getPathForFirebaseStorageFromBlob')
    //   return
    // }

    return stat.path;
  }
};

export const weekOfMonth = (input = moment()) => {
  const firstDayOfMonth = input.clone().startOf('month');
  const firstDayOfWeek = firstDayOfMonth.clone().startOf('week');

  const offset = firstDayOfMonth.diff(firstDayOfWeek, 'days');

  return Math.ceil((input.date() + offset) / 7);
};

export const countWeekdayOccurrencesInMonth = (date) => {
  const m = moment(date);
  const weekDay = m.day();
  let count = 0;

  const month = m.month();
  const year = m.year();

  m.startOf('month');
  while (m.month() === month && m.year() === year) {
    if (m.day() === weekDay) {
      count++;
    }
    m.add(1, 'day');
  }

  return count;
};

export const areArraysEqual = (a1, a2) => {
  /* WARNING: arrays must not contain {objects} or behavior may be undefined */
  return JSON.stringify(a1) === JSON.stringify(a2);
};

export const getObjectDiff = (a, b) =>
  Object.entries(a).reduce(
    (ac, [k, v]) => (b[k] && b[k] !== v ? ((ac[k] = b[k]), ac) : ac),
    {},
  );

export const isObjectEqual = (value, other) => {
  // Get the value type
  var type = Object.prototype.toString.call(value);

  // If the two objects are not the same type, return false
  if (type !== Object.prototype.toString.call(other)) {
    return false;
  }

  // If items are not an object or array, return false
  if (['[object Array]', '[object Object]'].indexOf(type) < 0) {
    return false;
  }

  // Compare the length of the length of the two items
  var valueLen =
    type === '[object Array]' ? value.length : Object.keys(value).length;
  var otherLen =
    type === '[object Array]' ? other.length : Object.keys(other).length;
  if (valueLen !== otherLen) {
    return false;
  }
  // Compare two items
  var compare = function (item1, item2) {
    // Get the object type
    var itemType = Object.prototype.toString.call(item1);

    // If an object or array, compare recursively
    if (['[object Array]', '[object Object]'].indexOf(itemType) >= 0) {
      if (!isObjectEqual(item1, item2)) {
        return false;
      }
    }

    // Otherwise, do a simple comparison
    else {
      // If the two items are not the same type, return false
      if (itemType !== Object.prototype.toString.call(item2)) return false;

      // Else if it's a function, convert to a string and compare
      // Otherwise, just compare
      if (itemType === '[object Function]') {
        if (item1.toString() !== item2.toString()) return false;
      } else if (item1 !== item2) return false;
    }
  };

  // Compare properties
  if (type === '[object Array]') {
    for (var i = 0; i < valueLen; i++) {
      if (compare(value[i], other[i]) === false) return false;
    }
  } else {
    for (var key in value) {
      if (value.hasOwnProperty(key)) {
        if (compare(value[key], other[key]) === false) return false;
      }
    }
  }

  // If nothing failed, return true
  return true;
};

export const convertCartItemForKitchenSummary = (cartItem, userOrder) => {
  var addOnParsedList = [];
  if (cartItem.addOns) {
    for (var i = 0; i < cartItem.addOns.length; i++) {
      addOnParsedList.push({
        name: `${cartItem.addOns[i].name}: ${cartItem.addOns[i].choiceNames[0]}`,
        ...(cartItem.addOns[i].quantities && {
          quantity: cartItem.addOns[i].quantities[0],
        }),
      });
    }
  }

  addOnParsedList.sort((a, b) => (a.name > b.name ? 1 : -1));

  return {
    key:
      cartItem.itemId +
      cartItem.quantity.toString() +
      addOnParsedList
        .map(
          (addOn) =>
            addOn.name + (addOn.quantity ? addOn.quantity : 0).toString(),
        )
        .join(''),
    value: {
      name: cartItem.name,
      quantity: cartItem.quantity,
      addOnParsedList,

      userOrderId: userOrder.uniqueId,
      itemId: cartItem.itemId,
      cartItemDate: cartItem.cartItemDate,

      priceType: cartItem.priceType ? cartItem.priceType : PRODUCT_PRICE_TYPE.FIXED,
      unitType: cartItem.unitType ? cartItem.unitType : UNIT_TYPE.GRAM,
    },
  };
};

export const getWidth = (percentage) => {
  if (Dimensions.get('window').width > Dimensions.get('window').height) {
    return (Dimensions.get('window').height * percentage) / 100;
  }
  return (Dimensions.get('window').width * percentage) / 100;
};

export const getHeight = (percentage) => {
  if (Dimensions.get('window').width > Dimensions.get('window').height) {
    return (Dimensions.get('window').width * percentage) / 100;
  }
  return (Dimensions.get('window').height * percentage) / 100;
};

export const fixRNDimensions = async (orientation) => {
  // const windowDim = Dimensions.get('window');
  // const screenDim = Dimensions.get('screen');

  // logToFile(`fixRNDimensions`);
  // logToFile(`windowDim.width: ${windowDim.width}`);
  // logToFile(`windowDim.width: ${windowDim.height}`);
  // logToFile(`orientation: ${orientation}`);

  // console.log(`fixRNDimensions`);
  // console.log(`windowDim.width: ${windowDim.width}`);
  // console.log(`windowDim.width: ${windowDim.height}`);
  // console.log(`orientation: ${orientation}`);

  // if (
  //   (orientation.match(/LANDSCAPE/i) && windowDim.width < windowDim.height) ||
  //   (orientation.match(/PORTRAIT/i) && windowDim.width > windowDim.height)
  // ) {
  //   logToFile(`screenDim.width: ${screenDim.width}`);
  //   logToFile(`screenDim.width: ${screenDim.height}`);

  //   console.log(`screenDim.width: ${screenDim.width}`);
  //   console.log(`screenDim.width: ${screenDim.height}`);

  //   // console.log(orientation);
  //   // console.log('fixing dimensions after rotation', windowDim);
  //   Dimensions.set({
  //     screen: {
  //       ...screenDim,
  //       width: screenDim.height,
  //       height: screenDim.width,
  //     },
  //     window: {
  //       ...windowDim,
  //       width: windowDim.height,
  //       height: windowDim.width,
  //     },
  //   });
  // }
};

export const fixRNDimensionsOrientationChanged = async (orientation) => {
  // const windowDim = Dimensions.get('window');
  // const screenDim = Dimensions.get('screen');

  logToFile(`fixRNDimensionsOrientationChanged`);
  logToFile(`windowDim.width: ${windowDim.width}`);
  logToFile(`windowDim.width: ${windowDim.height}`);
  logToFile(`orientation: ${orientation}`);

  // if (global.simulateTabletMode &&
  //   global.windowWidthSimulate &&
  //   global.windowHeightSimulate) {

  //   let landscapeMode = false;

  //   if (
  //     (orientation.match(/LANDSCAPE/i))
  //   ) {
  //     landscapeMode = true;
  //   }

  //   performResize(
  //     {
  //       windowPhysicalPixels: {
  //         height: 2160,
  //         width: 1620,
  //         scale: 2,
  //       },
  //     },
  //     'iPad 9th Generation',
  //     false,
  //     false,
  //     landscapeMode,
  //     global.windowWidthOriginal,
  //     global.windowHeightOriginal,
  //     global.fontScaleOriginal,
  //   );

  //   // if (
  //   //   (orientation.match(/LANDSCAPE/i) && windowDim.width < windowDim.height) ||
  //   //   (orientation.match(/PORTRAIT/i) && windowDim.width > windowDim.height)
  //   // ) {
  //   //   logToFile(`screenDim.width: ${screenDim.width}`);
  //   //   logToFile(`screenDim.width: ${screenDim.height}`);

  //   //   // console.log(orientation);
  //   //   // console.log('fixing dimensions after rotation', windowDim);
  //   //   Dimensions.set({
  //   //     screen: {
  //   //       ...screenDim,
  //   //       width: screenDim.height,
  //   //       height: screenDim.width,
  //   //     },
  //   //     window: {
  //   //       ...windowDim,
  //   //       width: windowDim.height,
  //   //       height: windowDim.width,
  //   //     },
  //   //   });
  //   // }
  // }
};

export const fixRNDimensionsManual = async (orientation) => {
  const windowDim = Dimensions.get('window');
  const screenDim = Dimensions.get('screen');

  logToFile(`fixRNDimensionsManual`);
  logToFile(`windowDim.width: ${windowDim.width}`);
  logToFile(`windowDim.width: ${windowDim.height}`);
  logToFile(`orientation: ${orientation}`);

  if (
    (orientation.match(/LANDSCAPE/i) && windowDim.width < windowDim.height) ||
    (orientation.match(/PORTRAIT/i) && windowDim.width > windowDim.height)
  ) {
    logToFile(`screenDim.width: ${screenDim.width}`);
    logToFile(`screenDim.width: ${screenDim.height}`);

    // console.log(orientation);
    // console.log('fixing dimensions after rotation', windowDim);
    Dimensions.set({
      screen: {
        ...screenDim,
        width: screenDim.height,
        height: screenDim.width,
      },
      window: {
        ...windowDim,
        width: windowDim.height,
        height: windowDim.width,
      },
    });
  }
};

export const checkRNReload = async () => {
  const reloadApp = await AsyncStorage.getItem('reloadApp');

  // console.log('check refresh');
  // console.log(reloadApp);

  if (!reloadApp) {
    // console.log('refresh');
    // console.log(reloadApp);

    await saveRNReload();

    DevSettings.reload();

    // setTimeout(() => {
    //     DevSettings.reload();
    // }, 20000);
  }
};

export const saveRNReload = async () => {
  await AsyncStorage.setItem('reloadApp', '1');

  // console.log('save refresh');
};

export const removeRNReload = async () => {
  await AsyncStorage.removeItem('reloadApp');

  // console.log('remove refresh');
};

export const getCachedUrlContent = async (
  urlAsKey,
  expireInMinutes = 43800,
  object,
) => {
  var updatedAt = Date.now();
  if (object && object.updatedAt) {
    updatedAt = object.updatedAt;
  }

  let data = null;

  await AsyncStorage.getItem(urlAsKey, async (err, value) => {
    data = JSON.parse(value);

    // there is data in cache && cache is expired
    if (
      data !== null &&
      data.expireAt &&
      new Date(data.expireAt) < new Date()
    ) {
      //clear cache
      AsyncStorage.removeItem(urlAsKey);

      //update res to be null
      data = null;
    } else {
      // console.log('read data from cache  ');
    }
  });

  //update cache + set expire at date
  if (
    data === null ||
    (data && data.updatedAt && data.updatedAt !== updatedAt)
  ) {
    // console.log('cache new Date ');

    //fetch data

    await getImageFromFirebaseStorage(urlAsKey, async (parsedUrl) => {
      var apiRes = {};

      apiRes.parsedUrl = parsedUrl;
      apiRes.updatedAt = updatedAt;

      //set expire at
      apiRes.expireAt = getExpireDate(expireInMinutes);

      //stringify object
      const objectToStore = JSON.stringify(apiRes);

      //store object
      AsyncStorage.setItem(urlAsKey, objectToStore ? objectToStore : '');

      // console.log(apiRes.expireAt);

      return apiRes;
    });

    // data = fetch(urlAsKey).then((response) => response.json())
    //     .then(apiRes => {

    //         //set expire at
    //         apiRes.expireAt = getExpireDate(expireInMinutes);

    //         //stringify object
    //         const objectToStore = JSON.stringify(apiRes);

    //         //store object
    //         AsyncStorage.setItem(urlAsKey, objectToStore);

    //         // console.log(apiRes.expireAt);
    //         return apiRes;
    //     });
  }

  return data;
};

/**
 *
 * @param expireInMinutes
 * @returns {Date}
 */
const getExpireDate = (expireInMinutes) => {
  const now = new Date();
  let expireTime = new Date(now);
  expireTime.setMinutes(now.getMinutes() + expireInMinutes);
  return expireTime;
};

export const sliceUnicodeStringV2WithDots = function (str, n) {
  var r = /[^\x00-\xff]/g;
  if (str.replace(r, '@@').length <= n) {
    return str;
  }
  // var m = Math.floor(n / 2);
  var m = Math.ceil(n / 2);
  for (var i = m; i < str.length; i++) {
    if (str.substr(0, i).replace(r, '@@').length >= n) {
      return `${str.substr(0, i)}...`;
      //   return str.substr(0, i);
    }
  }
  return str;
};

export const updatePrintersAsyncStorage = async (printers) => {
  // await AsyncStorage.setItem('printerList', JSON.stringify([]));

  global.printerList = [];

  // if (!global.printerObjList) {
  //   // create it if not existed

  //   global.printerObjList = [];
  // }

  for (var i = 0; i < printers.length; i++) {
    const printerTemp = printers[i];

    //////////////////////////////////////////

    // 2025-04-22 - exclude printer changes

    if (global.excludePrinterIdDict[printerTemp.uniqueId]) {
      continue;
    }

    //////////////////////////////////////////

    // await AsyncStorage.setItem('printerIP', printerTemp.ip);

    // await AsyncStorage.setItem(printerTemp.area, printerTemp.ip);

    global.printerIP = printerTemp.ip;

    global[printerTemp.area] = printerTemp.ip;

    //////////////////////////////////////////

    // add to printer list, add/update obj as well

    // const printerListRaw = await AsyncStorage.getItem('printerList');

    // var printerList = [];
    // if (printerListRaw) {
    //   printerList = JSON.parse(printerListRaw);
    // }

    var printerList = global.printerList ? global.printerList : [];

    if (printerList.includes(printerTemp.uniqueId)) {
      // await AsyncStorage.setItem(
      //   printerTemp.uniqueId,
      //   JSON.stringify({
      //     ip: printerTemp.ip || '',
      //     area: printerTemp.area || '',
      //     types: printerTemp.types || [],
      //   }),
      // );

      global[printerTemp.uniqueId] = {
        ...printerTemp,

        uniqueId: printerTemp.uniqueId,

        ip: printerTemp.ip || '',
        area: printerTemp.area || '',
        types: printerTemp.types || [],

        blockKDCategoryIdList: printerTemp.blockKDCategoryIdList ? printerTemp.blockKDCategoryIdList : [],
        blockOSCategoryIdList: printerTemp.blockOSCategoryIdList ? printerTemp.blockOSCategoryIdList : [],
        blockReceiptCategoryIdList: printerTemp.blockReceiptCategoryIdList ? printerTemp.blockReceiptCategoryIdList : [],
        blockBDCategoryIdList: printerTemp.blockBDCategoryIdList ? printerTemp.blockBDCategoryIdList : [],

        blockKDOutletSectionIdList: printerTemp.blockKDOutletSectionIdList ? printerTemp.blockKDOutletSectionIdList : [],
        blockOSOutletSectionIdList: printerTemp.blockOSOutletSectionIdList ? printerTemp.blockOSOutletSectionIdList : [],
        blockReceiptOutletSectionIdList: printerTemp.blockReceiptOutletSectionIdList ? printerTemp.blockReceiptOutletSectionIdList : [],
        blockBDOutletSectionIdList: printerTemp.blockBDOutletSectionIdList ? printerTemp.blockBDOutletSectionIdList : [],

        paperWidth: printerTemp.paperWidth ? printerTemp.paperWidth : PRINTER_PAPER_WIDTH._80MM,

        kdVariation: printerTemp.kdVariation ? printerTemp.kdVariation : KD_PRINT_VARIATION.SUMMARY,

        userPriority: printerTemp.userPriority ? printerTemp.userPriority : PRINTER_USER_PRIORITY.NORMAL,

        kdOptionsDeliverReject: printerTemp.kdOptionsDeliverReject ? printerTemp.kdOptionsDeliverReject : KD_OPTIONS_DELIVER_REJECT.DELIVERED_REJECTED_ITEMS,

        groupList: printerTemp.groupList ? printerTemp.groupList.map(group => group.values) : [],
        groupName: printerTemp.groupName ? printerTemp.groupName : '',

        osPrintTimes: printerTemp.osPrintTimes ? printerTemp.osPrintTimes : '1',

        uniqueId: printerTemp.uniqueId,

        cooldownTime: printerTemp.cooldownTime ? printerTemp.cooldownTime : 1000,
        connectingTime: printerTemp.connectingTime ? printerTemp.connectingTime : 1500,
        printDelayTime: printerTemp.printDelayTime ? printerTemp.printDelayTime : 0,

        waitTimeAfterRc: printerTemp.waitTimeAfterRc ? printerTemp.waitTimeAfterRc : 50,

        rtIosD: printerTemp.rtIosD ? printerTemp.rtIosD : 4,
        rtAndD: printerTemp.rtAndD ? printerTemp.rtAndD : 4,
        rtIosId: printerTemp.rtIosId ? printerTemp.rtIosId : 5,
        rtAndId: printerTemp.rtAndId ? printerTemp.rtAndId : 5,
        rtIosRc: printerTemp.rtIosRc ? printerTemp.rtIosRc : 3,
        rtAndRc: printerTemp.rtAndRc ? printerTemp.rtAndRc : 3,

        initAfterRc: printerTemp.initAfterRc ? printerTemp.initAfterRc : false,
        selectAfterRc: printerTemp.selectAfterRc ? printerTemp.selectAfterRc : false,

        bs: printerTemp.bs !== undefined ? printerTemp.bs : true, // beep sound, default is true

        wlNumAnd: printerTemp.wlNumAnd ? printerTemp.wlNumAnd : 0,
        wlNumIos: printerTemp.wlNumIos ? printerTemp.wlNumIos : 0,

        cutF: printerTemp.cutF ? printerTemp.cutF : false,

        codepage: printerTemp.codepage ? printerTemp.codepage : '',
        beep: printerTemp.beep ? printerTemp.beep : '',
        encoding: printerTemp.encoding ? printerTemp.encoding : '',
        cpPre: printerTemp.cpPre ? printerTemp.cpPre : '',

        encodingStr: printerTemp.encodingStr ? printerTemp.encodingStr : '',

        noHex: printerTemp.noHex !== undefined ? printerTemp.noHex : false,

        deviceType: printerTemp.deviceType ? printerTemp.deviceType : PRINTER_DEVICE_TYPE.LAN,

        rnpIw: printerTemp.rnpIw !== undefined ? printerTemp.rnpIw : 250,
        rnpPx: printerTemp.rnpPx !== undefined ? printerTemp.rnpPx : 600,
        rnpPy: printerTemp.rnpPy !== undefined ? printerTemp.rnpPy : 0,

        qrH: printerTemp.qrH ? printerTemp.qrH : 300,
        qrW: printerTemp.qrW ? printerTemp.qrW : 300,
        qrPT: printerTemp.qrPT ? printerTemp.qrPT : 25,
        qrPL: printerTemp.qrPL ? printerTemp.qrPL : 25,
        qrPB: printerTemp.qrPB ? printerTemp.qrPB : 25,
        qrPR: printerTemp.qrPR ? printerTemp.qrPR : 25,

        qrWI: printerTemp.qrWI ? printerTemp.qrWI : 300,

        reconReceipt: printerTemp.reconReceipt ? printerTemp.reconReceipt : true,
        reconReceiptD: printerTemp.reconReceiptD ? printerTemp.reconReceiptD : false,

        commandType: printerTemp.commandType !== undefined ? printerTemp.commandType : PRINTER_COMMAND_TYPE.ESCPOS,
        labelWidth: printerTemp.labelWidth !== undefined ? printerTemp.labelWidth : 70,
        labelHeight: printerTemp.labelHeight !== undefined ? printerTemp.labelHeight : 40,
        labelGap: printerTemp.labelGap !== undefined ? printerTemp.labelGap : 2,
        labelEncoding: printerTemp.labelEncoding !== undefined ? printerTemp.labelEncoding : '',

        modelType: printerTemp.modelType !== undefined ? printerTemp.modelType : '',

        cmdCd: printerTemp.cmdCd !== undefined ? printerTemp.cmdCd : '',

        /////////////////////////////////////////

        // 2025-01-27 - open cash drawer times

        ocdTimes: printerTemp.ocdTimes !== undefined ? printerTemp.ocdTimes : 1,

        /////////////////////////////////////////

        // 2025-02-01 - printer sent before, printer sent after

        psb: printerTemp.psb !== undefined ? printerTemp.psb : '',
        psa: printerTemp.psa !== undefined ? printerTemp.psa : '',

        // 2025-02-04 - printer sent before. printer sent after (cash drawer)

        // psbcd: printerTemp.psbcd !== undefined ? printerTemp.psbcd : '',
        // psacd: printerTemp.psacd !== undefined ? printerTemp.psacd : '',

        cmdCdTimeout: printerTemp.cmdCdTimeout !== undefined ? printerTemp.cmdCdTimeout : 0,

        // pReconTime: printerTemp.pReconTime !== undefined ? printerTemp.pReconTime : 10,
      };
    } else {
      // await AsyncStorage.setItem(
      //   printerTemp.uniqueId,
      //   JSON.stringify({
      //     ip: printerTemp.ip || '',
      //     area: printerTemp.area || '',
      //     types: printerTemp.types || [],
      //   }),
      // );

      global[printerTemp.uniqueId] = {
        ...printerTemp,

        uniqueId: printerTemp.uniqueId,

        ip: printerTemp.ip || '',
        area: printerTemp.area || '',
        types: printerTemp.types || [],

        blockKDCategoryIdList: printerTemp.blockKDCategoryIdList ? printerTemp.blockKDCategoryIdList : [],
        blockOSCategoryIdList: printerTemp.blockOSCategoryIdList ? printerTemp.blockOSCategoryIdList : [],
        blockReceiptCategoryIdList: printerTemp.blockReceiptCategoryIdList ? printerTemp.blockReceiptCategoryIdList : [],
        blockBDCategoryIdList: printerTemp.blockBDCategoryIdList ? printerTemp.blockBDCategoryIdList : [],

        blockKDOutletSectionIdList: printerTemp.blockKDOutletSectionIdList ? printerTemp.blockKDOutletSectionIdList : [],
        blockOSOutletSectionIdList: printerTemp.blockOSOutletSectionIdList ? printerTemp.blockOSOutletSectionIdList : [],
        blockReceiptOutletSectionIdList: printerTemp.blockReceiptOutletSectionIdList ? printerTemp.blockReceiptOutletSectionIdList : [],
        blockBDOutletSectionIdList: printerTemp.blockBDOutletSectionIdList ? printerTemp.blockBDOutletSectionIdList : [],

        paperWidth: printerTemp.paperWidth ? printerTemp.paperWidth : PRINTER_PAPER_WIDTH._80MM,

        kdVariation: printerTemp.kdVariation ? printerTemp.kdVariation : KD_PRINT_VARIATION.SUMMARY,

        userPriority: printerTemp.userPriority ? printerTemp.userPriority : PRINTER_USER_PRIORITY.NORMAL,

        kdOptionsDeliverReject: printerTemp.kdOptionsDeliverReject ? printerTemp.kdOptionsDeliverReject : KD_OPTIONS_DELIVER_REJECT.DELIVERED_REJECTED_ITEMS,

        groupList: printerTemp.groupList ? printerTemp.groupList.map(group => group.values) : [],
        groupName: printerTemp.groupName ? printerTemp.groupName : '',

        osPrintTimes: printerTemp.osPrintTimes ? printerTemp.osPrintTimes : '1',

        uniqueId: printerTemp.uniqueId,

        cooldownTime: printerTemp.cooldownTime ? printerTemp.cooldownTime : 1000,
        connectingTime: printerTemp.connectingTime ? printerTemp.connectingTime : 1500,
        printDelayTime: printerTemp.printDelayTime ? printerTemp.printDelayTime : 0,

        waitTimeAfterRc: printerTemp.waitTimeAfterRc ? printerTemp.waitTimeAfterRc : 50,

        rtIosD: printerTemp.rtIosD ? printerTemp.rtIosD : 4,
        rtAndD: printerTemp.rtAndD ? printerTemp.rtAndD : 4,
        rtIosId: printerTemp.rtIosId ? printerTemp.rtIosId : 5,
        rtAndId: printerTemp.rtAndId ? printerTemp.rtAndId : 5,
        rtIosRc: printerTemp.rtIosRc ? printerTemp.rtIosRc : 3,
        rtAndRc: printerTemp.rtAndRc ? printerTemp.rtAndRc : 3,

        initAfterRc: printerTemp.initAfterRc ? printerTemp.initAfterRc : false,
        selectAfterRc: printerTemp.selectAfterRc ? printerTemp.selectAfterRc : false,

        bs: printerTemp.bs !== undefined ? printerTemp.bs : true, // beep sound, default is true

        wlNumAnd: printerTemp.wlNumAnd ? printerTemp.wlNumAnd : 0,
        wlNumIos: printerTemp.wlNumIos ? printerTemp.wlNumIos : 0,

        cutF: printerTemp.cutF ? printerTemp.cutF : false,

        codepage: printerTemp.codepage ? printerTemp.codepage : '',
        beep: printerTemp.beep ? printerTemp.beep : '',
        encoding: printerTemp.encoding ? printerTemp.encoding : '',
        cpPre: printerTemp.cpPre ? printerTemp.cpPre : '',

        encodingStr: printerTemp.encodingStr ? printerTemp.encodingStr : '',

        noHex: printerTemp.noHex !== undefined ? printerTemp.noHex : false,

        deviceType: printerTemp.deviceType ? printerTemp.deviceType : PRINTER_DEVICE_TYPE.LAN,

        rnpIw: printerTemp.rnpIw !== undefined ? printerTemp.rnpIw : 250,
        rnpPx: printerTemp.rnpPx !== undefined ? printerTemp.rnpPx : 600,
        rnpPy: printerTemp.rnpPy !== undefined ? printerTemp.rnpPy : 0,

        qrH: printerTemp.qrH ? printerTemp.qrH : 300,
        qrW: printerTemp.qrW ? printerTemp.qrW : 300,
        qrPT: printerTemp.qrPT ? printerTemp.qrPT : 25,
        qrPL: printerTemp.qrPL ? printerTemp.qrPL : 25,
        qrPB: printerTemp.qrPB ? printerTemp.qrPB : 25,
        qrPR: printerTemp.qrPR ? printerTemp.qrPR : 25,

        qrWI: printerTemp.qrWI ? printerTemp.qrWI : 300,

        reconReceipt: printerTemp.reconReceipt ? printerTemp.reconReceipt : true,
        reconReceiptD: printerTemp.reconReceiptD ? printerTemp.reconReceiptD : false,

        commandType: printerTemp.commandType !== undefined ? printerTemp.commandType : PRINTER_COMMAND_TYPE.ESCPOS,
        labelWidth: printerTemp.labelWidth !== undefined ? printerTemp.labelWidth : 70,
        labelHeight: printerTemp.labelHeight !== undefined ? printerTemp.labelHeight : 40,
        labelGap: printerTemp.labelGap !== undefined ? printerTemp.labelGap : 2,
        labelEncoding: printerTemp.labelEncoding !== undefined ? printerTemp.labelEncoding : '',

        modelType: printerTemp.modelType !== undefined ? printerTemp.modelType : '',

        cmdCd: printerTemp.cmdCd !== undefined ? printerTemp.cmdCd : '',

        /////////////////////////////////////////

        // 2025-01-27 - open cash drawer times

        ocdTimes: printerTemp.ocdTimes !== undefined ? printerTemp.ocdTimes : 1,

        /////////////////////////////////////////

        // 2025-02-01 - printer sent before, printer sent after

        psb: printerTemp.psb !== undefined ? printerTemp.psb : '',
        psa: printerTemp.psa !== undefined ? printerTemp.psa : '',

        // 2025-02-04 - printer sent before. printer sent after (cash drawer)

        // psbcd: printerTemp.psbcd !== undefined ? printerTemp.psbcd : '',
        // psacd: printerTemp.psacd !== undefined ? printerTemp.psacd : '',

        cmdCdTimeout: printerTemp.cmdCdTimeout !== undefined ? printerTemp.cmdCdTimeout : 0,

        // pReconTime: printerTemp.pReconTime !== undefined ? printerTemp.pReconTime : 10,
      };

      printerList.push(printerTemp.uniqueId);

      // await AsyncStorage.setItem('printerList', JSON.stringify(printerList));

      global.printerList = printerList;
    }

    ///////////////////////////////

    if (!global.printerObjList.find(printerObj =>
      printerObj.uniqueId === printerTemp.uniqueId)) {
      // means not existed yet

      global.printerObjList.push({
        ...printerTemp,

        uniqueId: printerTemp.uniqueId,

        area: printerTemp.area,
        name: printerTemp.name,

        ip: printerTemp.ip,
        uniqueId: printerTemp.uniqueId,
        tasks: [],

        priority: 0,
        frequency: 0,

        userPriority: printerTemp.userPriority ? printerTemp.userPriority : PRINTER_USER_PRIORITY.NORMAL,
        kdOptionsDeliverReject: printerTemp.kdOptionsDeliverReject ? printerTemp.kdOptionsDeliverReject : KD_OPTIONS_DELIVER_REJECT.DELIVERED_REJECTED_ITEMS,
        osPrintTimes: printerTemp.osPrintTimes ? printerTemp.osPrintTimes : '1',

        kdVariation: printerTemp.kdVariation ? printerTemp.kdVariation : KD_PRINT_VARIATION.SUMMARY,

        cooldownTime: printerTemp.cooldownTime ? printerTemp.cooldownTime : 1000,
        connectingTime: printerTemp.connectingTime ? printerTemp.connectingTime : 1500,
        printDelayTime: printerTemp.printDelayTime ? printerTemp.printDelayTime : 0,

        waitTimeAfterRc: printerTemp.waitTimeAfterRc ? printerTemp.waitTimeAfterRc : 50,

        rtIosD: printerTemp.rtIosD ? printerTemp.rtIosD : 4,
        rtAndD: printerTemp.rtAndD ? printerTemp.rtAndD : 4,
        rtIosId: printerTemp.rtIosId ? printerTemp.rtIosId : 5,
        rtAndId: printerTemp.rtAndId ? printerTemp.rtAndId : 5,
        rtIosRc: printerTemp.rtIosRc ? printerTemp.rtIosRc : 3,
        rtAndRc: printerTemp.rtAndRc ? printerTemp.rtAndRc : 3,

        initAfterRc: printerTemp.initAfterRc ? printerTemp.initAfterRc : false,
        selectAfterRc: printerTemp.selectAfterRc ? printerTemp.selectAfterRc : false,

        bs: printerTemp.bs !== undefined ? printerTemp.bs : true, // beep sound, default is true

        blockKDCategoryIdList: printerTemp.blockKDCategoryIdList ? printerTemp.blockKDCategoryIdList : [],
        blockOSCategoryIdList: printerTemp.blockOSCategoryIdList ? printerTemp.blockOSCategoryIdList : [],
        blockReceiptCategoryIdList: printerTemp.blockReceiptCategoryIdList ? printerTemp.blockReceiptCategoryIdList : [],
        blockBDCategoryIdList: printerTemp.blockBDCategoryIdList ? printerTemp.blockBDCategoryIdList : [],

        blockKDOutletSectionIdList: printerTemp.blockKDOutletSectionIdList ? printerTemp.blockKDOutletSectionIdList : [],
        blockOSOutletSectionIdList: printerTemp.blockOSOutletSectionIdList ? printerTemp.blockOSOutletSectionIdList : [],
        blockReceiptOutletSectionIdList: printerTemp.blockReceiptOutletSectionIdList ? printerTemp.blockReceiptOutletSectionIdList : [],
        blockBDOutletSectionIdList: printerTemp.blockBDOutletSectionIdList ? printerTemp.blockBDOutletSectionIdList : [],

        wlNumAnd: printerTemp.wlNumAnd ? printerTemp.wlNumAnd : 0,
        wlNumIos: printerTemp.wlNumIos ? printerTemp.wlNumIos : 0,

        cutF: printerTemp.cutF ? printerTemp.cutF : false,

        codepage: printerTemp.codepage ? printerTemp.codepage : '',
        beep: printerTemp.beep ? printerTemp.beep : '',
        encoding: printerTemp.encoding ? printerTemp.encoding : '',
        cpPre: printerTemp.cpPre ? printerTemp.cpPre : '',

        encodingStr: printerTemp.encodingStr ? printerTemp.encodingStr : '',

        noHex: printerTemp.noHex !== undefined ? printerTemp.noHex : false,

        deviceType: printerTemp.deviceType ? printerTemp.deviceType : PRINTER_DEVICE_TYPE.LAN,

        rnpIw: printerTemp.rnpIw !== undefined ? printerTemp.rnpIw : 250,
        rnpPx: printerTemp.rnpPx !== undefined ? printerTemp.rnpPx : 600,
        rnpPy: printerTemp.rnpPy !== undefined ? printerTemp.rnpPy : 0,

        qrH: printerTemp.qrH ? printerTemp.qrH : 300,
        qrW: printerTemp.qrW ? printerTemp.qrW : 300,
        qrPT: printerTemp.qrPT ? printerTemp.qrPT : 25,
        qrPL: printerTemp.qrPL ? printerTemp.qrPL : 25,
        qrPB: printerTemp.qrPB ? printerTemp.qrPB : 25,
        qrPR: printerTemp.qrPR ? printerTemp.qrPR : 25,

        qrWI: printerTemp.qrWI ? printerTemp.qrWI : 300,

        reconReceipt: printerTemp.reconReceipt ? printerTemp.reconReceipt : true,
        reconReceiptD: printerTemp.reconReceiptD ? printerTemp.reconReceiptD : false,

        commandType: printerTemp.commandType !== undefined ? printerTemp.commandType : PRINTER_COMMAND_TYPE.ESCPOS,
        labelWidth: printerTemp.labelWidth !== undefined ? printerTemp.labelWidth : 70,
        labelHeight: printerTemp.labelHeight !== undefined ? printerTemp.labelHeight : 40,
        labelGap: printerTemp.labelGap !== undefined ? printerTemp.labelGap : 2,
        labelEncoding: printerTemp.labelEncoding !== undefined ? printerTemp.labelEncoding : '',

        modelType: printerTemp.modelType !== undefined ? printerTemp.modelType : '',

        cmdCd: printerTemp.cmdCd !== undefined ? printerTemp.cmdCd : '',

        /////////////////////////////////////////

        // 2025-01-27 - open cash drawer times

        ocdTimes: printerTemp.ocdTimes !== undefined ? printerTemp.ocdTimes : 1,

        /////////////////////////////////////////

        // 2025-02-01 - printer sent before, printer sent after

        psb: printerTemp.psb !== undefined ? printerTemp.psb : '',
        psa: printerTemp.psa !== undefined ? printerTemp.psa : '',

        // 2025-02-04 - printer sent before. printer sent after (cash drawer)

        // psbcd: printerTemp.psbcd !== undefined ? printerTemp.psbcd : '',
        // psacd: printerTemp.psacd !== undefined ? printerTemp.psacd : '',

        cmdCdTimeout: printerTemp.cmdCdTimeout !== undefined ? printerTemp.cmdCdTimeout : 0,

        // pReconTime: printerTemp.pReconTime !== undefined ? printerTemp.pReconTime : 10,
      });
    }
    else {
      // if existed, just do nothing

      // 2023-05-09 - To support live changes (without the needs to restart the app)

      var foundIndex = global.printerObjList.findIndex(printerObj => printerObj.uniqueId === printerTemp.uniqueId);

      // global.printerObjList[foundIndex] = {
      //   ...printerTemp,
      // };

      global.printerObjList[foundIndex].uniqueId = printerTemp.uniqueId;

      global.printerObjList[foundIndex].area = printerTemp.area;
      global.printerObjList[foundIndex].name = printerTemp.name;

      global.printerObjList[foundIndex].ip = printerTemp.ip;
      global.printerObjList[foundIndex].types = printerTemp.types || [],
        global.printerObjList[foundIndex].uniqueId = printerTemp.uniqueId;

      global.printerObjList[foundIndex].blockKDCategoryIdList = printerTemp.blockKDCategoryIdList ? printerTemp.blockKDCategoryIdList : [];
      global.printerObjList[foundIndex].blockOSCategoryIdList = printerTemp.blockOSCategoryIdList ? printerTemp.blockOSCategoryIdList : [];
      global.printerObjList[foundIndex].blockReceiptCategoryIdList = printerTemp.blockReceiptCategoryIdList ? printerTemp.blockReceiptCategoryIdList : [];
      global.printerObjList[foundIndex].blockBDCategoryIdList = printerTemp.blockBDCategoryIdList ? printerTemp.blockBDCategoryIdList : [];

      global.printerObjList[foundIndex].blockKDOutletSectionIdList = printerTemp.blockKDOutletSectionIdList ? printerTemp.blockKDOutletSectionIdList : [];
      global.printerObjList[foundIndex].blockOSOutletSectionIdList = printerTemp.blockOSOutletSectionIdList ? printerTemp.blockOSOutletSectionIdList : [];
      global.printerObjList[foundIndex].blockReceiptOutletSectionIdList = printerTemp.blockReceiptOutletSectionIdList ? printerTemp.blockReceiptOutletSectionIdList : [];
      global.printerObjList[foundIndex].blockBDOutletSectionIdList = printerTemp.blockBDOutletSectionIdList ? printerTemp.blockBDOutletSectionIdList : [];

      global.printerObjList[foundIndex].paperWidth = printerTemp.paperWidth ? printerTemp.paperWidth : PRINTER_PAPER_WIDTH._80MM,

        global.printerObjList[foundIndex].kdVariation = printerTemp.kdVariation ? printerTemp.kdVariation : KD_PRINT_VARIATION.SUMMARY,

        global.printerObjList[foundIndex].userPriority = printerTemp.userPriority ? printerTemp.userPriority : PRINTER_USER_PRIORITY.NORMAL;
      global.printerObjList[foundIndex].kdOptionsDeliverReject = printerTemp.kdOptionsDeliverReject ? printerTemp.kdOptionsDeliverReject : KD_OPTIONS_DELIVER_REJECT.DELIVERED_REJECTED_ITEMS;

      global.printerObjList[foundIndex].groupList = printerTemp.groupList ? printerTemp.groupList.map(group => group.values) : [];
      global.printerObjList[foundIndex].groupName = printerTemp.groupName ? printerTemp.groupName : '';

      global.printerObjList[foundIndex].osPrintTimes = printerTemp.osPrintTimes ? printerTemp.osPrintTimes : '1';

      global.printerObjList[foundIndex].cooldownTime = printerTemp.cooldownTime ? printerTemp.cooldownTime : 1000;
      global.printerObjList[foundIndex].connectingTime = printerTemp.connectingTime ? printerTemp.connectingTime : 1500;
      global.printerObjList[foundIndex].printDelayTime = printerTemp.printDelayTime ? printerTemp.printDelayTime : 0;

      global.printerObjList[foundIndex].waitTimeAfterRc = printerTemp.waitTimeAfterRc ? printerTemp.waitTimeAfterRc : 50;

      global.printerObjList[foundIndex].rtIosD = printerTemp.rtIosD ? printerTemp.rtIosD : 4;
      global.printerObjList[foundIndex].rtAndD = printerTemp.rtAndD ? printerTemp.rtAndD : 4;
      global.printerObjList[foundIndex].rtIosId = printerTemp.rtIosId ? printerTemp.rtIosId : 5;
      global.printerObjList[foundIndex].rtAndId = printerTemp.rtAndId ? printerTemp.rtAndId : 5;
      global.printerObjList[foundIndex].rtIosRc = printerTemp.rtIosRc ? printerTemp.rtIosRc : 3;
      global.printerObjList[foundIndex].rtAndRc = printerTemp.rtAndRc ? printerTemp.rtAndRc : 3;

      global.printerObjList[foundIndex].initAfterRc = printerTemp.initAfterRc ? printerTemp.initAfterRc : false;
      global.printerObjList[foundIndex].selectAfterRc = printerTemp.selectAfterRc ? printerTemp.selectAfterRc : false;

      global.printerObjList[foundIndex].bs = printerTemp.bs !== undefined ? printerTemp.bs : true;

      global.printerObjList[foundIndex].wlNumAnd = printerTemp.wlNumAnd ? printerTemp.wlNumAnd : 0;
      global.printerObjList[foundIndex].wlNumIos = printerTemp.wlNumIos ? printerTemp.wlNumIos : 0;

      global.printerObjList[foundIndex].cutF = printerTemp.cutF ? printerTemp.cutF : false;

      global.printerObjList[foundIndex].codepage = printerTemp.codepage ? printerTemp.codepage : false;
      global.printerObjList[foundIndex].beep = printerTemp.beep ? printerTemp.beep : false;
      global.printerObjList[foundIndex].encoding = printerTemp.encoding ? printerTemp.encoding : false;
      global.printerObjList[foundIndex].cpPre = printerTemp.cpPre ? printerTemp.cpPre : '';

      global.printerObjList[foundIndex].encodingStr = printerTemp.encodingStr ? printerTemp.encodingStr : '';

      global.printerObjList[foundIndex].noHex = printerTemp.noHex ? printerTemp.noHex : false;

      global.printerObjList[foundIndex].deviceType = printerTemp.deviceType ? printerTemp.deviceType : PRINTER_DEVICE_TYPE.LAN;

      global.printerObjList[foundIndex].rnpIw = printerTemp.rnpIw ? printerTemp.rnpIw : 250;
      global.printerObjList[foundIndex].rnpPx = printerTemp.rnpPx ? printerTemp.rnpPx : 600;
      global.printerObjList[foundIndex].rnpPy = printerTemp.rnpPy ? printerTemp.rnpPy : 0;

      global.printerObjList[foundIndex].qrH = printerTemp.qrH ? printerTemp.qrH : 300;
      global.printerObjList[foundIndex].qrW = printerTemp.qrW ? printerTemp.qrW : 300;
      global.printerObjList[foundIndex].qrPT = printerTemp.qrPT ? printerTemp.qrPT : 25;
      global.printerObjList[foundIndex].qrPL = printerTemp.qrPL ? printerTemp.qrPL : 25;
      global.printerObjList[foundIndex].qrPB = printerTemp.qrPB ? printerTemp.qrPB : 25;
      global.printerObjList[foundIndex].qrPR = printerTemp.qrPR ? printerTemp.qrPR : 25;

      global.printerObjList[foundIndex].qrWI = printerTemp.qrWI ? printerTemp.qrWI : 300;

      global.printerObjList[foundIndex].reconReceipt = printerTemp.reconReceipt ? printerTemp.reconReceipt : true;
      global.printerObjList[foundIndex].reconReceiptD = printerTemp.reconReceiptD ? printerTemp.reconReceiptD : false;

      global.printerObjList[foundIndex].commandType = printerTemp.commandType !== undefined ? printerTemp.commandType : PRINTER_COMMAND_TYPE.ESCPOS;
      global.printerObjList[foundIndex].labelWidth = printerTemp.labelWidth !== undefined ? printerTemp.labelWidth : 70;
      global.printerObjList[foundIndex].labelHeight = printerTemp.labelHeight !== undefined ? printerTemp.labelHeight : 40;
      global.printerObjList[foundIndex].labelGap = printerTemp.labelGap !== undefined ? printerTemp.labelGap : 2;
      global.printerObjList[foundIndex].labelEncoding = printerTemp.labelEncoding !== undefined ? printerTemp.labelEncoding : '';

      global.printerObjList[foundIndex].modelType = printerTemp.modelType !== undefined ? printerTemp.modelType : '';

      global.printerObjList[foundIndex].cmdCd = printerTemp.cmdCd !== undefined ? printerTemp.cmdCd : '';

      global.printerObjList[foundIndex].ocdTimes = printerTemp.ocdTimes !== undefined ? printerTemp.ocdTimes : 1;

      global.printerObjList[foundIndex].psb = printerTemp.psb !== undefined ? printerTemp.psb : '';
      global.printerObjList[foundIndex].psa = printerTemp.psa !== undefined ? printerTemp.psa : '';

      global.printerObjList[foundIndex].cmdCdTimeout = printerTemp.cmdCdTimeout !== undefined ? printerTemp.cmdCdTimeout : 0;
    }

    ///////////////////////////////
  }

  // for (var i = 0; i < printers.length; i++) {
  //   const printerTemp = printers[i];

  //   await connectToPrinter(printerTemp.ip || '');
  // }
};

export const updateOutletAsyncStorage = async (outlet) => {
  // await AsyncStorage.setItem(
  //   'currOutlet',
  //   JSON.stringify({
  //     taxRate: outlet.taxRate,
  //     scRate: outlet.scRate,
  //     taxActive: outlet.taxActive,
  //     scActive: outlet.scActive,
  //     scOrderTypes: outlet.scOrderTypes,
  //   }),
  // );

  /////////////////////////////////////////

  try {
    global.currOutlet = {
      ...outlet,
      // ...JSON.parse(JSON.prune(outlet)),

      name: outlet.name ? outlet.name : '',

      uniqueId: outlet.uniqueId ? outlet.uniqueId : '',
      merchantId: outlet.merchantId ? outlet.merchantId : '',

      subdomain: outlet.subdomain ? outlet.subdomain : '',

      taxRate: outlet.taxRate,
      taxNum: outlet.taxNum ? outlet.taxNum : '',
      scRate: outlet.scRate,
      taxActive: outlet.taxActive,
      taxOrderTypes: outlet.taxOrderTypes ? outlet.taxOrderTypes : '',
      scActive: outlet.scActive,
      scOrderTypes: outlet.scOrderTypes,
      scOrderTypeDetails: outlet.scOrderTypeDetails ? outlet.scOrderTypeDetails : [ORDER_TYPE_DETAILS.POS, ORDER_TYPE_DETAILS.QR],
      scName: outlet.scName ? outlet.scName : '',

      scRateOtherD: outlet.scRateOtherD ? outlet.scRateOtherD : 0,
      scActiveOtherD: outlet.scActiveOtherD ? outlet.scActiveOtherD : false,
      scNameOtherD: outlet.scNameOtherD ? outlet.scNameOtherD : '',

      reportDisplayType: outlet.reportDisplayType ? outlet.reportDisplayType : REPORT_DISPLAY_TYPE.DAY,

      orderPlaceLimit: outlet.orderPlaceLimit ? outlet.orderPlaceLimit : 50,
      orderPayLimit: outlet.orderPayLimit ? outlet.orderPayLimit : 50,

      pLockTime: outlet.pLockTime ? outlet.pLockTime : 50,
      pReconTime: outlet.pReconTime !== undefined ? outlet.pReconTime : 1,

      logFile: outlet.logFile ? outlet.logFile : false,

      outletId: outlet.uniqueId,
      merchantId: outlet.merchantId,

      rtIos: outlet.rtIos ? outlet.rtIos : 2,
      rtAnd: outlet.rtAnd ? outlet.rtAnd : 2,

      bmtwIos: outlet.bmtwIos ? outlet.bmtwIos : 500,
      bmtwAnd: outlet.bmtwAnd ? outlet.bmtwAnd : 500,

      encoding: outlet.encoding ? outlet.encoding : '', // GB18030
      ...(outlet.codepage !== undefined) && {
        codepage: outlet.codepage,
      },
      noHex: outlet.noHex !== undefined ? outlet.noHex : false,

      /////////////////////////////////////////

      // 2024-07-19 - specific orders for cut options on different platforms

      ...(Platform.OS === 'android') && {
        cut: true, // android almost needed 'cut' to cut paper
      },
      ...(outlet.cut !== undefined) && {
        cut: outlet.cut,
      },
      ...(Platform.OS === 'ios') && {
        cut: false, // for ios, 'cut' option is not usable
      },

      /////////////////////////////////////////

      ...(outlet.ei !== undefined) && {
        ei: outlet.ei,
      },

      ...(outlet.eiPTin !== undefined) && {
        eiPTin: outlet.eiPTin,
      },

      ...(outlet.eiPQr !== undefined) && {
        eiPQr: outlet.eiPQr,
      },

      ...(outlet.pctoAnd !== undefined) && {
        pctoAnd: outlet.pctoAnd, // printer connecting timeout android
      },
      ...(outlet.pctoIos !== undefined) && {
        pctoIos: outlet.pctoIos, // printer connecting timeout ios
      },

      ...(outlet.pccHttp !== undefined) && {
        pccHttp: outlet.pccHttp, // printer connecting check http
      },

      prevWTAlg: outlet.prevWTAlg ? outlet.prevWTAlg : false,

      reprintOff: outlet.reprintOff ? outlet.reprintOff : false,

      icitnTime: outlet.icitnTime ? outlet.icitnTime : 3, // isCheckingInterruptedTasksNow

      qrH: outlet.qrH ? outlet.qrH : 300,
      qrW: outlet.qrW ? outlet.qrW : 300,
      qrWsunmi: outlet.qrWsunmi ? outlet.qrWsunmi : 300,
      qrWimin: outlet.qrWimin ? outlet.qrWimin : 300,
      qrPT: outlet.qrPT ? outlet.qrPT : 25,
      qrPL: outlet.qrPL ? outlet.qrPT : 25,
      qrPB: outlet.qrPB ? outlet.qrPB : 25,
      qrPR: outlet.qrPR ? outlet.qrPR : 25,

      qrWI: outlet.qrWI ? outlet.qrWI : 300,

      ssUOAT: outlet.ssUOAT ? outlet.ssUOAT : 500,
      ssUOMT: outlet.ssUOMT ? outlet.ssUOMT : 3000,
      ssUOMDT: outlet.ssUOMDT ? outlet.ssUOMDT : 1000,

      ciNum: outlet.ciNum ? outlet.ciNum : 20, // max composite inventory records limit to retrieve (ex: purchase order, etc)
      iNum: outlet.iNum ? outlet.iNum : 20, // max composite inventory records limit to retrieve (ex: purchase order, etc)

      payPRNum: outlet.payPRNum ? outlet.payPRNum : 1, // when paying, print receipt times (default is 1 only)
      payDirect: outlet.payDirect !== undefined ? outlet.payDirect : false, // when paying, skip the receipt summary (default is false)
      payDirectWait: outlet.payDirectWait ? outlet.payDirectWait : 0, // when paying directly, time to wait before trigger pay function (default is 0 ms)
      payDirectWaitAfter: outlet.payDirectWaitAfter ? outlet.payDirectWaitAfter : 5000, // after payment done, there will be a certain delay for order status, from 'pay' to 'paid', this delay is used to block the user interaction with pay button during payment processing

      pSR: outlet.pSR !== undefined ? outlet.pSR : true, // print shift report when close shift, default is true

      noSignoutFN: outlet.noSignoutFN !== undefined ? outlet.noSignoutFN : false, // no signed out when failed network
      noSignoutC: outlet.noSignoutC !== undefined ? outlet.noSignoutC : false, // no signed out when custom logic
      noSignoutI: outlet.noSignoutI !== undefined ? outlet.noSignoutI : false, // no signed out when idle

      dcocdwp: outlet.dcocdwp !== undefined ? outlet.dcocdwp : false, // double checking open cash drawer when pay (if saved settings matched)

      smsKds: outlet.smsKds !== undefined ? outlet.smsKds : false, // for whether to send sms for kds operations or not

      /////////////////////////////////////////

      dineInRequiredAuthorization: outlet.dineInRequiredAuthorization !== undefined ? outlet.dineInRequiredAuthorization : false,
      dineInAutoAuthorizationMinute: outlet.dineInAutoAuthorizationMinute !== undefined ? outlet.dineInAutoAuthorizationMinute : 0,
      dineInAutoAuthorizationActive: outlet.dineInAutoAuthorizationActive !== undefined ? outlet.dineInAutoAuthorizationActive : false,

      /////////////////////////////////////////

      rnpIw: outlet.rnpIw !== undefined ? outlet.rnpIw : 250,
      rnpPx: outlet.rnpPx !== undefined ? outlet.rnpPx : 600,
      rnpPy: outlet.rnpPy !== undefined ? outlet.rnpPy : 0,

      /////////////////////////////////////////

      // 2025-01-27 - open cash drawer times

      // ocdTimes: outlet.ocdTimes !== undefined ? outlet.ocdTimes : 1,

      openShiftWithNum: outlet.openShiftWithNum !== undefined ? outlet.openShiftWithNum : true,
      swTableSkipItems: outlet.swTableSkipItems !== undefined ? outlet.swTableSkipItems : false,
      shiftReminder: outlet.shiftReminder !== undefined ? outlet.shiftReminder : false,
      /////////////////////////////////////////


      //close shift wait time
      ...(outlet.cswt !== undefined) && {
        cswt: outlet.cswt, // printer connecting check http
      },
    };
  }
  catch (ex) {
    console.error(ex);
  }

  /////////////////////////////////////////

  if (outlet.env !== undefined) {
    if (outlet.env === APP_ENV.UAT ||
      outlet.env === APP_ENV.LIVE) {
      global.appEnv = outlet.env;
    }
    else {
      global.appEnv = APP_ENV.LIVE;
    }

    if (outlet.taNT1Off !== undefined) {
      outlet.taNT1Off = false;
    }

    if (outlet.taNT2Off !== undefined) {
      outlet.taNT2Off = false; // need change to false
    }

    if (outlet.taNT3On !== undefined) {
      outlet.taNT3On = false; // need change to false
    }
  }
  else {
    global.appEnv = APP_ENV.LIVE;
  }

  /////////////////////////////////////////

  if (typeof outlet.iminIdForce === 'string') {
    global.iminSerialNo = outlet.iminIdForce;

    await AsyncStorage.setItem('iminIdForce', outlet.iminIdForce);
  }

  if (typeof outlet.sunmiIdForce === 'string') {
    global.iminSerialNo = outlet.sunmiIdForce;

    await AsyncStorage.setItem('sunmiIdForce', outlet.sunmiIdForce);
  }

  /////////////////////////////////////////

  OutletStore.update(s => {
    s.reportDisplayType = outlet.reportDisplayType ? outlet.reportDisplayType : REPORT_DISPLAY_TYPE.DAY;

    s.odDt = Date.now();
  });

  console.log('updateOutletAsyncStorage > global.currOutlet');
  console.log(global.currOutlet);
};

export const logActivity = async (userId, action, remarks = '') => {
  // get the data from userAction using userId
  const userActionSnapshot = await firestore()
    .collection(Collections.UserAction)
    .where('userId', '==', userId)
    .limit(1)
    .get();

  // console.log('Activity log ', userId, action);

  var outletId = '';
  var merchantId = '';

  if (global.currOutlet && global.currOutlet.outletId && global.currOutlet.merchantId) {
    outletId = global.currOutlet.outletId;
    merchantId = global.currOutlet.merchantId;
  }

  try {
    var userAction = null;
    if (userActionSnapshot && !userActionSnapshot.empty) {
      userAction = userActionSnapshot.docs[0].data();

      // append an actiontype and datetime to the userAction list
      var actions = userAction.actions || [];

      if (actions.length >= 150) {
        // actions = actions.slice(-100);

        /////////////////////////////////////////////////////

        // 2023-08-17 - Here to generate new UserActionSlice

        var actionsSlice = actions.slice(0);
        actions = [];

        const newUserActionSlice = {
          uniqueId: uuidv4(),
          userId,
          actions: actionsSlice,

          ...outletId && {
            outletId,
          },

          ...merchantId && {
            merchantId,
          },

          startDate: actionsSlice[0].actionDate,
          endDate: actionsSlice[actionsSlice.length - 1].actionDate,

          createdAt: Date.now(),
          updatedAt: Date.now(),
          deletedAt: null,
        };

        firestore()
          .collection(Collections.UserActionSlice)
          .doc(newUserActionSlice.uniqueId)
          .set(newUserActionSlice);

        /////////////////////////////////////////////////////
      }

      actions = [
        ...actions,
        {
          actionType: action,
          actionDate: moment().valueOf(),

          ...remarks && {
            r: remarks,
          },
        },
      ];

      // update the userAction
      firestore()
        .collection(Collections.UserAction)
        .doc(userAction.uniqueId)
        .update({
          actions,

          ...outletId && {
            outletId,
          },

          ...merchantId && {
            merchantId,
          },

          updatedAt: Date.now(),
        });
    } else {
      // create a new userAction
      const newUserAction = {
        uniqueId: uuidv4(),
        userId,
        actions: [
          {
            actionType: action,
            actionDate: moment().valueOf(),
          },
        ],

        ...outletId && {
          outletId,
        },

        ...merchantId && {
          merchantId,
        },

        createdAt: Date.now(),
        updatedAt: Date.now(),
        deletedAt: null,
      };

      firestore()
        .collection(Collections.UserAction)
        .doc(newUserAction.uniqueId)
        .set(newUserAction);
    }
  } catch (error) {
    // console.log('Error logging activity ', error);
  }
};

export const waitForSeconds = async (seconds) => {
  return new Promise((resolve) =>
    setTimeout(() => {
      resolve();
    }, seconds * 1000),
  );
};

export const checkApplyDiscountPerValidity = (
  promotion,
  promotionIdList,
  cartItem,
) => {
  if (cartItem.rpOrder) {
    // is required purchase order, skip it

    return {
      validity: false,
      discountQuantity: 1,
    };
  }

  if (!promotion || cartItem.promotionId) {
    return {
      validity: false,
      discountQuantity: 1,
    };
  }

  if (
    promotion.applyDiscountPer === APPLY_DISCOUNT_PER.ORDER ||
    promotion.applyDiscountPer === undefined
  ) {
    if (promotionIdList.includes(promotion.promotionId)) {
      return {
        validity: false,
        discountQuantity: 1,
      };
    } else {
      return {
        validity: true,
        discountQuantity: 1,
      };
    }
  } else {
    return {
      validity: true,
      discountQuantity: cartItem.quantity,
    };
  }
};

// check earlier for the rest of cart items, to see if the min quantity and price, matched (for whole bill purpose)
export const checkQualifiedItemsQuantityAndAmountForPromotion = (
  allowStackedPromotionVoucher = false,
  applyDiscountType = APPLY_DISCOUNT_TYPE.PROMOTION_PROMO_CODE,
  campaignData = [],
  selectedPromoCodePromotion = {},
  selectedTaggableVoucher = {},
  promotionIdAppliedList = [],

  promotionDict,
  selectedOutletItemCategoriesDict,

  cartItems,
  usePromoCode = true,

  outletItems,
  outletsItemAddOnIdDict,
  outletsItemAddOnChoiceIdDict,
  orderType,
  currOutlet,

  orderTypeSub = ORDER_TYPE_SUB.NORMAL,
) => {
  let cartItemsInfo = {
    quantity: 0,
    price: 0,
  };

  var tempCartOutletItemsDict = {};

  var tempCartOutletItemAddOnDict = {};
  var tempCartOutletItemAddOnChoiceDict = {};

  for (let i = 0; i < cartItems.length; i++) {
    const tempCartItem = cartItems[i];

    ///////////////////////////////////////////

    if (tempCartItem.addOns === undefined) {
      // raw items

      if (tempCartOutletItemsDict[tempCartItem.itemId] === undefined) {
        let foundItem = outletItems.find(findItem => findItem.uniqueId === tempCartItem.itemId);

        if (foundItem) {
          tempCartOutletItemsDict[tempCartItem.itemId] =
            foundItem;
        }
      }

      var tempCartItemPrice = tempCartOutletItemsDict[tempCartItem.itemId].price;

      var extraPrice = 0;
      if (orderType === ORDER_TYPE.DELIVERY &&
        currOutlet &&
        currOutlet.deliveryPrice) {
        extraPrice = currOutlet.deliveryPrice;
      }
      else if (orderType === ORDER_TYPE.PICKUP &&
        currOutlet &&
        currOutlet.pickUpPrice) {
        extraPrice = currOutlet.pickUpPrice;
      }

      if (orderType === ORDER_TYPE.DELIVERY) {
        extraPrice = tempCartOutletItemsDict[tempCartItem.itemId].deliveryCharges || 0;

        if (extraPrice && tempCartOutletItemsDict[tempCartItem.itemId].deliveryChargesType === CHARGES_TYPE.PERCENTAGE_BASED) {
          extraPrice = BigNumber(tempCartOutletItemsDict[tempCartItem.itemId].price).multipliedBy(extraPrice).dividedBy(100).toNumber();
        }

        if (!tempCartOutletItemsDict[tempCartItem.itemId].deliveryChargesActive) {
          extraPrice = 0;
        }
      }

      if (orderType === ORDER_TYPE.PICKUP && orderTypeSub === ORDER_TYPE_SUB.NORMAL) {
        extraPrice = tempCartOutletItemsDict[tempCartItem.itemId].pickUpCharges || 0;

        if (extraPrice && tempCartOutletItemsDict[tempCartItem.itemId].pickUpChargesType === CHARGES_TYPE.PERCENTAGE_BASED) {
          extraPrice = BigNumber(tempCartOutletItemsDict[tempCartItem.itemId].price).multipliedBy(extraPrice).dividedBy(100).toNumber();
        }

        if (!tempCartOutletItemsDict[tempCartItem.itemId].pickUpChargesActive) {
          extraPrice = 0;
        }
      }

      if (orderType === ORDER_TYPE.PICKUP && orderTypeSub === ORDER_TYPE_SUB.OTHER_DELIVERY) {
        extraPrice = tempCartOutletItemsDict[tempCartItem.itemId].otherDCharges || 0;

        if (extraPrice && tempCartOutletItemsDict[tempCartItem.itemId].otherDChargesType === CHARGES_TYPE.PERCENTAGE_BASED) {
          extraPrice = BigNumber(tempCartOutletItemsDict[tempCartItem.itemId].price).multipliedBy(extraPrice).dividedBy(100).toNumber();
        }

        if (!tempCartOutletItemsDict[tempCartItem.itemId].otherDChargesActive) {
          extraPrice = 0;
        }
      }

      var tempCartItemPriceOriginal =
        BigNumber(tempCartOutletItemsDict[tempCartItem.itemId].price).plus(extraPrice).toNumber();

      //////////////////////////////////////////////////////////

      if (tempCartItem.priceVariable !== undefined) {
        tempCartItemPrice = tempCartItem.priceVariable;

        tempCartItemPriceOriginal = tempCartItem.priceVariable;
      }

      //////////////////////////////////////////////////////////

      if (tempCartItem.priceVariable !== undefined) {
        tempCartItemPrice = tempCartItem.priceVariable;

        tempCartItemPriceOriginal = tempCartItem.priceVariable;
      }

      if (tempCartItem.priceUpselling !== undefined) {
        tempCartItemPrice = tempCartItem.priceUpselling;

        tempCartItemPriceOriginal = tempCartOutletItemsDict[tempCartItem.itemId].price + extraPrice;
      }

      ///////////////////////////////////////////

      if (tempCartItem.priceVariable === undefined) {
        if (tempCartItem.priceUpselling === undefined) {
          tempCartItemPrice = BigNumber(tempCartOutletItemsDict[tempCartItem.itemId].price).plus(extraPrice).toNumber();
        }
      }
      else {
        tempCartItemPrice = tempCartItem.priceVariable;
      }

      //////////////////////////////////////////////////////////

      if (tempCartItem.choices) {
        const tempCartItemChoices = Object.entries(tempCartItem.choices).map(
          ([key, value]) => ({ key, value })
        );

        for (var j = 0; j < tempCartItemChoices.length; j++) {
          if (tempCartItemChoices[j].value) {
            // means the addon of this item is picked, need to retrieve the actual addon

            if (
              tempCartOutletItemAddOnChoiceDict[tempCartItemChoices[j].key] ===
              undefined
            ) {
              // const outletItemAddOnChoiceSnapshot = await firebase
              //   .firestore()
              //   .collection(Collections.OutletItemAddOnChoice)
              //   .where("uniqueId", "==", tempCartItemChoices[j].key)
              //   .limit(1)
              //   .get();

              // if (!outletItemAddOnChoiceSnapshot.empty) {
              //   tempCartOutletItemAddOnChoiceDict[tempCartItemChoices[j].key] =
              //     outletItemAddOnChoiceSnapshot.docs[0].data();
              // }

              if (outletsItemAddOnChoiceIdDict[tempCartItemChoices[j].key]) {
                tempCartOutletItemAddOnChoiceDict[tempCartItemChoices[j].key] =
                  outletsItemAddOnChoiceIdDict[tempCartItemChoices[j].key];
              }
            }

            if (tempCartItem.priceVariable === undefined) {
              // tempCartItemPrice +=
              //   tempCartOutletItemAddOnChoiceDict[tempCartItemChoices[j].key]
              //     .price;

              tempCartItemPrice =
                BigNumber(tempCartItemPrice).plus(tempCartOutletItemAddOnChoiceDict[tempCartItemChoices[j].key]
                  .price).toNumber();

              // 2022-10-05 - Added missing line

              // tempCartItemPriceOriginal +=
              //   tempCartOutletItemAddOnChoiceDict[tempCartItemChoices[j].key]
              //     .price;

              tempCartItemPriceOriginal =
                BigNumber(tempCartItemPriceOriginal).plus(tempCartOutletItemAddOnChoiceDict[tempCartItemChoices[j].key].price).toNumber();
            }

            // need to retrieve the description/type name of this addon choice

            const tempCartItemAddOnChoice =
              tempCartOutletItemAddOnChoiceDict[tempCartItemChoices[j].key];

            if (
              tempCartOutletItemAddOnDict[
              tempCartItemAddOnChoice.outletItemAddOnId
              ] === undefined
            ) {
              // const outletItemAddOnSnapshot = await firebase
              //   .firestore()
              //   .collection(Collections.OutletItemAddOn)
              //   .where(
              //     "uniqueId",
              //     "==",
              //     tempCartItemAddOnChoice.outletItemAddOnId
              //   )
              //   .limit(1)
              //   .get();

              // if (!outletItemAddOnSnapshot.empty) {
              //   tempCartOutletItemAddOnDict[
              //     tempCartItemAddOnChoice.outletItemAddOnId
              //   ] = outletItemAddOnSnapshot.docs[0].data();
              // }

              if (outletsItemAddOnIdDict[tempCartItemAddOnChoice.outletItemAddOnId]) {
                tempCartOutletItemAddOnDict[tempCartItemAddOnChoice.outletItemAddOnId] =
                  outletsItemAddOnIdDict[tempCartItemAddOnChoice.outletItemAddOnId];
              }
            }

            // if (
            //     tempCartItemAddOnCategorized[
            //     tempCartItemAddOnChoice.outletItemAddOnId
            //     ] === undefined
            // ) {
            //     tempCartItemAddOnCategorized[
            //         tempCartItemAddOnChoice.outletItemAddOnId
            //     ] = [];
            // }

            // tempCartItemAddOnCategorized[
            //     tempCartItemAddOnChoice.outletItemAddOnId
            // ].push(tempCartItemAddOnChoice.name);

            // if (
            //     tempCartItemAddOnCategorizedPrice[
            //     tempCartItemAddOnChoice.outletItemAddOnId
            //     ] === undefined
            // ) {
            //     tempCartItemAddOnCategorizedPrice[
            //         tempCartItemAddOnChoice.outletItemAddOnId
            //     ] = [];
            // }

            // tempCartItemAddOnCategorizedPrice[
            //     tempCartItemAddOnChoice.outletItemAddOnId
            // ].push(tempCartItemAddOnChoice.price);
          }
        }

        // const tempCartItemAddOnCategorizedList = Object.entries(
        //     tempCartItemAddOnCategorized
        // ).map(([key, value]) => ({ key: key, value: value }));
        // const tempCartItemAddOnCategorizedPriceList = Object.entries(
        //     tempCartItemAddOnCategorizedPrice
        // ).map(([key, value]) => ({ key: key, value: value }));

        // if (tempCartItemAddOnCategorizedList.length > 0) {
        //     for (var j = 0; j < tempCartItemAddOnCategorizedList.length; j++) {
        //         const tempCartItemAddOnName =
        //             tempCartOutletItemAddOnDict[
        //                 tempCartItemAddOnCategorizedList[j].key
        //             ].name;

        //         tempCartItemAddOnParsed.push({
        //             name: tempCartItemAddOnName,
        //             choiceNames: tempCartItemAddOnCategorizedList[j].value,
        //             prices: tempCartItemAddOnCategorizedPriceList[j].value,
        //         });
        //     }
        // }
      }

      //////////////////////////////////////////////////////////////////////

      // for add-on group

      if (tempCartItem.addOnGroupList) {
        for (var j = 0; j < tempCartItem.addOnGroupList.length; j++) {
          // now separate all, might change in future

          const addOnGroup = tempCartItem.addOnGroupList[j];

          // tempCartItemAddOnParsed.push({
          //     name: addOnGroup.addOnName,
          //     addOnId: addOnGroup.outletItemAddOnId,
          //     choiceNames: [addOnGroup.choiceName],
          //     prices: [addOnGroup.quantity * addOnGroup.price],
          //     quantities: [addOnGroup.quantity],
          //     singlePrices: [addOnGroup.price],
          //     addOnChoiceIdList: [addOnGroup.outletItemAddOnChoiceId],
          //     minSelectList: [addOnGroup.minSelect],
          //     maxSelectList: [addOnGroup.maxSelect],
          // });

          if (tempCartItem.priceVariable === undefined) {
            // tempCartItemPrice += addOnGroup.quantity * addOnGroup.price;
            // tempCartItemPriceOriginal += addOnGroup.quantity * addOnGroup.price;

            tempCartItemPrice = BigNumber(tempCartItemPrice).plus(BigNumber(addOnGroup.quantity).multipliedBy(addOnGroup.price)).toNumber();
            tempCartItemPriceOriginal = BigNumber(tempCartItemPriceOriginal).plus(BigNumber(addOnGroup.quantity).multipliedBy(addOnGroup.price)).toNumber();
          }
        }
      }

      //////////////////////////////////////////////////////////////////////

      // 2022-10-05 - Fixes for variable pricing (should use the inputed price already)

      if (tempCartItem.priceVariable === undefined) {
        // tempCartItemPrice = tempCartItemPrice * tempCartItem.quantity;
        // tempCartItemPriceOriginal =
        //   tempCartItemPriceOriginal * tempCartItem.quantity;

        tempCartItemPrice = BigNumber(tempCartItemPrice).multipliedBy(tempCartItem.quantity).toNumber();
        tempCartItemPriceOriginal =
          BigNumber(tempCartItemPriceOriginal).multipliedBy(tempCartItem.quantity).toNumber();
      }
    }
    else {
      // processed items

      tempCartItemPrice = tempCartItem.price;
      // tempCartItemPrepareTime = tempCartItem.prepareTime;

      //////////////////////////////////////////////////////////

      // if is bundle item

      if (tempCartItem.priceBundle !== undefined) {
        tempCartItemPrice = tempCartItem.priceBundle;
        // tempCartItemPriceOriginal = tempCartItem.priceBundle;
      }

      //////////////////////////////////////////////////////////

      // if is variable price item

      if (tempCartItem.priceVariable !== undefined) {
        tempCartItemPrice = tempCartItem.priceVariable;
        // tempCartItemPriceOriginal = tempCartItem.priceBundle;
      }

      //////////////////////////////////////////////////////////

      if (tempCartItem.priceUpselling !== undefined) {
        // tempCartItemPrice = tempCartItem.priceUpselling;

        // tempCartItemPriceOriginal = tempCartOutletItemsDict[tempCartItem.itemId].price + extraPrice;
      }
      //////////////////////////////////////////////////////////
    }

    //////////////////////////////////////////////////////////////////////

    var promotionCategory = undefined;

    if (
      selectedOutletItemCategoriesDict[tempCartItem.categoryId] &&
      promotionDict[
      selectedOutletItemCategoriesDict[tempCartItem.categoryId].name
      ] !== undefined
      && promotionDict[
        selectedOutletItemCategoriesDict[tempCartItem.categoryId].name
      ].usePromoCode === usePromoCode
      &&
      checkIsAllowPromotionVoucherToApply(
        allowStackedPromotionVoucher,
        applyDiscountType,
        promotionDict[
        selectedOutletItemCategoriesDict[tempCartItem.categoryId].name
        ],
        selectedPromoCodePromotion,
        {},
        promotionIdAppliedList,
      )
    ) {
      promotionCategory =
        promotionDict[
        selectedOutletItemCategoriesDict[tempCartItem.categoryId].name
        ];
    }

    if (promotionDict[tempCartItem.itemSku] !== undefined
      && promotionDict[tempCartItem.itemSku].usePromoCode === usePromoCode
      &&
      checkIsAllowPromotionVoucherToApply(
        allowStackedPromotionVoucher,
        applyDiscountType,
        promotionDict[tempCartItem.itemSku],
        selectedPromoCodePromotion,
        {},
        promotionIdAppliedList,
      )
      // &&
      // (percentageOffItemSkuDict[tempCartItem.itemSku].applyBefore === APPLY_BEFORE.ORDER_PLACED || percentageOffItemSkuDict[tempCartItem.itemSku].applyBefore === undefined)
    ) {
      var promotionResult = checkApplyDiscountPerValidity(promotionDict[tempCartItem.itemSku], promotionIdAppliedList, tempCartItem);

      if (promotionResult.validity) {
        // here accum the amount and quantity

        cartItemsInfo.quantity += tempCartItem.quantity;
        cartItemsInfo.price += tempCartItemPrice;
      }
    } else if (promotionCategory !== undefined) {
      var promotionResult = checkApplyDiscountPerValidity(promotionCategory, promotionIdAppliedList, tempCartItem);

      if (promotionResult.validity) {
        // here accum the amount and quantity

        cartItemsInfo.quantity += tempCartItem.quantity;
        cartItemsInfo.price += tempCartItemPrice;
      }
    }
  }

  console.log(cartItemsInfo);

  return cartItemsInfo;
};

export const generateUniqueUserIdHuman = async () => {
  var userIdHumanUnique = '';

  var isUnique = false;

  while (!isUnique) {
    userIdHumanUnique = randomUserIdHuman();

    var findCRMUserSnapshot = await firestore()
      .collection(Collections.CRMUser)
      .where('userIdHuman', '==', userIdHumanUnique)
      .get();

    if (findCRMUserSnapshot && !findCRMUserSnapshot.empty) {
      // means existing
    } else {
      isUnique = true;
    }
  }

  return userIdHumanUnique;
};

export const generateUniqueEmailAddress = async () => {
  var emailAddressUnique = '';

  var isUnique = false;

  while (!isUnique) {
    emailAddressUnique = randomEmailAddress();

    var findUserSnapshot = await firestore()
      .collection(Collections.User)
      .where('email', '==', emailAddressUnique)
      .get();

    if (findUserSnapshot && !findUserSnapshot.empty) {
      // means existing
    } else {
      findUserSnapshot = await firestore()
        .collection(Collections.CRMUser)
        .where('email', '==', emailAddressUnique)
        .get();

      if (findUserSnapshot && !findUserSnapshot.empty) {
        // means existing
      } else {
        isUnique = true;
      }
    }
  }

  return `user-${emailAddressUnique}@mykoodoo.com`;
};

export const parseValidPriceText = (text) => {
  text = text.replace(/[^0-9\.]/g, '');

  if (text.indexOf('.') > 0) {
    var strAfter = text.slice(text.indexOf('.'));

    if (strAfter.length > 2) {
      return parseFloat(text).toFixed(2);
    } else {
      return text;
    }
  } else {
    return text;
  }
};

export const parseValidIntegerText = (text) => {
  text = text.replace(/[^0-9\.]/g, '');

  if (text.indexOf('.') > 0) {
    var strBefore = text.slice(0, text.indexOf('.'));

    return strBefore;
  } else {
    return text;
  }
};

export const getRandomColor = () => {
  return `#${Math.floor(Math.random() * 16777215).toString(16)}`;
};

export const logEventAnalytics = async (param) => {
  const {
    eventName,
    eventNameParsed,

    merchantName,
    outletName,

    merchantId,
    outletId,
    userId,
  } = param;

  try {
    await analytics().logEvent(`${eventName}`, {
      // id: uuidv4(),

      eventNameParsed,

      merchantName: global.merchantName,
      outletName: global.outletName,

      merchantId: global.merchantId,
      outletId: global.outletId,
      userId: global.userId,

      // pin: global.pinNo,
    });
  }
  catch (ex) {
    console.error(ex);
  }
};

export const checkUserLoyaltyStampCompleted = async (loyaltyStampId, userLoyaltyStamp) => {
  var statusCompleted = true;
  // var statusText = 'Completed!';

  // var userLoyaltyStamp = selectedCustomerUserLoyaltyStamps.find(stamp => stamp.loyaltyStampId === item.uniqueId);

  const loyaltyStampSnapshot = await firestore()
    .collection(Collections.LoyaltyStamp)
    .where('uniqueId', '==', loyaltyStampId)
    // .where('deletedAt', '==', null)
    .limit(1)
    .get();

  var loyaltyStamp = null;
  if (!loyaltyStampSnapshot.empty) {
    loyaltyStamp = loyaltyStampSnapshot.docs[0].data();
  }

  if (userLoyaltyStamp && loyaltyStamp && loyaltyStamp.lsItems) {
    for (var i = 0; i < loyaltyStamp.lsItems.length; i++) {
      if (!userLoyaltyStamp.getIdHistory.includes(loyaltyStamp.lsItems[i].lsItemId)) {
        // means havent redeem this stamp yet

        // var stampsRequired = loyaltyStamp.lsItems[i].noOfStamp - userLoyaltyStamp.stampCount;

        // if (stampsRequired < 0) {
        //   stampsRequired = 0;
        // }

        // statusText = `${stampsRequired} more to go!`;

        statusCompleted = false;

        break;
      }
      else {
        // means already redeem this stamp, proceed to next to check

        continue;
      }
    }
  }
  else {
    statusCompleted = false;
  }

  return statusCompleted;
};

export const isTablet = async () => {
  // const isTabletForce = storageMMKV.getString('isTabletForce');
  const isTabletForce = await global.watermelonDBDatabase.localStorage.get('isTabletForce');

  if (isTabletForce === '1' ||
    isTabletForce === '0') {
    if (isTabletForce === '1') {
      // global.isTabletForce = '1';

      return true;
    }
    else {
      // global.isTabletForce = '0';

      return false;
    }

    // return true;
  }
  else {
    // return isTabletOriginal;
    return isTabletWrapper();
    // return () => { return true };
    // return true;
  }
};

export const isTabletStatic = async () => {
  // const isTabletForce = storageMMKV.getString('isTabletForce');
  const isTabletForce = await global.watermelonDBDatabase.localStorage.get('isTabletForce');

  if (isTabletForce === '1' ||
    isTabletForce === '0') {
    if (isTabletForce === '1') {
      return true;
    }
    else {
      return false;
      // return true;
    }
  }
  else {
    // return isTabletOriginal;
    return isTabletWrapper();
    // return true;
  }
};

export const ipV6ToIpV4 = (ip6) => {
  function parseIp6(ip6str) {
    const str = ip6str.toString();

    // Initialize
    const ar = new Array();
    for (var i = 0; i < 8; i++) ar[i] = 0;

    // Check for trivial IPs
    if (str == '::') return ar;

    // Parse
    const sar = str.split(':');
    let slen = sar.length;
    if (slen > 8) slen = 8;
    let j = 0;
    i = 0
    for (i = 0; i < slen; i++) {
      // This is a "::", switch to end-run mode
      if (i && sar[i] == '') {
        j = 9 - slen + i;
        continue;
      }
      ar[j] = parseInt(`0x0${sar[i]}`);
      j++;
    }

    return ar;
  }

  var ip6parsed = parseIp6(ip6);
  const ip4 = `${ip6parsed[6] >> 8}.${ip6parsed[6] & 0xff}.${ip6parsed[7] >> 8}.${ip6parsed[7] & 0xff}`;
  return ip4;
};

export const assignDefaultTagsToUser = async (userData, userOrder, userCRM) => {
  let crmUserTagIdList = [];
  if (userCRM && userCRM.crmUserTagIdList && Array.isArray(userCRM.crmUserTagIdList)) {
    crmUserTagIdList = [
      ...userCRM.crmUserTagIdList
    ];
  }

  if (userData.email && userData.phone) {
    let TAG_ORDER_TYPE = DEFAULT_CRM_TAG[userOrder.orderType];
    let TAG_DAY_PERIOD = '';
    if (moment(userOrder.createdAt).isBetween(
      moment().hour(0).minute(0).second(0),
      moment().hour(12).minute(0).second(0),
      'minute',
      '[)',
    )) {
      TAG_DAY_PERIOD = DEFAULT_CRM_TAG.MORNING;
    }
    else if (moment(userOrder.createdAt).isBetween(
      moment().hour(12).minute(0).second(0),
      moment().hour(18).minute(0).second(0),
      'minute',
      '[)',
    )) {
      TAG_DAY_PERIOD = DEFAULT_CRM_TAG.AFTERNOON;
    }
    else if (moment(userOrder.createdAt).isBetween(
      moment().hour(18).minute(0).second(0),
      moment().hour(23).minute(59).second(59),
      'minute',
      '[)',
    )) {
      TAG_DAY_PERIOD = DEFAULT_CRM_TAG.NIGHT;
    }

    if (TAG_ORDER_TYPE) {
      var tagOrderType = null;

      var findTagSnapshot = await firestore()
        .collection(Collections.CRMUserTag)
        .where('name', '==', TAG_ORDER_TYPE)
        .where('merchantId', '==', userOrder.merchantId)
        .get();

      if (findTagSnapshot && !findTagSnapshot.empty) {
        tagOrderType = findTagSnapshot.docs[0].data();
      }

      if (tagOrderType) {
        if (userData.email && userData.phone) {
          if (!tagOrderType.emailList.includes(userData.email) || !tagOrderType.phoneList.includes(userData.phone)) {
            try {
              firestore()
                .collection(Collections.CRMUserTag)
                .doc(tagOrderType.uniqueId)
                .update({
                  emailList: [
                    ...tagOrderType.emailList,
                    userData.email,
                  ],
                  phoneList: [
                    ...tagOrderType.phoneList,
                    userData.phone,
                  ],
                  tokenFcmList: [
                    ...tagOrderType.tokenFcmList,
                    '',
                  ],

                  updatedAt: Date.now(),
                });
            }
            catch (ex) {
              console.error(ex);
            }

            if (!crmUserTagIdList.includes(tagOrderType.uniqueId)) {
              crmUserTagIdList.push(tagOrderType.uniqueId);
            }
          }
        }
      }
      else {
        var emailListNew = userData.email ? [userData.email] : [];
        var phoneListNew = userData.phone ? [userData.phone] : [];
        var tokenFcmListNew = (userData.email || userData.phone) ? [''] : [];

        tagOrderType = {
          uniqueId: uuidv4(),

          name: TAG_ORDER_TYPE,

          merchantId: userOrder.merchantId,

          emailList: emailListNew,
          phoneList: phoneListNew,
          tokenFcmList: tokenFcmListNew,

          createdAt: Date.now(),
          updatedAt: Date.now(),
          deletedAt: null,
        };

        firestore()
          .collection(Collections.CRMUserTag)
          .doc(tagOrderType.uniqueId)
          .set(tagOrderType);

        if (!crmUserTagIdList.includes(tagOrderType.uniqueId)) {
          crmUserTagIdList.push(tagOrderType.uniqueId);
        }
      }
    }

    if (TAG_DAY_PERIOD) {
      var tagDayPeriod = null;

      var findTagSnapshot = await firestore()
        .collection(Collections.CRMUserTag)
        .where('name', '==', TAG_DAY_PERIOD)
        .where('merchantId', '==', userOrder.merchantId)
        .get();

      if (findTagSnapshot && !findTagSnapshot.empty) {
        tagDayPeriod = findTagSnapshot.docs[0].data();
      }

      if (tagDayPeriod) {
        if (userData.email && userData.phone) {
          if (!tagDayPeriod.emailList.includes(userData.email) || !tagDayPeriod.phoneList.includes(userData.phone)) {
            try {
              firestore()
                .collection(Collections.CRMUserTag)
                .doc(tagDayPeriod.uniqueId)
                .update({
                  emailList: [
                    ...tagDayPeriod.emailList,
                    userData.email,
                  ],
                  phoneList: [
                    ...tagDayPeriod.phoneList,
                    userData.phone,
                  ],
                  tokenFcmList: [
                    ...tagDayPeriod.tokenFcmList,
                    '',
                  ],

                  updatedAt: Date.now(),
                });
            }
            catch (ex) {
              console.error(ex);
            }

            if (!crmUserTagIdList.includes(tagDayPeriod.uniqueId)) {
              crmUserTagIdList.push(tagDayPeriod.uniqueId);
            }
          }
        }
      }
      else {
        var emailListNew = userData.email ? [userData.email] : [];
        var phoneListNew = userData.phone ? [userData.phone] : [];
        var tokenFcmListNew = (userData.email || userData.phone) ? [''] : [];

        tagDayPeriod = {
          uniqueId: uuidv4(),

          name: TAG_DAY_PERIOD,

          merchantId: userOrder.merchantId,

          emailList: emailListNew,
          phoneList: phoneListNew,
          tokenFcmList: tokenFcmListNew,

          createdAt: Date.now(),
          updatedAt: Date.now(),
          deletedAt: null,
        };

        firestore()
          .collection(Collections.CRMUserTag)
          .doc(tagDayPeriod.uniqueId)
          .set(tagDayPeriod);

        if (!crmUserTagIdList.includes(tagDayPeriod.uniqueId)) {
          crmUserTagIdList.push(tagDayPeriod.uniqueId);
        }
      }
    }

    ////////////////////////////////////////////////

    if (userOrder && userOrder.cartItems && userOrder.cartItems.length > 0) {
      // create and or assign category as tag to user

      var categoryIdUniqueList = [...new Set(userOrder.cartItems.map(item => item.categoryId))];

      for (let i = 0; i < categoryIdUniqueList.length; i++) {
        let category = null;

        var findCategorySnapshot = await firestore()
          .collection(Collections.OutletItemCategory)
          .where('uniqueId', '==', categoryIdUniqueList[i])
          .get();

        if (findCategorySnapshot && !findCategorySnapshot.empty) {
          category = findCategorySnapshot.docs[0].data();
        }

        if (category && category.name) {
          var tagCategoryName = null;

          var findTagSnapshot = await firestore()
            .collection(Collections.CRMUserTag)
            .where('name', '==', category.name)
            .where('merchantId', '==', userOrder.merchantId)
            .get();

          if (findTagSnapshot && !findTagSnapshot.empty) {
            tagCategoryName = findTagSnapshot.docs[0].data();
          }

          if (tagCategoryName) {
            if (userData.email && userData.phone) {
              if (!tagCategoryName.emailList.includes(userData.email) || !tagCategoryName.phoneList.includes(userData.phone)) {
                try {
                  firestore()
                    .collection(Collections.CRMUserTag)
                    .doc(tagCategoryName.uniqueId)
                    .update({
                      emailList: [
                        ...tagCategoryName.emailList,
                        userData.email,
                      ],
                      phoneList: [
                        ...tagCategoryName.phoneList,
                        userData.phone,
                      ],
                      tokenFcmList: [
                        ...tagCategoryName.tokenFcmList,
                        '',
                      ],

                      updatedAt: Date.now(),
                    });
                }
                catch (ex) {
                  console.error(ex);
                }

                if (!crmUserTagIdList.includes(tagCategoryName.uniqueId)) {
                  crmUserTagIdList.push(tagCategoryName.uniqueId);
                }
              }
            }
          }
          else {
            var emailListNew = userData.email ? [userData.email] : [];
            var phoneListNew = userData.phone ? [userData.phone] : [];
            var tokenFcmListNew = (userData.email || userData.phone) ? [''] : [];

            tagCategoryName = {
              uniqueId: uuidv4(),

              name: category.name,

              merchantId: userOrder.merchantId,

              emailList: emailListNew,
              phoneList: phoneListNew,
              tokenFcmList: tokenFcmListNew,

              createdAt: Date.now(),
              updatedAt: Date.now(),
              deletedAt: null,
            };

            firestore()
              .collection(Collections.CRMUserTag)
              .doc(tagCategoryName.uniqueId)
              .set(tagCategoryName);

            if (!crmUserTagIdList.includes(tagCategoryName.uniqueId)) {
              crmUserTagIdList.push(tagCategoryName.uniqueId);
            }
          }
        }
      }
    }
  }

  let toUpdated = false;
  if (userCRM && userCRM.uniqueId && userCRM.crmUserTagIdList && Array.isArray(userCRM.crmUserTagIdList)) {
    if (crmUserTagIdList.length !== userCRM.crmUserTagIdList.length) {
      toUpdated = true;
    }
  }
  else if (userCRM && userCRM.uniqueId && userCRM.crmUserTagIdList === undefined) {
    toUpdated = true;
  }

  if (toUpdated) {
    console.log('update user tags');

    firestore()
      .collection(Collections.CRMUser)
      .doc(userCRM.uniqueId)
      .update({
        crmUserTagIdList,

        updatedAt: Date.now(),
      });
  }
  else {
    console.log('no need update user tags');
  }
};

export const sendOrderReceiptEmail = async (
  userOrderParam,
  emailToSent,
  currToPaidUser = null,
) => {
  let userOrder = userOrderParam;

  if (userOrder && userOrder.uniqueId) {
    // no need retrieve the order data
  }
  else if (typeof userOrder === 'string') {
    const userOrderSnapshot = await firestore()
      .collection(Collections.UserOrder)
      .where('uniqueId', '==', userOrder) // should be a string
      .limit(1)
      .get();

    if (!userOrderSnapshot.empty) {
      userOrder = userOrderSnapshot.docs[0].data();
    }
  }

  if (userOrder && userOrder.uniqueId) {
    // can proceed

    const currOutletLocal = global.currOutlet ? global.currOutlet : null;

    var amountReceived = 0;
    var amountBalance = 0;

    if (
      // isSplitReceipt &&
      userOrder.splitAmountList &&
      userOrder.splitAmountList.length > 0
    ) {
      amountReceived =
        userOrder.splitAmountList[
          userOrder.splitAmountList.length - 1
        ].amount;

      let totalPaidAmount = userOrder.splitAmountList.reduce(
        (accum, splitAmount) => accum + splitAmount.amount,
        0,
      );
      amountBalance = userOrder.finalPrice - totalPaidAmount;
    }

    var scOtherDApplied = false;
    if (currOutletLocal &&
      currOutletLocal.scActiveOtherD &&
      userOrder.orderType === ORDER_TYPE.PICKUP &&
      userOrder.orderTypeSub === ORDER_TYPE_SUB.OTHER_DELIVERY) {
      scOtherDApplied = true;
    }
    else {
      scOtherDApplied = false;
    }

    var body = {
      email: emailToSent,
      title: `[KooDoo] ${userOrder.outletName} - Your Receipt For Order #${userOrder.orderType !== ORDER_TYPE.DINEIN ? 'T' : ''}${userOrder.orderId}`,
      html: `
    <!DOCTYPE html>
<html>
<head>

  <meta charset="utf-8">
  <meta http-equiv="x-ua-compatible" content="ie=edge">
  <title>Email Receipt</title>
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <style type="text/css">
  /**
   * Google webfonts. Recommended to include the .woff version for cross-client compatibility.
   */
  @media screen {
    @font-face {
      font-family: 'Source Sans Pro';
      font-style: normal;
      font-weight: 400;
      src: local('Source Sans Pro Regular'), local('SourceSansPro-Regular'), url(https://fonts.gstatic.com/s/sourcesanspro/v10/ODelI1aHBYDBqgeIAH2zlBM0YzuT7MdOe03otPbuUS0.woff) format('woff');
    }

    @font-face {
      font-family: 'Source Sans Pro';
      font-style: normal;
      font-weight: 700;
      src: local('Source Sans Pro Bold'), local('SourceSansPro-Bold'), url(https://fonts.gstatic.com/s/sourcesanspro/v10/toadOcfmlt9b38dHJxOBGFkQc6VGVFSmCnC_l7QZG60.woff) format('woff');
    }
  }

  /**
   * Avoid browser level font resizing.
   * 1. Windows Mobile
   * 2. iOS / OSX
   */
  body,
  table,
  td,
  a {
    -ms-text-size-adjust: 100%; /* 1 */
    -webkit-text-size-adjust: 100%; /* 2 */
  }

  /**
   * Remove extra space added to tables and cells in Outlook.
   */
  table,
  td {
    mso-table-rspace: 0pt;
    mso-table-lspace: 0pt;
  }

  /**
   * Better fluid images in Internet Explorer.
   */
  img {
    -ms-interpolation-mode: bicubic;
  }

  /**
   * Remove blue links for iOS devices.
   */
  a[x-apple-data-detectors] {
    font-family: inherit !important;
    font-size: inherit !important;
    font-weight: inherit !important;
    line-height: inherit !important;
    color: inherit !important;
    text-decoration: none !important;
  }

  /**
   * Fix centering issues in Android 4.4.
   */
  div[style*="margin: 16px 0;"] {
    margin: 0 !important;
  }

  body {
    width: 100% !important;
    height: 100% !important;
    padding: 0 !important;
    margin: 0 !important;
  }

  /**
   * Collapse table borders to avoid space between cells.
   */
  table {
    border-collapse: collapse !important;
  }

  a {
    color: #1a82e2;
  }

  img {
    height: auto;
    line-height: 100%;
    text-decoration: none;
    border: 0;
    outline: none;
  }
  </style>

</head>
<body style="background-color: #4E9F7D;">

  <!-- start preheader -->
  <div class="preheader" style="display: none; max-width: 0; max-height: 0; overflow: hidden; font-size: 1px; line-height: 1px; color: #fff; opacity: 0;">    
  </div>
  <!-- end preheader -->

  <!-- start body -->
  <table border="0" cellpadding="0" cellspacing="0" width="100%">

    <!-- start logo -->
    <tr>
      <td align="center" bgcolor="#4E9F7D">
        <!--[if (gte mso 9)|(IE)]>
        <table align="center" border="0" cellpadding="0" cellspacing="0" width="600">
        <tr>
        <td align="center" valign="top" width="600">
        <![endif]-->
        <table border="0" cellpadding="0" cellspacing="0" width="100%" style="max-width: 600px;">
          <tr>
            <td align="center" valign="top" style="padding: 36px 24px;">
              <a href="https://mykoodoo.com" target="_blank" style="display: inline-block;">
                <img src="${userOrder.merchantLogo}" alt="Logo" border="0" width="48" style="display: block; width: 96px; max-width: 96px; min-width: 96px;">
              </a>
            </td>
          </tr>
        </table>

        ${userOrder.eiQrUrl
          ?
          `<table border="0" cellpadding="0" cellspacing="0" width="100%" style="max-width: 600px;">
        <tr>
          <td align="center" valign="top" style="padding: 36px 24px;">
            <a href="${userOrder.eiUrl}" target="_blank" style="display: inline-block;">
              <img src="${userOrder.eiQrUrl}" alt="Logo" border="0" width="128" style="display: block; width: 256px; max-width: 256px; min-width: 256px;">
            </a>
          </td>
        </tr>
      </table>`
          :
          ''}

        <!--[if (gte mso 9)|(IE)]>
        </td>
        </tr>
        </table>
        <![endif]-->
      </td>
    </tr>
    <!-- end logo -->

    <!-- start hero -->
    <tr>
      <td align="center" bgcolor="#4E9F7D">
        <!--[if (gte mso 9)|(IE)]>
        <table align="center" border="0" cellpadding="0" cellspacing="0" width="600">
        <tr>
        <td align="center" valign="top" width="600">
        <![endif]-->
        <table border="0" cellpadding="0" cellspacing="0" width="100%" style="max-width: 600px;">
          <tr>
            <td align="left" bgcolor="#ffffff" style="padding: 36px 24px 0; font-family: 'Source Sans Pro', Helvetica, Arial, sans-serif; border-top: 3px solid #d4dadf;">
              <h1 style="margin: 0; font-size: 32px; font-weight: 700; letter-spacing: -1px; line-height: 48px;">Thank you for your order!</h1>
            </td>
          </tr>
        </table>
        <!--[if (gte mso 9)|(IE)]>
        </td>
        </tr>
        </table>
        <![endif]-->
      </td>
    </tr>
    <!-- end hero -->

    <!-- start copy block -->
    <tr>
      <td align="center" bgcolor="#4E9F7D">
        <!--[if (gte mso 9)|(IE)]>
        <table align="center" border="0" cellpadding="0" cellspacing="0" width="600">
        <tr>
        <td align="center" valign="top" width="600">
        <![endif]-->
        <table border="0" cellpadding="0" cellspacing="0" width="100%" style="max-width: 600px;">

          <!-- start copy -->
          <tr>
            <td align="left" bgcolor="#ffffff" style="padding: 24px; font-family: 'Source Sans Pro', Helvetica, Arial, sans-serif; font-size: 16px; line-height: 24px;">
              <p style="margin: 0;">Here is the summary of your recent order.</p>
            </td>
          </tr>
          <!-- end copy -->

          <!-- start receipt table -->
          <tr>
            <td align="left" bgcolor="#ffffff" style="padding: 24px; font-family: 'Source Sans Pro', Helvetica, Arial, sans-serif; font-size: 16px; line-height: 24px;">
              <table border="0" cellpadding="0" cellspacing="0" width="100%">
                <tr>
                  <td align="left" bgcolor="#4E9F7D" width="75%" style="padding: 12px;font-family: 'Source Sans Pro', Helvetica, Arial, sans-serif; font-size: 16px; line-height: 24px;"><strong>Order #</strong></td>
                  <td align="right" bgcolor="#4E9F7D" width="25%" style="padding: 12px;font-family: 'Source Sans Pro', Helvetica, Arial, sans-serif; font-size: 16px; line-height: 24px;"><strong>${userOrder.orderType !== ORDER_TYPE.DINEIN ? 'T' : ''}${userOrder.orderId}</strong></td>
                </tr>
                ${userOrder.cartItems.map((item, index) => {
            return (
              `<tr><td align="left" width="75%" style="padding: 6px 12px;font-family: 'Source Sans Pro', Helvetica, Arial, sans-serif; font-size: 16px; line-height: 24px;">${`${(index + 1)}. ${item.name}${item.priceType === PRODUCT_PRICE_TYPE.UNIT ? ` (${UNIT_TYPE_SHORT[item.unitType]})` : ''} (x${item.quantity})`}</td>
                              <td align="right" width="25%" style="padding: 6px 12px;font-family: 'Source Sans Pro', Helvetica, Arial, sans-serif; font-size: 16px; line-height: 24px;">RM ${(item.price + (item.discount ? item.discount : 0)).toFixed(2)}</td>
                            </tr>
                            
                            ${item.remarks ? `
                              <tr>
                              <td align="left" width="75%" style="padding: 6px 12px;font-family: 'Source Sans Pro', Helvetica, Arial, sans-serif; font-size: 16px; line-height: 24px;">${item.remarks}</td>
                              <td align="right" width="25%" style="padding: 6px 12px;font-family: 'Source Sans Pro', Helvetica, Arial, sans-serif; font-size: 16px; line-height: 24px;"></td>
                            </tr>
                              ` : ''}

                              ${item.addOns ? `${item.addOns.map(
                (addOn, addOnIndex) => {
                  let addOnStr = `${addOn.name}: `;

                  addOnStr += addOn.choiceNames.join(', ');

                  return (`
                                  <tr>
                              <td align="left" width="75%" style="padding: 6px 12px;font-family: 'Source Sans Pro', Helvetica, Arial, sans-serif; font-size: 16px; line-height: 24px;">${addOnStr}${addOn.quantities !== undefined ? (`, x${addOn.quantities[0]}`) : ''}</td>
                              <td align="right" width="25%" style="padding: 6px 12px;font-family: 'Source Sans Pro', Helvetica, Arial, sans-serif; font-size: 16px; line-height: 24px;"></td>
                            </tr>
                                  `);
                }).join('')
                }` : ''}

                              ${item.extraPrice ? `
                              <tr>
                              <td align="left" width="75%" style="padding: 6px 12px;font-family: 'Source Sans Pro', Helvetica, Arial, sans-serif; font-size: 16px; line-height: 24px;">${item.orderType === ORDER_TYPE.PICKUP ? 'Takeaway Charges: ' : 'Delivery Charges: '}${(item.extraPrice).toFixed(2)} (x${item.quantity})</td>
                              <td align="right" width="25%" style="padding: 6px 12px;font-family: 'Source Sans Pro', Helvetica, Arial, sans-serif; font-size: 16px; line-height: 24px;"></td>
                            </tr>
                              ` : ''}`);
          }).join('')
        }
                <tr>
                  <td align="left" width="75%" style="padding: 6px 12px;font-family: 'Source Sans Pro', Helvetica, Arial, sans-serif; font-size: 16px; line-height: 24px;">Subtotal</td>
                  <td align="right" width="25%" style="padding: 6px 12px;font-family: 'Source Sans Pro', Helvetica, Arial, sans-serif; font-size: 16px; line-height: 24px;">RM ${((userOrder.isRefundOrder && userOrder.finalPrice <= 0) ? 0 : (
          userOrder.totalPrice +
          getOrderDiscountInfo(userOrder)
        )).toFixed(2)}</td>
                </tr>
                <tr>
                  <td align="left" width="75%" style="padding: 6px 12px;font-family: 'Source Sans Pro', Helvetica, Arial, sans-serif; font-size: 16px; line-height: 24px;">Discount</td>
                  <td align="right" width="25%" style="padding: 6px 12px;font-family: 'Source Sans Pro', Helvetica, Arial, sans-serif; font-size: 16px; line-height: 24px;">RM ${((userOrder.isRefundOrder && userOrder.finalPrice <= 0) ? 0 : (
          // userOrder.discount +
          getOrderDiscountInfo(userOrder)
        )).toFixed(2)}</td>
                </tr>
                
                ${userOrder.orderType === ORDER_TYPE.DELIVERY ?
          `<tr>
                  <td align="left" width="75%" style="padding: 6px 12px;font-family: 'Source Sans Pro', Helvetica, Arial, sans-serif; font-size: 16px; line-height: 24px;">Delivery</td>
                  <td align="right" width="25%" style="padding: 6px 12px;font-family: 'Source Sans Pro', Helvetica, Arial, sans-serif; font-size: 16px; line-height: 24px;">RM ${((userOrder.isRefundOrder && userOrder.finalPrice <= 0) ? 0 : userOrder.deliveryFee).toFixed(2)}</td>
                </tr>`
          : ''}
                
                ${(currOutletLocal && checkToApplyTaxOrNot(currOutletLocal, userOrder.orderType, userOrder.orderTypeSub)) ?
          `<tr>
                  <td align="left" width="75%" style="padding: 6px 12px;font-family: 'Source Sans Pro', Helvetica, Arial, sans-serif; font-size: 16px; line-height: 24px;">Tax (${(currOutletLocal.taxRate * 100).toFixed(0)}%) ${currOutletLocal.taxNum ? `(${currOutletLocal.taxNum})` : ''}</td>
                  <td align="right" width="25%" style="padding: 6px 12px;font-family: 'Source Sans Pro', Helvetica, Arial, sans-serif; font-size: 16px; line-height: 24px;">RM ${((userOrder.isRefundOrder && userOrder.finalPrice <= 0) ? 0 : userOrder.tax).toFixed(2)}</td>
                </tr>`
          : ''}
                
                ${(!scOtherDApplied && currOutletLocal && checkToApplyScOrNot(currOutletLocal, userOrder.orderType, userOrder.orderTypeSub)) ?
          `<tr>
                  <td align="left" width="75%" style="padding: 6px 12px;font-family: 'Source Sans Pro', Helvetica, Arial, sans-serif; font-size: 16px; line-height: 24px;">${currOutletLocal.scName ? currOutletLocal.scName : 'Service Charge'} (${(currOutletLocal.scRate * 100).toFixed(0)}%)</td>
                  <td align="right" width="25%" style="padding: 6px 12px;font-family: 'Source Sans Pro', Helvetica, Arial, sans-serif; font-size: 16px; line-height: 24px;">RM ${((userOrder.isRefundOrder && userOrder.finalPrice <= 0) ? 0 : userOrder.sc).toFixed(2)}</td>
                </tr>`
          : ''}

        ${(scOtherDApplied && currOutletLocal && currOutletLocal.scActiveOtherD && userOrder.orderType === ORDER_TYPE.PICKUP &&
          userOrder.orderTypeSub === ORDER_TYPE_SUB.OTHER_DELIVERY) ?
          `<tr>
                    <td align="left" width="75%" style="padding: 6px 12px;font-family: 'Source Sans Pro', Helvetica, Arial, sans-serif; font-size: 16px; line-height: 24px;">${currOutletLocal.scNameOtherD ? currOutletLocal.scNameOtherD : 'Service Charge'} (${(currOutletLocal.scRateOtherD * 100).toFixed(0)}%)</td>
                    <td align="right" width="25%" style="padding: 6px 12px;font-family: 'Source Sans Pro', Helvetica, Arial, sans-serif; font-size: 16px; line-height: 24px;">RM ${((userOrder.isRefundOrder && userOrder.finalPrice <= 0) ? 0 : userOrder.sc).toFixed(2)}</td>
                  </tr>`
          : ''}
                
                ${(userOrder.deliveryPackagingFee) ?
          `<tr>
                  <td align="left" width="75%" style="padding: 6px 12px;font-family: 'Source Sans Pro', Helvetica, Arial, sans-serif; font-size: 16px; line-height: 24px;">Delivery Packaging Fee</td>
                  <td align="right" width="25%" style="padding: 6px 12px;font-family: 'Source Sans Pro', Helvetica, Arial, sans-serif; font-size: 16px; line-height: 24px;">RM ${((userOrder.isRefundOrder && userOrder.finalPrice <= 0) ? 0 : userOrder.deliveryPackagingFee).toFixed(2)}</td>
                </tr>`
          : ''}
                
                ${(userOrder.pickupPackagingFee) ?
          `<tr>
                  <td align="left" width="75%" style="padding: 6px 12px;font-family: 'Source Sans Pro', Helvetica, Arial, sans-serif; font-size: 16px; line-height: 24px;">Takeaway Packaging Fee</td>
                  <td align="right" width="25%" style="padding: 6px 12px;font-family: 'Source Sans Pro', Helvetica, Arial, sans-serif; font-size: 16px; line-height: 24px;">RM ${((userOrder.isRefundOrder && userOrder.finalPrice <= 0) ? 0 : userOrder.pickupPackagingFee).toFixed(2)}</td>
                </tr>`
          : ''}
                
                <tr style="margin-vertical: 4px;">
                  <td align="left" width="75%" style="padding: 12px; font-family: 'Source Sans Pro', Helvetica, Arial, sans-serif; font-size: 16px; line-height: 24px; border-top: 2px dashed #4E9F7D; border-bottom: 2px dashed #4E9F7D;"><strong>Total</strong></td>
                  <td align="right" width="25%" style="padding: 12px; font-family: 'Source Sans Pro', Helvetica, Arial, sans-serif; font-size: 16px; line-height: 24px; border-top: 2px dashed #4E9F7D; border-bottom: 2px dashed #4E9F7D;"><strong>RM ${((userOrder.isRefundOrder && userOrder.finalPrice <= 0) ? 0 : userOrder.finalPrice).toFixed(2)}</strong></td>
                </tr>
                
                <tr>
                  <td align="left" width="75%" style="padding: 6px 12px;font-family: 'Source Sans Pro', Helvetica, Arial, sans-serif; font-size: 16px; line-height: 24px;">${((userOrder.paymentDetails && userOrder.paymentDetails.channel) ? (PAYMENT_CHANNEL_NAME_PARSED[userOrder.paymentDetails.channel] ? PAYMENT_CHANNEL_NAME_PARSED[userOrder.paymentDetails.channel] : userOrder.paymentDetails.channel) : 'Cash')}</td>
                  <td align="right" width="25%" style="padding: 6px 12px;font-family: 'Source Sans Pro', Helvetica, Arial, sans-serif; font-size: 16px; line-height: 24px;">RM ${(userOrder.amountReceived ? userOrder.amountReceived.toFixed(2) : amountReceived.toFixed(2))}</td>
                </tr>
                
                <tr>
                  <td align="left" width="75%" style="padding: 6px 12px;font-family: 'Source Sans Pro', Helvetica, Arial, sans-serif; font-size: 16px; line-height: 24px;">Received</td>
                  <td align="right" width="25%" style="padding: 6px 12px;font-family: 'Source Sans Pro', Helvetica, Arial, sans-serif; font-size: 16px; line-height: 24px;">RM ${(userOrder.amountReceived ? userOrder.amountReceived.toFixed(2) : amountReceived.toFixed(2))}</td>
                </tr>
                
                <tr>
                  <td align="left" width="75%" style="padding: 6px 12px;font-family: 'Source Sans Pro', Helvetica, Arial, sans-serif; font-size: 16px; line-height: 24px;">Balance</td>
                  <td align="right" width="25%" style="padding: 6px 12px;font-family: 'Source Sans Pro', Helvetica, Arial, sans-serif; font-size: 16px; line-height: 24px;">RM ${(userOrder.amountBalance ? userOrder.amountBalance.toFixed(2) : amountBalance.toFixed(2))}</td>
                </tr>
              </table>
            </td>
          </tr>
          <!-- end reeipt table -->

        </table>
        <!--[if (gte mso 9)|(IE)]>
        </td>
        </tr>
        </table>
        <![endif]-->
      </td>
    </tr>
    <!-- end copy block -->

    <!-- start receipt address block -->
    <tr>
      <td align="center" bgcolor="#4E9F7D" valign="top" width="100%">
        <!--[if (gte mso 9)|(IE)]>
        <table align="center" border="0" cellpadding="0" cellspacing="0" width="600">
        <tr>
        <td align="center" valign="top" width="600">
        <![endif]-->
        <table align="center" bgcolor="#ffffff" border="0" cellpadding="0" cellspacing="0" width="100%" style="max-width: 600px;">
          <tr>
            <td align="center" valign="top" style="font-size: 0; border-bottom: 3px solid #d4dadf">
              <!--[if (gte mso 9)|(IE)]>
              <table align="center" border="0" cellpadding="0" cellspacing="0" width="600">
              <tr>
              <td align="left" valign="top" width="300">
              <![endif]-->
              <div style="display: inline-block; width: 100%; max-width: 50%; min-width: 240px; vertical-align: top;">
                <table align="left" border="0" cellpadding="0" cellspacing="0" width="100%" style="max-width: 300px;">
                  <tr>
                    <td align="center" valign="top" style="padding-bottom: 36px; padding-left: 36px; font-family: 'Source Sans Pro', Helvetica, Arial, sans-serif; font-size: 16px; line-height: 24px;">
                      <p><strong>${userOrder.outletName} (${userOrder.outletPhone ? userOrder.outletPhone : 'N/A'})</strong></p>
                      <p>${userOrder.outletAddress ? userOrder.outletAddress : 'N/A'}</p>
                    </td>
                  </tr>
                </table>
              </div>
              <!--[if (gte mso 9)|(IE)]>
              </td>
              <td align="left" valign="top" width="300">
              <![endif]-->
              <!--<div style="display: inline-block; width: 100%; max-width: 50%; min-width: 240px; vertical-align: top;">
                <table align="left" border="0" cellpadding="0" cellspacing="0" width="100%" style="max-width: 300px;">
                  <tr>
                    <td align="left" valign="top" style="padding-bottom: 36px; padding-left: 36px; font-family: 'Source Sans Pro', Helvetica, Arial, sans-serif; font-size: 16px; line-height: 24px;">
                      <p><strong>Billing Address</strong></p>
                      <p>1234 S. Broadway Ave<br>Unit 2<br>Denver, CO 80211</p>
                    </td>
                  </tr>
                </table>
              </div>-->
              <!--[if (gte mso 9)|(IE)]>
              </td>
              </tr>
              </table>
              <![endif]-->
            </td>
          </tr>
        </table>
        <!--[if (gte mso 9)|(IE)]>
        </td>
        </tr>
        </table>
        <![endif]-->
      </td>
    </tr>
    <!-- end receipt address block -->

    <!-- start footer -->
    <tr>
      <td align="center" bgcolor="#4E9F7D" style="padding: 24px;">
        <!--[if (gte mso 9)|(IE)]>
        <table align="center" border="0" cellpadding="0" cellspacing="0" width="600">
        <tr>
        <td align="center" valign="top" width="600">
        <![endif]-->
        <table border="0" cellpadding="0" cellspacing="0" width="100%" style="max-width: 600px;">

          <!-- start permission -->
          <!--<tr>
            <td align="center" bgcolor="#4E9F7D" style="padding: 12px 24px; font-family: 'Source Sans Pro', Helvetica, Arial, sans-serif; font-size: 14px; line-height: 20px; color: #666;">
              <p style="margin: 0;">You received this email because we received a request for [type_of_action] for your account. If you didn't request [type_of_action] you can safely delete this email.</p>
            </td>
          </tr>-->
          <!-- end permission -->

          <!-- start unsubscribe -->
          <tr>
            <td align="center" bgcolor="#4E9F7D" style="padding: 12px 24px; font-family: 'Source Sans Pro', Helvetica, Arial, sans-serif; font-size: 14px; line-height: 20px;">
              <!--<p style="margin: 0;">To stop receiving these emails, you can <a href="https://sendgrid.com" target="_blank">unsubscribe</a> at any time.</p>-->
              <p style="margin: 0;">&copy;
    <span id="copyright">
        2023
    </span>
    KooDoo</p>
            </td>
          </tr>
          <!-- end unsubscribe -->

        </table>
        <!--[if (gte mso 9)|(IE)]>
        </td>
        </tr>
        </table>
        <![endif]-->
      </td>
    </tr>
    <!-- end footer -->

  </table>
  <!-- end body -->

</body>
</html>
    `,
    };

    await new Promise(async (resolve, reject) => {
      ApiClient.POST(API.sendGeneralHTMLEmail, body).then((result) => {
        // callback && callback();

        //////////////////////////////

        // 2024-04-23 - set email info on crmUser

        try {
          if (currToPaidUser && userOrder.crmUserId && currToPaidUser && currToPaidUser.uniqueId) {
            if (currToPaidUser.emailSecond !== emailToSent) {
              firestore().collection(Collections.CRMUser).doc(currToPaidUser.uniqueId).update({
                emailSecond: emailToSent,

                updatedAt: Date.now(),
              });
            }
          }
        }
        catch (ex) {
          console.error(ex);
        }

        //////////////////////////////

        resolve();
      });
    });
  }
};

export const sendTextEmail = async (
  emailToSent,
  textContent,
) => {
  var body = {
    email: emailToSent,
    title: `[KooDoo] ${global.currOutlet ? global.currOutlet.subdomain : ''} - ${moment().format('YYYY-MM-DD, HH:mm:ss A')}`,
    html: `
    <!DOCTYPE html>
  <html>
  <head>
  </head>
  <body>${textContent}</body>
  </html>
    `
  };

  await new Promise(async (resolve, reject) => {
    ApiClient.POST(API.sendGeneralHTMLEmail, body).then((result) => {
      // callback && callback();

      //////////////////////////////

      // 2024-04-23 - set email info on crmUser

      try {
        // if (currToPaidUser && userOrder.crmUserId && currToPaidUser && currToPaidUser.uniqueId) {
        //   if (currToPaidUser.emailSecond !== emailToSent) {
        //     firestore().collection(Collections.CRMUser).doc(currToPaidUser.uniqueId).update({
        //       emailSecond: emailToSent,

        //       updatedAt: Date.now(),
        //     });
        //   }
        // }
      }
      catch (ex) {
        console.error(ex);
      }

      //////////////////////////////

      resolve();
    });
  });
};

export const lockOutletTable = async (table) => {
  if (global.tokenFcm && table && table.uniqueId &&
    table.lockedToken !== global.tokenFcm) {
    // firestore().collection(Collections.OutletTable).doc(table.uniqueId).update({
    //   lockedToken: global.tokenFcm,

    //   // updatedAt: Date.now(),
    // });
  }
};

export const unlockOutletTable = async (table) => {
  if (global.tokenFcm && table && table.uniqueId &&
    table.lockedToken === global.tokenFcm) {
    // firestore().collection(Collections.OutletTable).doc(table.uniqueId).update({
    //   lockedToken: '',

    //   // updatedAt: Date.now(),
    // });
  }
};

export const checkIsAllowPromotionVoucherToApply = (
  allowStackedPromotionVoucher = false,
  applyDiscountType = APPLY_DISCOUNT_TYPE.PROMOTION,
  campaignData = [],
  selectedPromoCodePromotion = {},
  selectedTaggableVoucher = {},
  promotionIdAppliedList = [],
  cartItems = [],
) => {
  if (allowStackedPromotionVoucher) {
    return true;
  }
  else if (applyDiscountType === APPLY_DISCOUNT_TYPE.PROMOTION) {
    // merchant's own promotion

    if (selectedPromoCodePromotion && selectedPromoCodePromotion.uniqueId) {
      return false;
    }
    else if (selectedTaggableVoucher && selectedTaggableVoucher.uniqueId) {
      return false;
    }
    else {
      // check if got multiple merchant promotion (without promo code)

      if (promotionIdAppliedList.length > 1
        // ||
        // (
        //   campaignData.qtyRp > 0 // qtyRp campaign can go inside for further checking
        // )
      ) {
        // means got more than 1 promotion

        if (promotionIdAppliedList.includes(campaignData.promotionId)) {
          // if is own one, can continue (might be per order/[item], that applied for multiple items)

          // if (campaignData.qtyRp > 0) {
          //   // if is required purchased campaign, can skip, as can only applied once

          //   return false;
          // }
          // else {
          //   return false;
          // }

          return true;
        }
        else {
          return false;
        }
      }
      else {
        return true;
      }
    }
  }
  else if (applyDiscountType === APPLY_DISCOUNT_TYPE.PROMOTION_PROMO_CODE) {
    // promo code promotion

    if (selectedTaggableVoucher && selectedTaggableVoucher.uniqueId) {
      return false;
    }
    else {
      // check if got multiple merchant promotion (without promo code)

      // if (promotionIdAppliedList.length > 1) {
      //     // means already got 1

      //     if (promotionIdAppliedList.includes(campaignData.promotionId)) {
      //         // if is own one, can continue (might be per order/[item], that applied for multiple items)

      //         return true;
      //     }
      //     else {
      //         return false;
      //     }
      // }
      // else {
      //     return true;
      // }

      return true;
    }
  }
  else if (applyDiscountType === APPLY_DISCOUNT_TYPE.VOUCHER) {
    // voucher

    // return true;

    if (campaignData.type === PROMOTION_TYPE.BUY_A_GET_B_FOR_FREE) {
      if (promotionIdAppliedList.includes(campaignData.promotionId) ||
        cartItems.find(cartItem => cartItem.promotionId === campaignData.promotionId)) {
        // if is own one, can continue (might be per order/[item], that applied for multiple items)

        return false;
      }
      else {
        return true;
      }
    }
    else {
      return true;
    }
  }
};

export const encodeGeohash = (lat, lon, precision = 12) => {
  const BITS = [16, 8, 4, 2, 1];
  const BASE32 = '0123456789bcdefghjkmnpqrstuvwxyz';
  let is_even = true;
  let lat_min = -90.0; let lat_max = 90.0;
  let lon_min = -180.0; let lon_max = 180.0;
  let geohash = '';

  while (geohash.length < 10) {
    let bit = 0;
    for (let b of BITS) {
      if (is_even) {
        let mid = (lon_min + lon_max) / 2;
        if (lon >= mid) {
          bit |= b;
          lon_min = mid;
        } else {
          lon_max = mid;
        }
      } else {
        let mid = (lat_min + lat_max) / 2;
        if (lat >= mid) {
          bit |= b;
          lat_min = mid;
        } else {
          lat_max = mid;
        }
      }
      is_even = !is_even;
    }
    geohash += BASE32[bit];
  }
  return geohash;
}

export const saveEmployeeData = async (allOutletsEmployees) => {
  await AsyncStorage.setItem('employees', allOutletsEmployees ? JSON.stringify(allOutletsEmployees.map(employee => {
    return {
      firebaseUid: employee.firebaseUid ? employee.firebaseUid : '',
      name: employee.name ? employee.name : '',
      gender: employee.gender ? employee.gender : '',
      race: employee.race ? employee.race : '',
      state: employee.state ? employee.state : '',
      number: employee.number ? employee.number : '',
      email: employee.email ? employee.email : '',
      dob: employee.dob ? employee.dob : '',
      role: employee.role ? employee.role : '',
      merchantId: employee.merchantId ? employee.merchantId : '',
      outletId: employee.outletId ? employee.outletId : '',
      avatar: employee.avatar ? employee.avatar : '',
      refreshToken: employee.refreshToken ? employee.refreshToken : '',

      uniqueName: employee.email ? employee.email : '',

      pinNo: employee.pinNo ? employee.pinNo : '0000',

      /////////////////////////////////

      pendingRegistration: employee.pendingRegistration ? employee.pendingRegistration : false,

      /////////////////////////////////

      privileges: employee.privileges ? employee.privileges : [],
      screensToBlock: employee.screensToBlock ? employee.screensToBlock : [],

      isMasterAccount: employee.isMasterAccount !== undefined ? employee.isMasterAccount : (employee.role === ROLE_TYPE.ADMIN ? true : false),

      createdAt: employee.createdAt,
      updatedAt: employee.updatedAt,
      // deletedAt: employee.deletedAt, // can't store null value in async storage
    };
  })) : JSON.stringify([]));

  global.employees = allOutletsEmployees;
};

export const compareOrderDateByDisplayType = (convertDate, orderDate, cmpType, cmpUnit, reportDisplayType, outletShifts) => {
  if (reportDisplayType === REPORT_DISPLAY_TYPE.DAY) {
    if (cmpType === DATE_COMPARE_TYPE.IS_SAME) {
      if (cmpUnit === '') {
        return moment(convertDate).isSame(orderDate);
      }
      else {
        return moment(convertDate).isSame(orderDate, cmpUnit);
      }
    }
    else if (cmpType === DATE_COMPARE_TYPE.IS_SAME_OR_AFTER) {
      if (cmpUnit === '') {
        return moment(convertDate).isSameOrAfter(orderDate);
      }
      else {
        return moment(convertDate).isSameOrAfter(orderDate, cmpUnit);
      }
    }
    else if (cmpType === DATE_COMPARE_TYPE.IS_SAME_OR_BEFORE) {
      if (cmpUnit === '') {
        return moment(convertDate).isSameOrBefore(orderDate);
      }
      else {
        return moment(convertDate).isSameOrBefore(orderDate, cmpUnit);
      }
    }
    else if (cmpType === DATE_COMPARE_TYPE.IS_AFTER) {
      if (cmpUnit === '') {
        return moment(convertDate).isAfter(orderDate);
      }
      else {
        return moment(convertDate).isAfter(orderDate, cmpUnit);
      }
    }
    else if (cmpType === DATE_COMPARE_TYPE.IS_BEFORE) {
      if (cmpUnit === '') {
        return moment(convertDate).isBefore(orderDate);
      }
      else {
        return moment(convertDate).isBefore(orderDate, cmpUnit);
      }
    }
    else {
      return false;
    }
  }
  else if (reportDisplayType === REPORT_DISPLAY_TYPE.SHIFT) {
    // console.log('=========================================');
    // console.log(`checking for new order - ${cmpType} - ${cmpUnit}`);
    // console.log(`order date: ${moment(orderDate).format('YYYY/MM/DD hh:mm A')}`);
    // console.log(`convert date: ${moment(convertDate).format('YYYY/MM/DD hh:mm A')}`);

    if (cmpType === DATE_COMPARE_TYPE.IS_SAME) {
      if (cmpUnit === '') {
        for (var i = 0; i < outletShifts.length; i++) {
          // console.log(`${i}, shift, open date: ${moment(outletShifts[i].openDate).format('YYYY/MM/DD hh:mm A')} | close date: ${outletShifts[i].closeDate ? moment(outletShifts[i].closeDate).format('YYYY/MM/DD hh:mm A') : 'N/A'}`);

          // see if the checking date (ex: 05-19)
          if (
            moment(convertDate).isSame(outletShifts[i].openDate, 'day') &&
            moment(orderDate).isSameOrAfter(outletShifts[i].openDate)
          ) {
            if (outletShifts[i].closeDate) {
              // console.log(`${i}, shift, close date: ${moment(outletShifts[i].closeDate).format('YYYY/MM/DD hh:mm A')}`);

              if (moment(orderDate).isBefore(outletShifts[i].closeDate)) {
                // console.log('valid match');

                return true;
              }
            }
            else {
              // console.log('valid match');

              return true;
            }
          }
        }
      }
      else {
        // if got passed 'hour', 'day',

        for (var i = 0; i < outletShifts.length; i++) {
          // console.log(`${i}, shift, open date: ${moment(outletShifts[i].openDate).format('YYYY/MM/DD hh:mm A')} | close date: ${outletShifts[i].closeDate ? moment(outletShifts[i].closeDate).format('YYYY/MM/DD hh:mm A') : 'N/A'}`);

          // see if the checking date (ex: 05-19)
          if (
            moment(convertDate).isSame(outletShifts[i].openDate, (cmpUnit === 'hour' ? 'day' : cmpUnit)) &&
            moment(orderDate).isSameOrAfter(outletShifts[i].openDate, cmpUnit)
          ) {
            if (outletShifts[i].closeDate) {
              // console.log(`${i}, shift, close date: ${moment(outletShifts[i].closeDate).format('YYYY/MM/DD hh:mm A')}`);

              if (moment(orderDate).isBefore(outletShifts[i].closeDate, cmpUnit)) {
                // console.log('valid match');

                return true;
              }
            }
            else {
              // console.log('valid match');              

              return true;
            }
          }
        }
      }
    }
    else if (cmpType === DATE_COMPARE_TYPE.IS_SAME_OR_AFTER) {
      let closeDate = moment().valueOf();

      if (cmpUnit === '') {
        for (var i = 0; i < outletShifts.length; i++) {
          // console.log(`${i}, shift, open date: ${moment(outletShifts[i].openDate).format('YYYY/MM/DD hh:mm A')} | close date: ${outletShifts[i].closeDate ? moment(outletShifts[i].closeDate).format('YYYY/MM/DD hh:mm A') : 'N/A'}`);

          if (outletShifts[i].closeDate) {
            // console.log(`${i}, shift, close date: ${moment(outletShifts[i].closeDate).format('YYYY/MM/DD hh:mm A')}`);

            closeDate = outletShifts[i].closeDate;
          }

          // see if the checking date (ex: 05-19)
          if (
            moment(convertDate).isSame(outletShifts[i].openDate, 'day') &&
            moment(closeDate).isAfter(orderDate)
          ) {
            return true;
          }
        }
      }
      else {
        for (var i = 0; i < outletShifts.length; i++) {
          // console.log(`${i}, shift, open date: ${moment(outletShifts[i].openDate).format('YYYY/MM/DD hh:mm A')} | close date: ${outletShifts[i].closeDate ? moment(outletShifts[i].closeDate).format('YYYY/MM/DD hh:mm A') : 'N/A'}`);

          if (outletShifts[i].closeDate) {
            // console.log(`${i}, shift, close date: ${moment(outletShifts[i].closeDate).format('YYYY/MM/DD hh:mm A')}`);

            closeDate = outletShifts[i].closeDate;
          }

          // see if the checking date (ex: 05-19)
          if (
            moment(convertDate).isSame(outletShifts[i].openDate, (cmpUnit === 'hour' ? 'day' : cmpUnit)) &&
            moment(closeDate).isAfter(orderDate, cmpUnit)
          ) {
            return true;
          }
        }
      }
    }
    else if (cmpType === DATE_COMPARE_TYPE.IS_SAME_OR_BEFORE) {
      if (cmpUnit === '') {
        for (var i = 0; i < outletShifts.length; i++) {
          // console.log(`${i}, shift, open date: ${moment(outletShifts[i].openDate).format('YYYY/MM/DD hh:mm A')} | close date: ${outletShifts[i].closeDate ? moment(outletShifts[i].closeDate).format('YYYY/MM/DD hh:mm A') : 'N/A'}`);

          // see if the checking date (ex: 05-19)
          if (
            moment(convertDate).isSame(outletShifts[i].openDate, 'day') &&
            moment(outletShifts[i].openDate).isSameOrBefore(orderDate)
          ) {
            return true;
          }
        }
      }
      else {
        for (var i = 0; i < outletShifts.length; i++) {
          // console.log(`${i}, shift, open date: ${moment(outletShifts[i].openDate).format('YYYY/MM/DD hh:mm A')} | close date: ${outletShifts[i].closeDate ? moment(outletShifts[i].closeDate).format('YYYY/MM/DD hh:mm A') : 'N/A'}`);

          // see if the checking date (ex: 05-19)
          if (
            moment(convertDate).isSame(outletShifts[i].openDate, (cmpUnit === 'hour' ? 'day' : cmpUnit)) &&
            moment(outletShifts[i].openDate).isSameOrBefore(orderDate, cmpUnit)
          ) {
            return true;
          }
        }
      }
    }
    else if (cmpType === DATE_COMPARE_TYPE.IS_AFTER) {
      let closeDate = moment().valueOf();

      if (cmpUnit === '') {
        for (var i = 0; i < outletShifts.length; i++) {
          // console.log(`${i}, shift, open date: ${moment(outletShifts[i].openDate).format('YYYY/MM/DD hh:mm A')} | close date: ${outletShifts[i].closeDate ? moment(outletShifts[i].closeDate).format('YYYY/MM/DD hh:mm A') : 'N/A'}`);

          if (outletShifts[i].closeDate) {
            // console.log(`${i}, shift, close date: ${moment(outletShifts[i].closeDate).format('YYYY/MM/DD hh:mm A')}`);

            closeDate = outletShifts[i].closeDate;
          }

          // see if the checking date (ex: 05-19)
          if (
            moment(convertDate).isSame(outletShifts[i].openDate, 'day') &&
            moment(outletShifts[i].openDate).isAfter(orderDate) &&
            moment(closeDate).isAfter(orderDate)
          ) {
            return true;
          }
        }
      }
      else {
        for (var i = 0; i < outletShifts.length; i++) {
          // console.log(`${i}, shift, open date: ${moment(outletShifts[i].openDate).format('YYYY/MM/DD hh:mm A')} | close date: ${outletShifts[i].closeDate ? moment(outletShifts[i].closeDate).format('YYYY/MM/DD hh:mm A') : 'N/A'}`);

          if (outletShifts[i].closeDate) {
            // console.log(`${i}, shift, close date: ${moment(outletShifts[i].closeDate).format('YYYY/MM/DD hh:mm A')}`);

            closeDate = outletShifts[i].closeDate;
          }

          // see if the checking date (ex: 05-19)
          if (
            moment(convertDate).isSame(outletShifts[i].openDate, (cmpUnit === 'hour' ? 'day' : cmpUnit)) &&
            moment(outletShifts[i].openDate).isAfter(orderDate, cmpUnit) &&
            moment(closeDate).isAfter(orderDate, cmpUnit)
          ) {
            return true;
          }
        }
      }
    }
    else if (cmpType === DATE_COMPARE_TYPE.IS_BEFORE) {
      let closeDate = moment().valueOf();

      if (cmpUnit === '') {
        for (var i = 0; i < outletShifts.length; i++) {
          // console.log(`${i}, shift, open date: ${moment(outletShifts[i].openDate).format('YYYY/MM/DD hh:mm A')} | close date: ${outletShifts[i].closeDate ? moment(outletShifts[i].closeDate).format('YYYY/MM/DD hh:mm A') : 'N/A'}`);

          if (outletShifts[i].closeDate) {
            // console.log(`${i}, shift, close date: ${moment(outletShifts[i].closeDate).format('YYYY/MM/DD hh:mm A')}`);

            closeDate = outletShifts[i].closeDate;
          }

          // see if the checking date (ex: 05-19)
          if (
            moment(convertDate).isSame(outletShifts[i].openDate, 'day') &&
            moment(outletShifts[i].openDate).isBefore(orderDate) &&
            moment(closeDate).isBefore(orderDate)
          ) {
            return true;
          }
        }
      }
      else {
        for (var i = 0; i < outletShifts.length; i++) {
          // console.log(`${i}, shift, open date: ${moment(outletShifts[i].openDate).format('YYYY/MM/DD hh:mm A')} | close date: ${outletShifts[i].closeDate ? moment(outletShifts[i].closeDate).format('YYYY/MM/DD hh:mm A') : 'N/A'}`);

          if (outletShifts[i].closeDate) {
            // console.log(`${i}, shift, close date: ${moment(outletShifts[i].closeDate).format('YYYY/MM/DD hh:mm A')}`);

            closeDate = outletShifts[i].closeDate;
          }

          // see if the checking date (ex: 05-19)
          if (
            moment(convertDate).isSame(outletShifts[i].openDate, (cmpUnit === 'hour' ? 'day' : cmpUnit)) &&
            moment(outletShifts[i].openDate).isBefore(orderDate, cmpUnit) &&
            moment(closeDate).isBefore(orderDate, cmpUnit)
          ) {
            return true;
          }
        }
      }
    }
    else {
      return false;
    }
  }
};

// ex:
// subtotal: rm 50
// cart item discount: rm 10
// * whole order discount: rm 5
// subtotal (original): rm 60
// final price: rm 45

// this function returns rm 10 only
export const getOrderDiscountInfo = (order, cartItemListToIncludedOnly = []) => {
  let upsellIdList = [];

  const cartItemDiscount = order.cartItems.reduce((accum, cartItem) => {
    if (cartItemListToIncludedOnly.length > 0 && cartItemListToIncludedOnly.find(findItem => {
      if (findItem.itemId === cartItem.itemId && findItem.cartItemDate === cartItem.cartItemDate) {
        return true;
      }
      else {
        return false;
      }
    }) === undefined) {
      return accum;
    }
    else {
      if (cartItem.upsellId) {
        upsellIdList.push(cartItem.upsellId);
      }

      if (cartItem.upsellingCampaignId) {
        upsellIdList.push(cartItem.upsellingCampaignId);
      }

      return accum + (cartItem.discount ? cartItem.discount : 0);
    }
  }, 0);

  const orderDiscount = order.discount;

  // console.log('order getOrderDiscountInfo');
  // console.log(order);
  // console.log(cartItemDiscount);
  // console.log(orderDiscount);

  // if (orderDiscount > cartItemDiscount) {
  //   // means got applied order discount (whole order)
  //   // or applied manual discount over promotion/voucher
  // }

  if (order.discOd && order.discOd > 0) {
    if (cartItemDiscount > 0) {
      // for promotion, voucher, cart item discount

      if (
        (order.promotionIdList && order.promotionIdList.length > 0)
        ||
        (order.promoCodePromotionIdList && order.promoCodePromotionIdList.length > 0)
        ||
        (order.taggableVoucherId)
        ||
        (upsellIdList.length > 0)
      ) {
        return cartItemDiscount +
          // order.discOd + 
          0 +
          (order.discAam ? order.discAam : 0);
      }
      else {
        // should be manual discount by item (a bit special for this)

        if (cartItemListToIncludedOnly.length > 0) {
          // return the cartItemDiscount directly, if is showing the discount info based on item

          return cartItemDiscount +
            // order.discOd
            0
            ;
        }
        else {
          return 0 +
            // order.discOd +
            0 +
            (order.discAam ? order.discAam : 0); // return 0 can d 
        }
      }

      // return (orderDiscount > cartItemDiscount) ? orderDiscount : cartItemDiscount;

    }
    else if (orderDiscount > 0 && cartItemDiscount <= 0) {
      // for discount based on order

      // return orderDiscount;
      return 0 +
        // order.discOd +
        0 +
        (order.discAam ? order.discAam : 0); // return 0 here
    }
    else {
      return 0 +
        // order.discOd +
        0 +
        (order.discAam ? order.discAam : 0);
    }
  }
  else {
    if (cartItemDiscount > 0) {
      // for promotion, voucher, cart item discount

      if (
        (order.promotionIdList && order.promotionIdList.length > 0)
        ||
        (order.promoCodePromotionIdList && order.promoCodePromotionIdList.length > 0)
        ||
        (order.taggableVoucherId)
        ||
        (upsellIdList.length > 0)
      ) {
        return cartItemDiscount + (order.discAam ? order.discAam : 0);
      }
      else {
        // should be manual discount by item (a bit special for this)

        if (cartItemListToIncludedOnly.length > 0) {
          // return the cartItemDiscount directly, if is showing the discount info based on item

          return cartItemDiscount;
        }
        else {
          return 0; // return 0 can d 
        }
      }

      // return (orderDiscount > cartItemDiscount) ? orderDiscount : cartItemDiscount;

    }
    else if (orderDiscount > 0 && cartItemDiscount <= 0) {
      // for discount based on order

      // return orderDiscount;
      return 0; // return 0 here
    }
    else if (order.discAam > 0) {
      return order.discAam + orderDiscount;
    }
    else {
      return 0;
    }
  }
};

// ex:
// subtotal: rm 50
// cart item discount: rm 10
// * whole order discount: rm 5
// subtotal (original): rm 60
// final price: rm 45

// this function returns rm 15
export const getOrderDiscountInfoInclOrderBased = (order, cartItemListToIncludedOnly = []) => {
  const cartItemDiscount = order.cartItems.reduce((accum, cartItem) => {
    if (cartItemListToIncludedOnly.length > 0 && cartItemListToIncludedOnly.find(findItem => {
      if (findItem.itemId === cartItem.itemId && findItem.cartItemDate === cartItem.cartItemDate) {
        return true;
      }
      else {
        return false;
      }
    }) === undefined) {
      return accum;
    }
    else {
      return accum + (cartItem.discount ? cartItem.discount : 0);
    }
  }, 0);
  const orderDiscount = order.discount;

  // console.log('order getOrderDiscountInfoInclOrderBased');
  // console.log(order);
  // console.log(cartItemDiscount);
  // console.log(orderDiscount);

  // if (orderDiscount > cartItemDiscount) {
  //   // means got applied order discount (whole order)
  //   // or applied manual discount over promotion/voucher
  // }

  if (order.discOd && order.discOd > 0) {
    if (cartItemDiscount > 0) {
      // for promotion, voucher, cart item discount

      if (cartItemListToIncludedOnly.length > 0) {
        return cartItemDiscount + order.discOd;
      }
      else {
        return ((orderDiscount > cartItemDiscount) ? orderDiscount : cartItemDiscount) + order.discOd + (order.discAam ? order.discAam : 0);
        // return cartItemDiscount;
      }
    }
    else if (orderDiscount > 0 && cartItemDiscount <= 0) {
      // for discount based on order

      return (cartItemListToIncludedOnly.length > 0 ? 0 : orderDiscount) + order.discOd + (order.discAam ? order.discAam : 0);
    }
    else {
      return order.discOd + (order.discAam ? order.discAam : 0);
    }
  }
  else {
    if (cartItemDiscount > 0) {
      // for promotion, voucher, cart item discount

      if (cartItemListToIncludedOnly.length > 0) {
        return cartItemDiscount;
      }
      else {
        return ((orderDiscount > cartItemDiscount) ? orderDiscount : cartItemDiscount) + (order.discAam ? order.discAam : 0);
        // return cartItemDiscount;
      }
    }
    else if (orderDiscount > 0 && cartItemDiscount <= 0) {
      // for discount based on order

      return (cartItemListToIncludedOnly.length > 0 ? 0 : orderDiscount) + (order.discAam ? order.discAam : 0);
    }
    else if (order.discAam > 0) {
      return order.discAam + orderDiscount;
    }
    else {
      return 0;
    }
  }
};

export const performResize = (deviceInfo, deviceName, scaleToFit, scaleUp, landscapeMode, device_width, device_height, device_font_scale) => {
  logToFile(`performResize`);
  console.log('performResize');

  if (
    // deviceInfo.windowPhysicalPixels.height >
    // deviceInfo.windowPhysicalPixels.width
    false
  ) {
    // skip
  }
  else {
    // Make sure you are using copy of deviceInfo.windowPhysicalPixels
    const windowPhysicalPixels = {
      ...deviceInfo.windowPhysicalPixels,
      ...landscapeMode
      &&
      // (deviceInfo.windowPhysicalPixels >
      //   deviceInfo.windowPhysicalPixels.width) &&
      {
        width: deviceInfo.windowPhysicalPixels.height,
        height: deviceInfo.windowPhysicalPixels.width,
      },
    };
    const { width, height, scale } = windowPhysicalPixels;
    const w_points = Math.round(width / scale); // round because of JS
    const h_points = Math.round(height / scale); // round because of JS
    let infoSuffix = '';

    windowPhysicalPixels.fontScale = device_font_scale;

    console.log({ width, height, scale, w_points, h_points });

    // Scale to fit if needed
    if (scaleToFit && (w_points != device_width || h_points != device_height)) {
      const w_ratio = w_points / device_width;
      const h_ratio = h_points / device_height;
      const min_ratio = w_ratio < h_ratio ? w_ratio : h_ratio;
      const max_ratio = w_ratio > h_ratio ? w_ratio : h_ratio;

      if ((max_ratio <= 1 && scaleUp) || min_ratio > 1 || max_ratio > 1) {
        const ratio = min_ratio > 1 ? min_ratio : max_ratio;
        console.log({ ratio });

        windowPhysicalPixels.width = Math.round(windowPhysicalPixels.width / ratio);
        windowPhysicalPixels.height = Math.round(windowPhysicalPixels.height / ratio);
        windowPhysicalPixels.fontScale /= ratio;
      }

      console.log(`${device_width}x${device_height}`, { w_ratio, h_ratio, min_ratio, max_ratio });

      const { width: w, height: h } = windowPhysicalPixels;
      infoSuffix = ` (scaled to ${w}x${h})`;
    }

    windowPhysicalPixels.deviceName = deviceName;

    // Force RN to re-set the Dimensions sizes
    if (Platform.OS === 'ios') {
      Dimensions.set({ windowPhysicalPixels });
    }
    else {
      Dimensions.set({ windowPhysicalPixels });
      // Dimensions.set({ screenPhysicalPixels: windowPhysicalPixels });
    }
    console.log(`Resizing window to physical pixels ${width}x${height}${infoSuffix}`, { ...windowPhysicalPixels });
    // TODO: Android uses screenPhysicalPixels - see https://github.com/facebook/react-native/blob/master/Libraries/Utilities/Dimensions.js
  }
};

export const fitAreaNewIntoAreaOriginal = () => {
  const targetCenterX = global.windowWidthOriginal / 2;
  const targetCenterY = global.windowHeightOriginal / 2;

  const centerX = global.windowWidthSimulate / 2;
  const centerY = global.windowHeightSimulate / 2;

  const scaleX = global.windowWidthOriginal / global.windowWidthSimulate;
  const scaleY = global.windowHeightOriginal / global.windowHeightSimulate;

  const scaleFactor = Math.min(scaleX, scaleY);

  const newWidth = global.windowWidthSimulate * scaleFactor;
  const newHeight = global.windowHeightSimulate * scaleFactor;
  // const newWidth = global.windowWidthSimulate * 0.4
  // const newHeight = global.windowHeightSimulate * 0.4;

  const newX = targetCenterX - (newWidth / 2);
  const newY = targetCenterY - (newHeight / 2);

  const scaledWidthOriginal = global.windowWidthOriginal * 0.4;
  const scaledHeightOriginal = global.windowHeightOriginal * 0.4;

  const scaledWidthSimulate = global.windowWidthSimulate * 0.4;
  const scaledHeightSimulate = global.windowHeightSimulate * 0.4;

  return {
    targetCenterX,
    targetCenterY,
    centerX,
    centerY,
    scaleX,
    scaleY,
    scaleFactor,
    newWidth,
    newHeight,
    newX,
    newY,

    scaledWidthOriginal,
    scaledHeightOriginal,
    scaledWidthSimulate,
    scaledHeightSimulate,
  };

  // return [
  //   { scale: (scaleFactor) },    
  //   // { scale: 0.4 }, 
  //   { translateX: -(newWidth - targetCenterX + newX) },
  //   // { translateX: -(targetCenterX - (newX / 2))  },
  //   { translateY: -(targetCenterY * 2) + (newY * 1.5) },
  //   // { translateX: -(newWidth) },
  //   // { translateY: -(newX + newHeight) },
  //   // { scale: scaleFactor },        
  // ];

  //////////////////////////////////////////////////////////////

  // // Calculate the aspect ratios of the target area and area C
  // let targetAspect = global.windowWidthOriginal / global.windowHeightOriginal;
  // let areaCAspect = global.windowWidthSimulate / global.windowHeightSimulate;

  // // Scale area C based on the aspect ratios

  // let scale = 1;
  // if (areaCAspect > targetAspect)
  //   // Area C is wider, so scale its width to match the target area's width
  //   scale = global.windowWidthOriginal / global.windowWidthSimulate
  // else
  //   // Area C is taller, so scale its height to match the target area's height
  //   scale = global.windowHeightOriginal / global.windowHeightSimulate

  // // Scale area C while maintaining its aspect ratio
  // let scaledWidth = global.windowWidthSimulate * scale
  // let scaledHeight = global.windowHeightSimulate * scale

  // // Calculate the position of area C within the target area
  // let offsetX = (global.windowWidthOriginal - scaledWidth) / 2
  // let offsetY = (global.windowHeightOriginal - scaledHeight) / 2

  // // Move and scale area C using CSS transform
  // // areaC.setTransform(scale, 0, 0, scale, offsetX, offsetY)

  // return [
  //   // { scale: scale },    
  //   { scale: 0.4 }, 
  //   { translateX: -offsetX },
  //   { translateY: -offsetY },
  //   // { scale: scaleFactor },        
  // ];

  /////////////////////////////////////////////////////////////////

  // // Step 1: Calculate the center coordinates of Area A
  // const targetCenterX = (global.windowWidthOriginal / 2) + 0;
  // const targetCenterY = (global.windowHeightOriginal / 2) + 0;

  // // Step 2: Calculate the center coordinates of Area C
  // const areaCCenterX = (global.windowWidthSimulate / 2) + 0;
  // const areaCCenterY = (global.windowHeightSimulate / 2) + 0;

  // // Step 3: Calculate the translation distance
  // const translateX = targetCenterX - areaCCenterX;
  // const translateY = targetCenterY - areaCCenterY;

  // // Step 4: Calculate the scaling factor
  // const scaleX = global.windowWidthOriginal / global.windowWidthSimulate;
  // const scaleY = global.windowHeightOriginal / global.windowHeightSimulate;
  // const scaleFactor = Math.min(scaleX, scaleY);

  // return [
  //   { scale: 0.4 },    
  //   { translateX: translateX },
  //   { translateY: translateY },
  //   // { scale: scaleFactor },        
  // ];

  /////////////////////////////////////////////////////////////////

  // // Calculate the scale factor
  // const scale = Math.min(global.windowWidthOriginal / global.windowWidthSimulate, global.windowHeightOriginal / global.windowHeightSimulate);

  // // Calculate the translation values to center Area C
  // const translateX = (global.windowWidthOriginal - global.windowWidthSimulate * scale) / 2;
  // const translateY = (global.windowHeightOriginal - global.windowHeightSimulate * scale) / 2;

  // return [
  //   { scale: scale },
  //   { translateX: translateX },
  //   { translateY: translateY },
  // ];
};

export const getTransformForScreenOutsideNavigation = () => {
  var result = {};
  if (global.simulateTabletMode) {
    result = fitAreaNewIntoAreaOriginal();
  }

  console.log('getTransformForScreenOutsideNavigation');
  console.dir(result, { depth: null });
  console.log(`result.targetCenterX: ${result.targetCenterX}`);
  console.log(`result.targetCenterY: ${result.targetCenterY}`);
  console.log(`global.windowWidthSimulate: ${global.windowWidthSimulate}`);
  console.log(`global.windowHeightSimulate: ${global.windowHeightSimulate}`);

  return {
    ...global.simulateTabletMode && {
      transform: [
        { translateX: result.targetCenterX },
        { translateY: result.targetCenterY },
        { scaleX: 0.4 },
        { scaleY: 0.4 },
        { translateX: __DEV__ ? -global.windowWidthSimulate * 0.61 : -global.windowWidthSimulate * 0.51 },
        { translateY: __DEV__ ? -global.windowHeightSimulate * 0.65 : -global.windowHeightSimulate * 0.325 },

        // { scaleX: 0.4 },
        // { scaleY: 0.4 },
        // { translateX: -420 },
        // { translateY: -600 },

        // { translateY: result.targetCenterY - result.centerY },
      ],
      // transform: fitAreaNewIntoAreaOriginal(),
    },
  };
};

export const getTransformForScreenInsideNavigation = () => {
  var result = {};
  if (global.simulateTabletMode) {
    result = fitAreaNewIntoAreaOriginal();
  }

  console.log('getTransformForScreenInsideNavigation');
  console.dir(result, { depth: null });
  console.log(`global.windowWidthSimulate: ${global.windowWidthSimulate}`);
  console.log(`global.windowHeightSimulate: ${global.windowHeightSimulate}`);

  return {
    ...global.simulateTabletMode && {
      transform: [
        { translateX: result.targetCenterX },
        { translateY: result.targetCenterY },
        { scaleX: 0.42 },
        { scaleY: 0.42 },
        { translateX: __DEV__ ? -global.windowWidthSimulate * 0.59 : -global.windowWidthSimulate * 0.495 },
        { translateY: __DEV__ ? -global.windowHeightSimulate * 0.64 : -global.windowHeightSimulate * 0.42 },


        // { scaleX: 0.42 },
        // { scaleY: 0.42 },
        // { translateX: -420 },
        // { translateY: -600 },
      ],
    },
  };
};

export const getTransformForHeaderInsideNavigation = () => {
  var result = {};
  if (global.simulateTabletMode) {
    result = fitAreaNewIntoAreaOriginal();
  }

  console.log('getTransformForHeaderInsideNavigation');
  console.dir(result, { depth: null });
  console.log(`global.windowWidthSimulate: ${global.windowWidthSimulate}`);
  console.log(`global.windowHeightSimulate: ${global.windowHeightSimulate}`);

  return {
    ...global.simulateTabletMode && {
      transform: [
        { translateX: result.targetCenterX },
        { translateY: result.targetCenterY },
        { scaleX: 0.42 },
        { scaleY: 0.42 },
        { translateX: __DEV__ ? -global.windowWidthSimulate * 0.59 : -global.windowWidthSimulate * 0.495 },
        { translateY: __DEV__ ? -global.windowHeightSimulate * 0.295 : -global.windowHeightSimulate * 0.2475 },

        // { scaleX: 0.42 },
        // { scaleY: 0.42 },
        // { translateX: -420 },
        // { translateY: -40 },
      ],
    },
  };
};

export const getTransformForModalInsideNavigation = (
  scaleX = 0.42,
  scaleY = 0.42,
  translateX = 0,
  translateY = 0,
) => {
  return {
    ...global.simulateTabletMode && {
      transform: [
        { scaleX },
        { scaleY },
        { translateX },
        { translateY },
      ],
    },
  };
};

export const getTransformForModalFullScreen = (
  scaleX = 0.42,
  scaleY = 0.42,
  translateX = -420,
  translateY = -500,
) => {
  return {
    ...global.simulateTabletMode && {
      transform: [
        { scaleX },
        { scaleY },
        { translateX },
        { translateY },
      ],
    },
  };
};

export const recalculateOrderInfoBasedOnCartItems = (userOrder, cartItems, currOutlet) => {
  const selectedOrderToPayListTemp = [userOrder];

  const totalPriceTemp = cartItems.reduce(
    (accum, cartItem) => accum + cartItem.price,
    0,
  );

  var taxTemp = 0;
  if (checkToApplyTaxOrNot(currOutlet, userOrder.orderType, userOrder.orderTypeSub)) {
    taxTemp = totalPriceTemp * currOutlet.taxRate;
  }

  var scTemp = 0;
  if (checkToApplyScOrNot(currOutlet, userOrder.orderType, userOrder.orderTypeSub)) {
    scTemp = totalPriceTemp * currOutlet.scRate;
  }

  const tablePaxTemp = selectedOrderToPayListTemp.reduce(
    (accum, order) => accum + order.tablePax,
    0,
  );
  const totalPrepareTimeTemp = selectedOrderToPayListTemp.reduce(
    (accum, order) => accum + order.totalPrepareTime,
    0,
  );
  const estimatedPreparedDateTemp = moment(
    selectedOrderToPayListTemp[0].orderDate,
  )
    .add(totalPrepareTimeTemp, 'second')
    .valueOf();
  const remarksTemp = selectedOrderToPayListTemp
    .map((order) => order.remarks)
    .join('\n');

  const orderIdTemp = selectedOrderToPayListTemp
    .sort((a, b) => a.orderId.localeCompare(b.orderId))
    .map((order) => order.orderId)
    .join(', ');

  const totalPromotionIdListTemp = selectedOrderToPayListTemp.reduce(
    (accum, order) => accum.concat(order.promotionIdList || []),
    [],
  );

  const totalPromoCodePromotionIdListTemp = selectedOrderToPayListTemp.reduce(
    (accum, order) => accum.concat(order.promoCodePromotionIdList || []),
    [],
  );

  const finalPrice =
    Math.round((totalPriceTemp + taxTemp + scTemp) * 20) / 20;
  const finalPriceBefore = totalPriceTemp + taxTemp + scTemp;

  // const selectedOrderToPayUser =
  //   selectedOrderToPayUserIdDict[selectedOrderToPayUserId];

  var combinedPrintedTokenList = selectedOrderToPayListTemp.reduce((accum, order) => accum.concat(order.printedTokenList ? order.printedTokenList : []), []);
  combinedPrintedTokenList = [...new Set(combinedPrintedTokenList)];

  var combinedPrintedTokenListSupport = selectedOrderToPayListTemp.reduce((accum, order) => accum.concat(order.printedTokenListSupport ? order.printedTokenListSupport : []), []);
  combinedPrintedTokenListSupport = [...new Set(combinedPrintedTokenListSupport)];

  var currPendingOrderTemp = {
    uniqueId: uuidv4(),
    // userId: toPaidUser ? (toPaidUser.firebaseUid || toPaidUser.email) : selectedOrderToPayUser.userId,
    // userEmail: toPaidUser ? (toPaidUser.email) : selectedOrderToPayUser.userId,
    // crmUserId: toPaidUser ? (toPaidUser.uniqueId) : '',
    // userPhone: toPaidUser ? (toPaidUser.number) : selectedOrderToPayUser.userPhone,
    userId: userOrder.userId,
    userEmail: userOrder.userEmail,
    crmUserId: userOrder.crmUserId ? userOrder.crmUserId : '',
    userPhone: userOrder.userPhone,

    userIdAnonymous: selectedOrderToPayListTemp[0].userIdAnonymous ? selectedOrderToPayListTemp[0].userIdAnonymous : '',

    // promotionIdList: totalPromotionIdListTemp,
    promoCodePromotionIdList: totalPromoCodePromotionIdListTemp,
    cartPromotionIdList: selectedOrderToPayListTemp.map(order => order.promotionIdList || []).reduce((accum, idList) => accum.concat([...idList]), []),

    cartItems: [...cartItems.map(cartItem => ({
      ...cartItem,
      // priceOriginal: cartItem.price,
      priceOriginal: cartItem.priceOriginal || cartItem.price,

      priceFinal: cartItem.price,
      priceOriginalFinal: cartItem.priceOriginal || cartItem.price,

      ...cartItem.upsellingCampaignId && {
        // priceUpselling: cartItem.priceUpselling,
        // upsellingCampaignId: cartItem.upsellingCampaignId,

        priceOriginal: cartItem.priceOriginal,

        // priceOriginal: +tempCartItemPriceOriginal.toFixed(2),

        // ...(tempCartItemPrice < tempCartItemPriceOriginal) && {
        //   discount: +(tempCartItemPriceOriginal - tempCartItemPrice).toFixed(2),
        // }
      },
    }))],
    orderType: userOrder.orderType,
    orderTypeSub: userOrder.orderTypeSub,
    paymentMethod: 'Offline',
    userVoucherId: null,
    userVoucherCode: null,
    userAddressId: null,
    orderDate: selectedOrderToPayListTemp[0].orderDate,
    totalPrice: +totalPriceTemp.toFixed(2),

    discountVoucher: 0,
    discount: 0, // for all discounts
    discountPromotionsTotal: 0,
    tax: +taxTemp.toFixed(2),
    sc: +scTemp.toFixed(2),
    deliveryFee: 0,

    tableId: userOrder.tableId,
    tablePax: userOrder.tablePax,
    tableCode: userOrder.tableCode,

    finalPrice: +finalPrice.toFixed(2),
    finalPriceBefore: +finalPriceBefore.toFixed(2),
    outletId: userOrder.outletId,

    merchantId: userOrder.merchantId ? userOrder.merchantId : '',
    outletCover: userOrder.outletCover ? userOrder.outletCover : '',
    merchantLogo: userOrder.merchantLogo ? userOrder.merchantLogo : '',
    outletName: userOrder.outletName ? userOrder.outletName : '',
    merchantName: userOrder.merchantName ? userOrder.merchantName : '',

    outletAddress: currOutlet ? currOutlet.address : '',
    outletPhone: currOutlet ? currOutlet.phone : '',
    outletTaxId: '',
    outletTaxNumber: '',
    outletTaxName: '',
    // outletTaxRate: currOutlet
    //   ? outletsTaxDict[currOutlet.uniqueId].rate
    //   : 0.06,
    outletTaxRate: checkToApplyTaxOrNot(currOutlet, userOrder.orderType, userOrder.orderTypeSub) ? currOutlet.taxRate : 0,

    outletScRate:
      (checkToApplyScOrNot(currOutlet, userOrder.orderType, userOrder.orderTypeSub))
        ? currOutlet.scRate
        : 0,

    // orderStatus: userOrdersTableDict[selectedOutletTable.uniqueId], // this will store entire order as orderStatus
    // orderStatus: USER_ORDER_STATUS.ORDER_DELIVERED,
    orderStatus: userOrder.orderStatus,

    courierCode: '',
    courierId: '',
    courierStatus: '',

    courierLink: '',
    driverId: '',
    driverDetails: null,

    scheduleAt: null,

    //////////////////////////////////////

    // append delivery info into user order itself, enable merchant later call courier again

    // cr stands for 'courier raw'

    crDeliveryCurrency: '',

    crOutletLat: null,
    crOutletLng: null,
    crOutletAddress: '',

    crOutletPhone: '',

    crUserLat: null,
    crUserLng: null,
    crUserAddress: '',

    crUserName: '',
    crUserPhone: '',
    crUserRemarks: '',

    crScheduleAt: '',

    crTotalWeightKg: null,
    crOutletRequiredStartDatetime: '',
    crOutletRequiredFinishDatetime: '',
    crUserRequiredStartDatetime: '',
    crUserRequiredFinishDatetime: '',

    //////////////////////////////////////

    isPrioritizedOrder: false,
    waiterName: userOrder.waiterName ? userOrder.waiterName : '',
    waiterId: userOrder.waiterId ? userOrder.waiterId : '',

    totalPrepareTime: totalPrepareTimeTemp,
    estimatedPreparedDate: estimatedPreparedDateTemp,

    remarks: remarksTemp,

    paymentDetails: null,

    ///////////////////////////////

    collectionDate: selectedOrderToPayListTemp[0].collectionDate,
    completedDate: null,

    paymentDate: null,

    priority: userOrder.priority, // should use one of the joined order?

    userAddress: userOrder.userAddress
      ? userOrder.userAddress
      : '',
    // userName: selectedOrderToPayUser.userName
    //   ? selectedOrderToPayUser.userName
    //   : '',
    userName: userOrder.userName,
    // userPhone: selectedOrderToPayUser.userPhone
    //   ? selectedOrderToPayUser.userPhone
    //   : '',

    // orderId: nanoid(),
    receiptId: nanoid(),

    orderId: orderIdTemp,
    // receiptId: (outletOrderNumber.number + '').padStart(4, '0'),

    ///////////////////////////////

    preorderPackageId: null,
    preorderCollectionDate: null,
    preorderCollectionTime: null,

    ///////////////////////////////

    pointsToRedeem: 0,
    pointsToRedeemPackageIdList: [],
    pointsToRedeemAmountList: [],
    pointsToRedeemDiscount: 0,
    usePointsToRedeem: false,

    // splitAmountList: [], // for split bill

    tableQRUrl: selectedOrderToPayListTemp[0].tableQRUrl
      ? selectedOrderToPayListTemp[0].tableQRUrl
      : '',
    orderRegisterQRUrl: selectedOrderToPayListTemp[0].orderRegisterQRUrl
      ? selectedOrderToPayListTemp[0].orderRegisterQRUrl
      : '',

    ////////////////////

    deliveryPackagingFee: selectedOrderToPayListTemp.reduce((accum, order) => accum + order.deliveryPackagingFee || 0, 0),
    pickupPackagingFee: selectedOrderToPayListTemp.reduce((accum, order) => accum + order.pickupPackagingFee || 0, 0),

    // isReservationOrder: selectedOrderToPayListTemp[0].isReservationOrder ? selectedOrderToPayListTemp[0].isReservationOrder : false,

    // printedTokenList: selectedOrderToPayListTemp.reduce((accum, order) => accum.concat(order.printedTokenList ? order.printedTokenList : []), []),
    printedTokenList: combinedPrintedTokenList,

    printedTokenListSupport: combinedPrintedTokenListSupport,

    isOnlineOrdering: selectedOrderToPayListTemp[0].isOnlineOrdering ? selectedOrderToPayListTemp[0].isOnlineOrdering : false,

    appType: selectedOrderToPayListTemp[0].appType ? selectedOrderToPayListTemp[0].appType : APP_TYPE.MERCHANT,

    settlementDate: null,

    ////////////////////

    createdAt: Date.now(),
    updatedAt: Date.now(),
    deletedAt: null,
  };

  return currPendingOrderTemp;
};

export const getCartItemPriceWithoutAddOn = (cartItem) => {
  return cartItem.price
    +
    (
      (cartItem.discount ? cartItem.discount : 0)
    )
    -
    (
      cartItem.addOns.reduce(
        (accum, addOn) => accum + addOn.prices.reduce((accumAddOn, priceAddOn) => accumAddOn + priceAddOn, 0),
        0,
      ) * cartItem.quantity
    );
};

export const getAddOnChoiceQuantity = (addOnChoice, cartItem) => {
  return addOnChoice.quantities[0] * cartItem.quantity;
};

export const getAddOnChoicePrice = (addOnChoice, cartItem) => {
  return (addOnChoice.prices.reduce((accumAddOn, priceAddOn) => accumAddOn + priceAddOn, 0) * cartItem.quantity);
};

export const checkIsDeviceUnderRouter = (deviceIp, routerIp) => {
  const deviceOctets = deviceIp.split('.');
  const routerOctets = routerIp.split('.');

  // Compare the first three octets of the device and router IP
  for (let i = 0; i < 3; i++) {
    if (deviceOctets[i] !== routerOctets[i]) {
      return false;
    }
  }

  return true;
}

export const showAlertForFailedPrinting = (
  printer,
  routerIp,
  tasks,
) => {
  const isSameNetwork = checkIsDeviceUnderRouter(printer.ip, routerIp);

  if (isSameNetwork || global.currOutlet.reprintFailedDebug) {
    var messageToInsert = '';
    var messagePrinter = '';

    for (var i = 0; i < tasks.length; i++) {
      var task = tasks[i];

      messageToInsert += `${PRINTER_TASK_TYPE_PARSED[task.taskType]}`;

      if (task.taskInfo && task.taskInfo.orderId) {
        messageToInsert += ` (${task.taskInfo.orderId})`;
      }

      if (i === tasks.length - 1) {

      }
      else {
        messageToInsert += ', ';
      }
    }

    if (printer) {
      messagePrinter = ` for (${printer.name}/${printer.area}/${printer.ip})`;
    }

    var message = `The printing action${tasks.length > 1 ? 's' : ''} of ${messageToInsert} ${tasks.length > 1 ? 'are' : 'is'} failed${messagePrinter}, you may follow the guide below to troubleshoot
  \n- Turn off/on the printer and router
  \n- Reset the printer and redo the configuration
  \n- Replacing LAN cable of printer and/or router
  \n- Replacing printer/router
  \n\nIf the issue still persist, please seek for Account Manager for assistance (or click on the 'Support' tab for more info)
  \n\nNote: Can go to Settings > General > Disable Printing Alerts, to disable this message in the future.
  `;

    logToFile(`The printing action${tasks.length > 1 ? 's' : ''} of ${messageToInsert} ${tasks.length > 1 ? 'are' : 'is'} failed${messagePrinter}`);

    Alert.alert('Info', message);

    // Alert.alert('Info', message, [
    //   {
    //     text: 'OK',
    //     onPress: () => {
    //       logToFile('no reprint clicked (failed connection issue)');
    //     },
    //   },
    //   {
    //     text: 'REPRINT',
    //     onPress: async () => {
    //       logToFile('reprint clicked (failed connection issue)');

    //       // reprintAction(userOrder);
    //     },
    //   },
    // ]);
  }
};

export const reprintAction = async (userOrder) => {
  var printTimes = 1;

  if (global.outletCategoriesDict) {
    if (userOrder.cartItems && userOrder.cartItems.length > 0) {
      for (var i = 0; i < userOrder.cartItems.length; i++) {
        if (global.outletCategoriesDict[userOrder.cartItems[i].categoryId] &&
          global.outletCategoriesDict[userOrder.cartItems[i].categoryId].printKDNum) {
          printTimes = global.outletCategoriesDict[userOrder.cartItems[i].categoryId].printKDNum;
        }
      }
    }

    if (userOrder.cartItemsCancelled && userOrder.cartItemsCancelled.length > 0) {
      for (var i = 0; i < userOrder.cartItemsCancelled.length; i++) {
        if (global.outletCategoriesDict[userOrder.cartItemsCancelled[i].categoryId] &&
          global.outletCategoriesDict[userOrder.cartItemsCancelled[i].categoryId].printKDNum) {
          printTimes = global.outletCategoriesDict[userOrder.cartItems[i].categoryId].printKDNum;
        }
      }
    }
  }

  for (var i = 0; i < printTimes; i++) {
    logToFile('common - printUserOrder - KITCHEN_DOCKET reprint');

    await printUserOrder(
      {
        orderData: userOrder,
      },
      false,
      [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
      false,
      false,
      false,
      { isInternetReachable: true, isConnected: true },
      false,
      [KD_ITEM_STATUS.DELIVERED, KD_ITEM_STATUS.CANCELLED],
    );

    printKDSummaryCategoryWrapper(
      {
        orderData: userOrder,
      },
      [KD_ITEM_STATUS.DELIVERED, KD_ITEM_STATUS.CANCELLED],
    );

    console.log('a======');

    if (userOrder && userOrder.cartItems && userOrder.cartItems.length > 0) {
      console.log('b======');

      const printerIpCountDict = await calcPrintTotalForKdIndividual({
        userOrder: userOrder,
      });
      const printerTaskId = uuidv4();
      global.printingTaskIdDict[printerTaskId] = {};

      for (let bdIndex = 0; bdIndex < userOrder.cartItems.length; bdIndex++) {
        console.log('c======');
        if (!userOrder.cartItems[bdIndex].isDocket) {
          console.log('d======');
          await printDocketForKD(
            {
              userOrder,
              cartItem: userOrder.cartItems[bdIndex],
              printerIpCountDict: printerIpCountDict,
              printerTaskId: printerTaskId,
            },
            // true,
            [PRINTER_USAGE_TYPE.KITCHEN_DOCKET],
            // selectedPaymentMethodType === OFFLINE_PAYMENT_METHOD_TYPE.CASH,
            [KD_ITEM_STATUS.CANCELLED, KD_ITEM_STATUS.DELIVERED],
            // deliveredUser,
          );
        }
      }

      for (let index = 0; index < userOrder.cartItems.length; index++) {
        console.log('e======');
        if (userOrder.cartItems[index].isDocket) {
          console.log('f======');
          await printDocket(
            {
              userOrder,
              cartItem: userOrder.cartItems[index],
            },
            // true,
            [PRINTER_USAGE_TYPE.RECEIPT],
            [KD_ITEM_STATUS.CANCELLED, KD_ITEM_STATUS.DELIVERED],
            // deliveredUser,
          );
        }
      }
    }
  }
};

export const showAlertForInterruptedPrinting = async (
  userOrderMetadata,
) => {
  // var messageToInsert = '';
  // var messagePrinter = '';

  const userOrderSnapshot = await firestore()
    .collection(Collections.UserOrder)
    .where('uniqueId', '==', userOrderMetadata.uniqueId)
    .limit(1)
    .get();

  let userOrder = null;
  if (!userOrderSnapshot.empty) {
    userOrder = userOrderSnapshot.docs[0].data();
  }

  if (userOrder) {
    var message = `The order #${(userOrder.orderType === ORDER_TYPE.DINEIN ? '' : 'T') + userOrder.orderId} has experienced some interruptions on the printing, press REPRINT to print again.`;

    logToFile(message);

    if (global.currOutlet && global.currOutlet.reprintAuto) {
      // means auto reprint

      logToFile('auto reprint');

      reprintAction(userOrder);
    }
    else {
      Alert.alert('Info', message, [
        {
          text: 'NO',
          onPress: () => {
            logToFile('no reprint clicked');
          },
        },
        {
          text: 'REPRINT',
          onPress: async () => {
            logToFile('reprint clicked');

            reprintAction(userOrder);
          },
        },
      ]);
    }
  }
};

export const logToFile = (logs, stripEscPos = false) => {
  if (global.currOutlet && global.currOutlet.logFile) {
    if (!global.supportCodeData || global.scl) {
      if (stripEscPos) {
        FileLogger.info(removeESCPOSCommands(logs));
      }
      else {
        FileLogger.info(logs);
      }
    }
  }
};

export const uploadLogFile = async (outlet) => {
  console.log('uploadLogFile');

  const savedLogPaths = await FileLogger.getLogFilePaths();

  console.log(savedLogPaths);

  let arrayBuffer = null;

  if (savedLogPaths && savedLogPaths.length > 0) {
    const logPath = savedLogPaths[0];

    const statResult = await RNFS.stat(logPath);

    console.log('statResult');
    console.log(statResult);

    if (statResult && statResult.size > 0) {
      // means got content, proceed to upload

      const referencePath = `/merchant-logs/${outlet.subdomain}/logs/${moment().format('YYYY-MM-DD_HH-mm-ss_A')}${global.duid ? `-${global.duid}` : ''}.log`;

      const s3bucket = new AWS.S3({
        accessKeyId: awsId,
        secretAccessKey: awsSecret,
        Bucket: awsBucket,
        signatureVersion: 'v4',
      });

      const contentType = `text/plain`;
      const contentDeposition = `inline;filename="${referencePath.slice(1)}"`;
      const fPath = logPath;
      const base64 = await RNFS.readFile(fPath, 'base64');
      const arrayBuffer = decode(base64);

      await new Promise(async (resolve, reject) => {
        await s3bucket.createBucket(async () => {
          const params = {
            Bucket: awsBucket,
            Key: referencePath.slice(1),
            Body: arrayBuffer,
            ContentDisposition: contentDeposition,
            ContentType: contentType,
          };
          await s3bucket.upload(params, async (error, data) => {
            if (error) {
              // console.log('image upload error');
              // console.log(error);
              // reject(getApiError(error));
              resolve('');
            } else {
              // console.log('image upload success');
              // console.log(data);
              // resolve(data);

              console.log('done upload!');

              await FileLogger.deleteLogFiles();

              resolve(data.Location);
            }
          });
        });
      });
    }

    ////////////////////////////////////

    // try {
    //   // write to local files

    //   if (global.currOutlet && global.currOutlet.logFileL && arrayBuffer) {
    //     // log file locally

    //     var logFileName = `${global.currOutlet.subdomain}-${moment().format('YYYY-MM-DD_HH-mm-ss_A')}`;

    //     var logFilePath = `${Platform.OS === 'ios'
    //       ? RNFS.DocumentDirectoryPath
    //       : RNFS.DownloadDirectoryPath
    //       }/KooDooData/logs/${logFileName}.log`;

    //     RNFS.writeFile(logFilePath, arrayBuffer, 'ascii')
    //       .then(async (success) => {
    //         // uploadEmailReport(
    //         //   null,
    //         //   arrayBuffer,
    //         //   logFileName,
    //         //   reportUrl,
    //         //   emailAddress,
    //         //   subject,
    //         //   text,
    //         //   callback,
    //         // );
    //       })
    //       .catch((err) => {
    //         // console.log(err.message);
    //       });
    //   }
    // }
    // catch (ex) {
    //   console.error(ex);
    // }

    ////////////////////////////////////
  }
};

export const removeESCPOSCommands = (inputString) => {
  for (const cmd in ESCPOS_CMD) {
    if (ESCPOS_CMD.hasOwnProperty(cmd)) {
      const regex = new RegExp(ESCPOS_CMD[cmd], 'g');
      inputString = inputString.replace(regex, '');
    }
  }
  return inputString;
}

export const compareTwoValues = (left, right) => {
  const isEqual = JSON.prune(left) === JSON.prune(right);
  return isEqual;
};

// 2024-04-12 - checking for required purchase item quantity
export const checkRPQtyStatus = (
  campaignData = {},
  cartItems = [],
  selectedOutletItemCategoriesDict = {},
  rpCategoryIdUsedPromoDict = undefined,
) => {
  let result = false;

  let currQty = 0;
  let matchedCartItems = [];

  if (
    campaignData.variationRp === undefined ||
    campaignData.variationItemsRp === undefined ||
    campaignData.variationItemsSkuRp === undefined ||
    campaignData.qtyRp === undefined ||
    campaignData.qtyRp <= 0 ||
    typeof campaignData.qtyRp !== 'number'
  ) {
    result = true;
  }
  else {
    for (let i = 0; i < cartItems.length; i++) {
      const tempCartItem = cartItems[i];

      if (campaignData.variationRp === PROMOTION_TYPE_VARIATION.PRODUCT_OF_CATEGORY) {
        const tempCategory = selectedOutletItemCategoriesDict[tempCartItem.categoryId];

        if (campaignData.variationItemsSkuRp.includes(tempCategory.name)) {
          currQty += tempCartItem.quantity;

          matchedCartItems.push(tempCartItem);
        }
      }
      else if (campaignData.variationRp === PROMOTION_TYPE_VARIATION.SPECIFIC_PRODUCTS) {
        if (campaignData.variationItemsSkuRp.includes(tempCartItem.itemSku)) {
          currQty += tempCartItem.quantity;

          matchedCartItems.push(tempCartItem);
        }
      }

      ///////////////////////////////////////////
    }

    if (currQty >= campaignData.qtyRp) {
      result = true;
    }

    //////////////////////////

    // 2024-05-07 - required purchase changes

    let rpCategoryIdUsedPromoDictLocal = {
      ...global.rpCategoryIdUsedPromoDict,
    };
    if (rpCategoryIdUsedPromoDict) {
      rpCategoryIdUsedPromoDictLocal = rpCategoryIdUsedPromoDict;
    }

    let isStillConsumable = false;
    for (let rpIndex = 0; rpIndex < matchedCartItems.length; rpIndex++) {
      const rpKey = `${matchedCartItems[rpIndex].itemId}-${matchedCartItems[rpIndex].cartItemDate.toFixed(0)}`;

      if (rpCategoryIdUsedPromoDictLocal[rpKey] === true) {
        // existed already, can skip
        continue;
      }
      else {
        // global.rpCategoryIdUsedPromoDict[rpKey] = true;

        // if (campaignData.promotionId === matchedCartItems[rpIndex].promotionId) {
        //   rpCategoryIdUsedPromoDictLocal[rpKey] = true;
        //   global.rpCategoryIdUsedPromoDict[rpKey] = true;
        //   continue;
        // }

        isStillConsumable = true;
        break;
      }
    }

    if (!isStillConsumable) {
      result = false;
    }

    //////////////////////////
  }

  console.log(`checkRPQtyStatus result`);
  console.log(result);
  console.log(`currQty: ${currQty}`);
  console.log(`campaignData.qtyRp: ${campaignData.qtyRp}`);

  return {
    rpQtyStatus: result,
    rpQty: campaignData.qtyRp,
    currQty,
    matchedCartItems,
  };
};

// 2024-11-22 - pre-calculate consumed items for required purchases promotions
// export const precalculateConsumedRPQty = (
//   cartItems,
//   selectedOutletItemCategoriesDict,
//   // availablePromotions,
//   // availablePromoCodePromotions,
//   amountOffItemSkuDict,
//   amountOffCategoryNameDict,
//   percentageOffItemSkuDict,
//   percentageOffCategoryNameDict,
// ) => {
//   for (let i = 0; i < cartItems.length; i++) {
//     const tempCartItemCheck = cartItems[i];

//     if (tempCartItemCheck.promotionId &&
//       tempCartItemCheck.rpProcessed === 1 // mean pre-processed promotional items
//     ) {
//       // can proceed to pre-calculate

//       const tempCartItemCategoryCheck = selectedOutletItemCategoriesDict[tempCartItemCheck.categoryId];

//       let campaignData = null;

//       if (amountOffItemSkuDict[tempCartItemCheck.itemSku]) {
//         campaignData = amountOffItemSkuDict[tempCartItem.itemSku];
//       }
//       else if (tempCartItemCategoryCheck && amountOffCategoryNameDict[tempCartItemCategoryCheck.name]) {
//         campaignData = amountOffCategoryNameDict[tempCartItemCategoryCheck.name];
//       }

//       if (percentageOffItemSkuDict[tempCartItemCheck.itemSku]) {
//         campaignData = percentageOffItemSkuDict[tempCartItem.itemSku];
//       }
//       else if (tempCartItemCategoryCheck && percentageOffCategoryNameDict[tempCartItemCategoryCheck.name]) {
//         campaignData = percentageOffCategoryNameDict[tempCartItemCategoryCheck.name];
//       }

//       ///////////////////////////////////////////////////

//       let result = false;

//       let currQty = 0;
//       let matchedCartItems = [];

//       if (
//         campaignData &&
//         campaignData.variationRp === undefined ||
//         campaignData.variationItemsRp === undefined ||
//         campaignData.variationItemsSkuRp === undefined ||
//         campaignData.qtyRp === undefined ||
//         campaignData.qtyRp <= 0 ||
//         typeof campaignData.qtyRp !== 'number'
//       ) {
//         result = true;
//       }
//       else {
//         for (let i = 0; i < cartItems.length; i++) {
//           const tempCartItem = cartItems[i];

//           if (campaignData.variationRp === PROMOTION_TYPE_VARIATION.PRODUCT_OF_CATEGORY) {
//             const tempCategory = selectedOutletItemCategoriesDict[tempCartItem.categoryId];

//             if (campaignData.variationItemsSkuRp.includes(tempCategory.name)) {
//               currQty += tempCartItem.quantity;

//               matchedCartItems.push(tempCartItem);
//             }
//           }
//           else if (campaignData.variationRp === PROMOTION_TYPE_VARIATION.SPECIFIC_PRODUCTS) {
//             if (campaignData.variationItemsSkuRp.includes(tempCartItem.itemSku)) {
//               currQty += tempCartItem.quantity;

//               matchedCartItems.push(tempCartItem);
//             }
//           }

//           ///////////////////////////////////////////
//         }

//         if (currQty >= campaignData.qtyRp) {
//           result = true;
//         }

//         //////////////////////////

//         // 2024-05-07 - required purchase changes

//         let rpCategoryIdUsedPromoDictLocal = {
//           ...global.rpCategoryIdUsedPromoDict,
//         };
//         if (rpCategoryIdUsedPromoDict) {
//           rpCategoryIdUsedPromoDictLocal = rpCategoryIdUsedPromoDict;
//         }

//         let isStillConsumable = false;
//         for (let rpIndex = 0; rpIndex < matchedCartItems.length; rpIndex++) {
//           const rpKey = `${matchedCartItems[rpIndex].itemId}-${matchedCartItems[rpIndex].cartItemDate.toFixed(0)}`;

//           if (rpCategoryIdUsedPromoDictLocal[rpKey] === true) {
//             // existed already, can skip
//             continue;
//           }
//           else {
//             // global.rpCategoryIdUsedPromoDict[rpKey] = true;

//             // if (campaignData.promotionId === matchedCartItems[rpIndex].promotionId) {
//             //   rpCategoryIdUsedPromoDictLocal[rpKey] = true;
//             //   global.rpCategoryIdUsedPromoDict[rpKey] = true;
//             //   continue;
//             // }

//             isStillConsumable = true;
//             break;
//           }
//         }

//         if (!isStillConsumable) {
//           result = false;
//         }

//         //////////////////////////
//       }
//     }
//   }

//   return cartItems;
// };

// 2024-11-22 - check if the order is required purchase promotions
export const isRpAppliedOrder = (
  cartItems,
  selectedOutletItemCategoriesDict,
  // availablePromotions,
  // availablePromoCodePromotions,
  amountOffItemSkuDict,
  amountOffCategoryNameDict,
  percentageOffItemSkuDict,
  percentageOffCategoryNameDict,
) => {
  let result = false;

  for (let i = 0; i < cartItems.length; i++) {
    const tempCartItemCheck = cartItems[i];

    if (tempCartItemCheck.promotionId
      // &&
      // tempCartItemCheck.rpProcessed === 1 // mean pre-processed promotional items
    ) {
      // can proceed to pre-calculate

      const tempCartItemCategoryCheck = selectedOutletItemCategoriesDict[tempCartItemCheck.categoryId];

      let campaignData = null;

      if (amountOffItemSkuDict[tempCartItemCheck.itemSku]) {
        campaignData = amountOffItemSkuDict[tempCartItemCheck.itemSku];
      }
      else if (tempCartItemCategoryCheck && amountOffCategoryNameDict[tempCartItemCategoryCheck.name]) {
        campaignData = amountOffCategoryNameDict[tempCartItemCategoryCheck.name];
      }

      if (percentageOffItemSkuDict[tempCartItemCheck.itemSku]) {
        campaignData = percentageOffItemSkuDict[tempCartItemCheck.itemSku];
      }
      else if (tempCartItemCategoryCheck && percentageOffCategoryNameDict[tempCartItemCategoryCheck.name]) {
        campaignData = percentageOffCategoryNameDict[tempCartItemCategoryCheck.name];
      }

      ///////////////////////////////////////////////////

      if (campaignData) {
        if (
          campaignData.variationRp === undefined ||
          campaignData.variationItemsRp === undefined ||
          campaignData.variationItemsSkuRp === undefined ||
          campaignData.qtyRp === undefined ||
          campaignData.qtyRp <= 0 ||
          typeof campaignData.qtyRp !== 'number'
        ) {
        }
        else {
          if (campaignData.applyDiscountPer === APPLY_DISCOUNT_PER.ITEM) {
            result = true;
            break;
          }

          //////////////////////////
        }
      }
    }
  }

  return result;
};

export const parseImagePickerResponse = (response) => {
  if (response.uri !== undefined) {
    return {
      uri: response.uri,
      width: response.width,
      height: response.height,
    };
  }
  else {
    // newer api

    if (response.assets && response.assets.length > 0) {
      return {
        uri: response.assets[0].uri ? response.assets[0].uri : '',
        width: response.assets[0].width ? response.assets[0].width : 0,
        height: response.assets[0].height ? response.assets[0].height : 0,
      };
    }
    else {
      return {
        uri: '',
        width: 0,
        height: 0,
      }
    }
  }
};

export const fetchNextBatchCRMV2 = async (currOutletId, lastDocArr, isPrev = false, defaultLastDoc, crmUsers = []) => {

  try {
    if (!isPrev) {
      let currLastDoc = null;
      if (lastDocArr.length > 0) {
        currLastDoc = lastDocArr[lastDocArr.length - 1];
      }

      let queryCheck = firestore()
        .collection(Collections.CRMUser)
        .where('outletId', '==', currOutletId)
        .where('deletedAt', '==', null)
        .orderBy('updatedAt', 'desc')
        .limit(1);

      if (lastDocArr.length <= 0) {
        queryCheck = queryCheck.startAfter(defaultLastDoc);
      }
      else {
        queryCheck = queryCheck.startAfter(currLastDoc);
      }

      const querySnapshotCheck = await queryCheck.get();

      if (querySnapshotCheck && !querySnapshotCheck.empty) {
        let query = firestore()
          .collection(Collections.CRMUser)
          .where('outletId', '==', currOutletId)
          .where('deletedAt', '==', null)
          .orderBy('updatedAt', 'desc')
          .limit(500);

        if (lastDocArr.length <= 0) {
          query = query.startAfter(defaultLastDoc);
        }
        else {
          query = query.startAfter(currLastDoc);
        }

        const querySnapshot = await query.get();

        if (querySnapshot && !querySnapshot.empty) {
          const crmNextBatch = querySnapshot.docs.map((doc) => doc.data());
          const newLastDoc = querySnapshot.docs[querySnapshot.docs.length - 1];

          OutletStore.update((s) => {
            s.crmNextBatch = crmNextBatch;
            s.crmLastDoc = newLastDoc;

            s.lastDocArr = [
              ...lastDocArr,
              newLastDoc,
            ];
          });
          CommonStore.update((s) => {
            s.isLoading = false;
          })
          console.log('Fetched next batch of CRM users:', crmNextBatch);
        }
        else {

          console.log('No CRM users found for the next batch.');

          CommonStore.update((s) => {
            s.isLoading = false;
          })
          Alert.alert('Info', 'No more CRM users found.');
        }
      } else {
        console.log('No CRM users found for the next batch.');

        CommonStore.update((s) => {
          s.isLoading = false;
        })
        Alert.alert('Info', 'No more CRM users found.');
      }
    }
    else {
      // back

      let currLastDoc = null;
      if (lastDocArr.length > 2) {
        // when on 3rd page onwards

        // if (lastDocArr.length <= 2) {
        //   // for 3rd page

        //   currLastDoc = defaultLastDoc;
        // }
        // else {
        // for 4th page onwards
        currLastDoc = lastDocArr[lastDocArr.length - 3];
        console.log('curr last doc', currLastDoc);
        // }

      }
      else {
        // when on 2nd page

        currLastDoc = defaultLastDoc;
      }

      if (lastDocArr.length >= 2) {
        // need do get query

        let queryBack = firestore()
          .collection(Collections.CRMUser)
          .where('outletId', '==', currOutletId)
          .where('deletedAt', '==', null)
          .orderBy('updatedAt', 'desc')
          .limit(500);

        queryBack = queryBack.startAfter(currLastDoc);

        const queryBackSnapshot = await queryBack.get();
        console.log('query snapshot', queryBackSnapshot);

        if (queryBackSnapshot && !queryBackSnapshot.empty) {
          const crmNextBatch = queryBackSnapshot.docs.map((doc) => doc.data());
          const newLastDoc = queryBackSnapshot.docs[queryBackSnapshot.docs.length - 1];

          OutletStore.update((s) => {
            s.crmNextBatch = crmNextBatch;
            s.crmLastDoc = newLastDoc;

            s.lastDocArr = lastDocArr.slice(0, lastDocArr.length - 1);
          });
          CommonStore.update((s) => {
            s.isLoading = false;
          })
          console.log('Fetched next batch of CRM users:', crmNextBatch);
          console.log('new last doc', newLastDoc);
        }
        else {

          console.log('No CRM users found for the next batch.');

          CommonStore.update((s) => {
            s.isLoading = false;
          })
          Alert.alert('Info', 'No more CRM users found.');
        }
      }
      else {
        // on 2nd page, can just use default crmUsers

        OutletStore.update((s) => {
          s.crmNextBatch = crmUsers;
          s.crmLastDoc = defaultLastDoc;

          s.lastDocArr = [];
        });

        CommonStore.update((s) => {
          s.isLoading = false;
        })
      }
    }

  } catch (error) {
    console.error('Error fetching next batch of CRM users:', error);
  }
};

export const checkToApplyTaxOrNot = (currOutlet, orderType, orderTypeSub = ORDER_TYPE_SUB.NORMAL, orderData = {}) => {
  let status = false;

  if (orderTypeSub === undefined) {
    orderTypeSub = ORDER_TYPE_SUB.NORMAL;
  }

  if (currOutlet.taxActive &&
    (
      currOutlet.taxOrderTypes === undefined
      ||
      currOutlet.taxOrderTypes === ''
      ||
      (
        currOutlet.taxOrderTypes &&
        currOutlet.taxOrderTypes.length > 0 &&
        currOutlet.taxOrderTypes.includes(orderType))
    )
  ) {
    if (currOutlet.taxOrderTypes &&
      currOutlet.taxOrderTypes.length > 0) {
      if (orderType === ORDER_TYPE.PICKUP) {
        if (orderTypeSub === ORDER_TYPE_SUB.NORMAL) {
          status = true;
        }
        else {
          // other delivery

          status = false;
        }
      }
      else {
        status = true;
      }
    }
    else {
      status = true;
    }
  }

  /////////////////////////////////////////

  if (orderData.isDepo) {
    status = false;
  }

  ////////////////////////////////////////

  return status;
};

export const checkToApplyScOrNot = (currOutlet, orderType, orderTypeSub = ORDER_TYPE_SUB.NORMAL,
  // orderTypeDetails = ORDER_TYPE_DETAILS.POS,
  orderData = {},
) => {
  let status = false;

  if (orderTypeSub === undefined) {
    orderTypeSub = ORDER_TYPE_SUB.NORMAL;
  }

  if (currOutlet.scActive &&
    (
      currOutlet.scOrderTypes === undefined
      ||
      currOutlet.scOrderTypes === ''
      ||
      (
        currOutlet.scOrderTypes &&
        currOutlet.scOrderTypes.length > 0 &&
        currOutlet.scOrderTypes.includes(orderType))
    )
  ) {
    if (currOutlet.scOrderTypes &&
      currOutlet.scOrderTypes.length > 0) {
      if (orderType === ORDER_TYPE.PICKUP) {
        if (orderTypeSub === ORDER_TYPE_SUB.NORMAL) {
          status = true;
        }
        else {
          // other delivery

          status = false;
        }
      }
      else {
        status = true;
      }
    }
    else {
      status = true;
    }
  }

  if (orderData.isDepo) {
    status = false;
  }

  return status;
};

export const recordOutletSupplyItemTransaction = async (params) => {
  const {
    outletSupplyItemId,
    name,
    updatedAt,
    cn,
    cnId,

    cnData,

    am,
    amw,
    // osit,
  } = params;

  // update first, if got exception then only create

  const dt = moment().format('YYYYMMDD');
  const ositId = `${outletSupplyItemId}-${dt}`;

  try {
    // can try search again

    const ositSnapshot = await firestore()
      .collection(Collections.OutletSupplyItemTransaction)
      .where('uniqueId', '==', ositId)
      .limit(1)
      .get();

    var osit = null;
    if (!ositSnapshot.empty) {
      osit = ositSnapshot.docs[0].data();
    }

    if (osit) {
      // update

      updateOutletSupplyItemTransaction({
        ...params,

        osit: osit,
      });
    }
    else {
      // create

      // need retrieve the OutletSupplyItem also, to get the stocks

      createOutletSupplyItemTransaction(params);
    }
  }
  catch (ex) {
    console.error(ex);
  }
};

export const updateOutletSupplyItemTransaction = async (params) => {
  // update

  let {
    outletSupplyItemId,
    name,
    updatedAt,
    cn,
    cnId,
    cnData,

    am,
    amw,
    osit,

    body,

    osi,
  } = params;

  try {
    const dt = moment().format('YYYYMMDD');
    const ositId = `${outletSupplyItemId}-${dt}`;

    ///////////////////////////////

    // if osit not existed, can call api to retrieve

    if (osit && osit.uniqueId) {
      // got data provided
    }
    else {
      // retrieve at here instead

      const ositSnapshot = await firestore()
        .collection(Collections.OutletSupplyItemTransaction)
        .where('uniqueId', '==', ositId)
        .limit(1)
        .get();

      osit = null;
      if (!ositSnapshot.empty) {
        osit = ositSnapshot.docs[0].data();
      }
    }

    ///////////////////////////////

    let remarks = '';
    if (cn === Collections.OutletItem &&
      cnData && cnData.userOrder &&
      cnData.userOrder.uniqueId &&
      cnData.cartItem) {
      remarks = `#${cnData.userOrder.orderType === ORDER_TYPE.DINEIN ? '' : 'T'}${cnData.userOrder.orderId} | x${cnData.cartItem.quantity} ${cnData.cartItem.itemName}`;
    }
    else if (cn === Collections.OutletItemAddOnChoice &&
      cnData && cnData.userOrder &&
      cnData.userOrder.uniqueId &&
      cnData.outletItemAddOnChoice) {
      remarks = `#${cnData.userOrder.orderType === ORDER_TYPE.DINEIN ? '' : 'T'}${cnData.userOrder.orderId} | x${cnData.cartItem.quantity * (cnData.addOnGroupChoiceQty ? cnData.addOnGroupChoiceQty : 1)} ${cnData.outletItemAddOnChoice.name}`;
    }
    else if (cn === Collections.OutletSupplyItem &&
      cnData && cnData.userOrder &&
      cnData.userOrder.uniqueId &&
      cnData.osiParent
    ) {
      remarks = `#${cnData.userOrder.orderType === ORDER_TYPE.DINEIN ? '' : 'T'}${cnData.userOrder.orderId} | x${(cnData.quantity ? cnData.quantity : 1)} of ${cnData.osiParent.name}`;
    }
    else if (cn === Collections.OutletSupplyItem &&
      cnData && cnData.listItem) {
      remarks = `Insert Inventory`;

      if (am < 0) {
        remarks = `Consume Inventory`;
      }
    }
    else if (cn === Collections.OutletSupplyItem &&
      cnData && cnData.woOrder && cnData.woItem) {
      remarks = `${cnData.woItem.userOrderIdHuman} | WO: ${cnData.woOrder.woId}`;
    }
    else if (cn === Collections.PurchaseOrder &&
      cnData && cnData.listItem &&
      cnData.body && cnData.body.poId) {
      remarks = `Purchase Order ${cnData.body.poId}`;
    }
    else if (cn === Collections.StockTake &&
      cnData && cnData.listItem &&
      cnData.body && cnData.body.stId) {
      remarks = `Stock Take ${cnData.body.stId}`;
    }
    else if (cn === Collections.StockTransfer &&
      cnData && cnData.listItem &&
      cnData.body && cnData.body.stId) {
      remarks = `Stock Transfer ${cnData.body.stId}`;
    }

    let baseQty = osit.af;
    if (osi && osi.quantity !== undefined &&
      osi.quantity !== osit.af) {
      baseQty = osi.quantity;
    }

    let amc = BigNumber(osi.price ? osi.price : 0).multipliedBy(BigNumber(am)).toNumber();
    let amwc = BigNumber(osi.price ? osi.price : 0).multipliedBy(BigNumber(amw ? amw : 0)).toNumber();

    let uniqueKey = uuidv4();

    firestore()
      .collection(Collections.OutletSupplyItemTransaction)
      .doc(ositId)
      .update({
        fNum: firestore.FieldValue.increment(1),
        [`fields.${uniqueKey}`]: {
          bf: baseQty,
          am: am,
          amw: amw,
          amc: amc,
          amwc: amwc,
          af: BigNumber(baseQty).plus(am).toNumber(),
          dt: updatedAt,

          cn: cn,
          cnId: cnId,
          rm: remarks,
        },

        bf: baseQty,
        am: am,
        amw: amw,
        amc: amc,
        amwc: amwc,
        af: BigNumber(baseQty).plus(am).toNumber(),

        ...(name) && {
          name: name,
        },

        updatedAt: Date.now(),
      });

    try {
      if (cnData.userOrder && cnData.userOrder.uniqueId) {
        firestore().collection(Collections.UserOrder).doc(cnData.userOrder.uniqueId).update({
          [`ci.${uniqueKey}`]: {
            cnId: cnId,
            cid: cnData.cartItem.cartItemDate, // cart item date
            am: am,
            amw: amw,
            amc: amc,
            amwc: amwc,
          },
        });
      }
    }
    catch (ex) {
      console.error(ex);
    }
  }
  catch (ex) {
    console.error(ex);
  }
};

export const createOutletSupplyItemTransaction = async (params) => {
  // create

  const {
    outletSupplyItemId,
    name,
    updatedAt,
    cn,
    cnId,
    cnData,
    am,
    amw,
    // osit = {},
  } = params;

  const dt = moment().format('YYYYMMDD');
  const ositId = `${outletSupplyItemId}-${dt}`;

  let osi = null;

  // do a local search first
  if (global.outletSupplyItemsDict[outletSupplyItemId]) {
    osi = global.outletSupplyItemsDict[outletSupplyItemId];
  }

  if (!osi) {
    // if still no, can do a query

    const osiSnapshot = await firestore()
      .collection(Collections.OutletSupplyItem)
      .where('uniqueId', '==', outletSupplyItemId)
      .limit(1)
      .get();

    // var osi = null;
    if (!osiSnapshot.empty) {
      osi = osiSnapshot.docs[0].data();
    }
  }

  if (osi) {
    // can proceed

    let baseQty = osi.quantity;
    if (cnData && cnData.existed === false) {
      baseQty = 0; // this is freshly created OutletSupplyItem
    }

    let remarks = '';
    if (cn === Collections.OutletItem &&
      cnData && cnData.userOrder &&
      cnData.userOrder.uniqueId &&
      cnData.cartItem) {
      remarks = `#${cnData.userOrder.orderType === ORDER_TYPE.DINEIN ? '' : 'T'}${cnData.userOrder.orderId} | x${cnData.cartItem.quantity} ${cnData.cartItem.itemName}`;
    }
    else if (cn === Collections.OutletItemAddOnChoice &&
      cnData && cnData.userOrder &&
      cnData.userOrder.uniqueId &&
      cnData.outletItemAddOnChoice) {
      remarks = `#${cnData.userOrder.orderType === ORDER_TYPE.DINEIN ? '' : 'T'}${cnData.userOrder.orderId} | x${cnData.cartItem.quantity * (cnData.addOnGroupChoiceQty ? cnData.addOnGroupChoiceQty : 1)} ${cnData.outletItemAddOnChoice.name}`;
    }
    else if (cn === Collections.OutletSupplyItem &&
      cnData && cnData.userOrder &&
      cnData.userOrder.uniqueId &&
      cnData.osiParent
    ) {
      remarks = `#${cnData.userOrder.orderType === ORDER_TYPE.DINEIN ? '' : 'T'}${cnData.userOrder.orderId} | x${(cnData.quantity ? cnData.quantity : 1)} of ${cnData.osiParent.name}`;
    }
    else if (cn === Collections.OutletSupplyItem &&
      cnData && cnData.listItem) {
      remarks = `Insert Inventory`;

      if (am < 0) {
        remarks = `Consume Inventory`;
      }
    }
    else if (cn === Collections.PurchaseOrder &&
      cnData && cnData.listItem &&
      cnData.body && cnData.body.poId) {
      remarks = `Purchase Order ${cnData.body.poId}`;
    }
    else if (cn === Collections.StockTake &&
      cnData && cnData.listItem &&
      cnData.body && cnData.body.stId) {
      remarks = `Stock Take ${cnData.body.stId}`;
    }
    else if (cn === Collections.StockTransfer &&
      cnData && cnData.listItem &&
      cnData.body && cnData.body.stId) {
      remarks = `Stock Transfer ${cnData.body.stId}`;
    }

    let amc = BigNumber(osi.price ? osi.price : 0).multipliedBy(BigNumber(am)).toNumber();
    let amwc = BigNumber(osi.price ? osi.price : 0).multipliedBy(BigNumber(amw ? amw : 0)).toNumber();

    let uniqueKey = uuidv4();

    let osit = {
      uniqueId: ositId,

      fNum: 1,
      fields: {
        [`${uniqueKey}`]: {
          bf: baseQty,
          am: am,
          amw: amw,
          amc: amc,
          amwc: amwc,
          af: BigNumber(baseQty).plus(am).toNumber(),
          dt: updatedAt,

          cn: cn,
          cnId: cnId,
          rm: remarks,
        },
      },
      // [`fields.${uuidv4()}`]: {
      //   bf: baseQty,
      //   am: am,
      //   af: BigNumber(baseQty).plus(am).toNumber(),
      //   dt: updatedAt,

      //   cn: cn,
      //   cnId: cnId,
      //   rm: remarks,
      // },

      bf: baseQty,
      am: am,
      amw: amw,
      amc: amc,
      amwc: amwc,
      af: BigNumber(baseQty).plus(am).toNumber(),

      dt: dt,
      name: osi.name,
      description: osi.description,
      price: osi.price,
      stockIdealQuantity: osi.stockIdealQuantity,
      stockWarningQuantity: osi.stockWarningQuantity,
      outletSupplyItemId: outletSupplyItemId,

      unit: osi.unit,
      sku: osi.sku,
      supplierId: osi.supplierId,
      merchantId: osi.merchantId,
      outletId: osi.outletId,

      createdAt: Date.now(),
      updatedAt: Date.now(),
      deletedAt: null,
    };

    try {
      firestore().collection(Collections.OutletSupplyItemTransaction).doc(osit.uniqueId).set(osit);

      try {
        if (cnData.userOrder && cnData.userOrder.uniqueId) {
          firestore().collection(Collections.UserOrder).doc(cnData.userOrder.uniqueId).update({
            [`ci.${uniqueKey}`]: {
              cnId: cnId,
              cid: cnData.cartItem.cartItemDate, // cart item date
              am: am,
              amw: amw,
              amc: amc,
              amwc: amwc,
            },
          });
        }
      }
      catch (ex) {
        console.error(ex);
      }
    }
    catch (ex) {
      console.error(ex);

      ///////////////////////////////

      // if create failed with exception, maybe same id existed already, can just update it

      updateOutletSupplyItemTransaction(params);

      ///////////////////////////////
    }
  }
};

export const clearIsPaymentLoading = () => {
  // if (global.currOutlet.payDirect) {
  //   setTimeout(() => {
  //     global.isPaymentLoading = false;
  //   }, global.currOutlet.payDirectWaitAfter);
  // }

  setTimeout(() => {
    global.isPaymentLoading = false;
  }, global.currOutlet.payDirectWaitAfter);
};

export const sendOrderDeliverRejectStatus = async (
  userOrder,

  orderStatus,
) => {
  let title = '';
  let message = '';
  let smsMessage = '';

  if (orderStatus === USER_ORDER_STATUS.ORDER_DELIVERED ||
    orderStatus === USER_ORDER_STATUS.ORDER_PREPARED) {
    title = `Your order #${userOrder.orderType === 'DINEIN' ? '' : 'T'}${userOrder.orderId} is ready`;
    message = `${userOrder.orderType === 'DINEIN' ? `Will be delivered to table ${userOrder.tableCode} soon` : 'Please collect at the counter'}`;

    smsMessage = `Hi ${userOrder.userName ? userOrder.userName : 'Guest'}, your order #${userOrder.orderType === 'DINEIN' ? '' : 'T'}${userOrder.orderId}${global.currOutlet && global.currOutlet.name ? ` from ${global.currOutlet.name}` : ''} is ready, ${userOrder.orderType === 'DINEIN' ? `will be delivered to table ${userOrder.tableCode} soon` : 'Please collect at the counter'}`;
  }

  let body = {
    sendPhoneNumber: userOrder.userPhone ? userOrder.userPhone : '',
    userIdAnonymous: userOrder.userIdAnonymous ? userOrder.userIdAnonymous : '',

    title,
    message,
    smsMessage,

    // outletEmail,

    // passcode,

    sendSMS: global.currOutlet.smsKds,

    outletId: global.currOutlet.uniqueId,
    merchantId: global.currOutlet.merchantId,
  };

  // console.log(body);

  await new Promise(async (resolve, reject) => {
    ApiClient.POST(API.sendWebPushNotificationMerchant, body).then((result) => {
      // callback && callback();

      resolve();
    });
  });
};

global.getHeaderTitleStyle = () => {
  return {
    // width: switchMerchant ? '100%' : Platform.OS === 'ios' ? "100%" : "100%",
    width: Platform.OS === 'ios' ? "100%" : "100%",
  };
}

export const processRefundUndoRefundOrder = async (
  params = {
    orderProcessType: ORDER_PROCESS_TYPE.REFUND,
    processAmount: 0,
    destOrder: {},
  }
) => {
  setTimeout(async () => {
    // loyalty campaign > earn cashback

    const destOrder = params.destOrder;

    const { userPhone } = destOrder;
    const { outletId } = destOrder;
    const { merchantId } = destOrder;
    const { merchantName } = destOrder;
    const { userName } = destOrder;
    const { outletName } = destOrder;
    const { merchantLogo } = destOrder;
    const { outletCover } = destOrder;

    if (userPhone) {
      // proceed if got phone no only

      try {
        // const outletSnapshot = await firestore()
        //   .collection(Collections.Outlet)
        //   .where('uniqueId', '==', outletId)
        //   .limit(1)
        //   .get();

        // let outlet = null;
        // if (!outletSnapshot.empty) {
        //   outlet = outletSnapshot.docs[0].data();
        // }

        // let cashbackPercentage = 0.1;

        // if (outlet && outlet.loyaltyCampaignCashbackPercentage) {
        //   cashbackPercentage = outlet.loyaltyCampaignCashbackPercentage;
        // }

        const loyaltyTierSnapshot = await firestore()
          .collection(Collections.LoyaltyTier)
          .where('outletId', '==', outletId)
          .limit(1)
          .get();

        let loyaltyTier = null;
        if (!loyaltyTierSnapshot.empty) {
          loyaltyTier = loyaltyTierSnapshot.docs[0].data();
        }

        let cashbackPercentage = 0.1;

        if (loyaltyTier && loyaltyTier.baseCashbackRate !== undefined) {
          cashbackPercentage = loyaltyTier.baseCashbackRate;
        }

        //////////////////////////////////////////////

        let loyaltyStampUser
          = null;

        ////////////////////////////////

        // try find by user id

        // let userSnapshot = await firestore()
        //     .collection(Collections.User)
        //     .where('firebaseUid', '==', uid)
        //     .limit(1)
        //     .get();

        let user = null;
        // if (!userSnapshot.empty) {
        //     user = userSnapshot.docs[0].data();
        // }

        // if (user === null) {
        //     // web user
        // }
        // else {
        //     // might be cashier, or real user

        //     if (user.role === 'user') {
        //         loyaltyStampUserActual = user;
        //     }
        // }

        // if (loyaltyStampUserActual === null) {
        //     // we try find by phone

        //     userSnapshot = await firestore()
        //         .collection(Collections.User)
        //         .where('number', '==', userPhone)
        //         .limit(1)
        //         .get();

        //     user = null;
        //     if (!userSnapshot.empty) {
        //         user = userSnapshot.docs[0].data();
        //     }

        //     if (user === null) {
        //         // web user
        //     }
        //     else {
        //         // might be cashier, or real user

        //         if (user.role === 'user') {
        //             loyaltyStampUserActual = user;
        //         }
        //     }
        // }

        ////////////////////////////////

        let loyaltyStampUserActual = null;
        let loyaltyStampUserCRM = null;

        // try find by user id

        let crmUserSnapshot = await firestore()
          .collection(Collections.CRMUser)
          .where('number', '==', userPhone)
          .where('outletId', '==', outletId)
          .limit(1)
          .get();

        let crmUser = null;
        if (!crmUserSnapshot.empty) {
          crmUser = crmUserSnapshot.docs[0].data();
        }

        if (crmUser === null) {
          // don't have the crm user for this outlet

          // maybe try the email method

          if (loyaltyStampUserActual) {
            crmUserSnapshot = await firestore()
              .collection(Collections.CRMUser)
              .where('email', '==', loyaltyStampUserActual.email)
              .where('outletId', '==', outletId)
              .limit(1)
              .get();

            crmUser = null;
            if (!crmUserSnapshot.empty) {
              crmUser = crmUserSnapshot.docs[0].data();
            }

            if (crmUser) {
              // means got the crm user created for this actual user

              // need check the crm user got phone number or not, add the phone number if not

              if (crmUser.number) {

              }
              else {
                firestore().collection(Collections.CRMUser).doc(crmUser.uniqueId).update({
                  number: loyaltyStampUserActual.number || userPhone,

                  updatedAt: Date.now(),
                });
              }
            }
            else {
              // no crm user created for this actual user, create for him/her

              // 2024-08-21 - no create/delete here

              // let userIdHumanUnique = await generateUniqueUserIdHuman();

              // crmUser = {
              //   uniqueId: uuidv4(),
              //   userId: loyaltyStampUserActual.firebaseUid ? loyaltyStampUserActual.firebaseUid : '',
              //   email: loyaltyStampUserActual.email ? loyaltyStampUserActual.email : '',

              //   name: userName || '',

              //   number: loyaltyStampUserActual.number || userPhone,

              //   merchantId,
              //   merchantName,
              //   outletId,

              //   pointsRedeemPackageDisableDict: {
              //     // [pointsRedeemPackageId]: disableStatus,
              //   },

              //   userIdHuman: userIdHumanUnique,

              //   createdAt: Date.now(),
              //   updatedAt: Date.now(),
              //   deletedAt: null,
              // };

              // firestore().collection(Collections.CRMUser).doc(crmUser.uniqueId).set(crmUser);
            }

            loyaltyStampUserCRM = crmUser;
          }
        }
        else {
          loyaltyStampUserCRM = crmUser;
        }

        //////////////////////////////////////////////

        // create loyalty campaign credit transaction, can use either crmUserId and/or userId

        let loyaltyStampUserActualId = '';
        let loyaltyStampUserCRMId = '';
        let loyaltyStampUserPhone = '';
        let loyaltyStampUserEmail = '';

        if (loyaltyStampUserCRM) {
          // means not registered user, but is the crm user for this outlet

          // just proceed

          // console.log('loyalty stamp > 1st');

          loyaltyStampUserCRMId = loyaltyStampUserCRM.uniqueId;
          loyaltyStampUserPhone = loyaltyStampUserCRM.number || userPhone;
          loyaltyStampUserEmail = loyaltyStampUserCRM.email || '';
        }

        if (loyaltyStampUserActual) {
          // means is registered user, but not the crm user for this outlet

          // just proceed

          // console.log('loyalty stamp > 2nd');

          loyaltyStampUserActualId = loyaltyStampUserActual.firebaseUid || '';
          loyaltyStampUserPhone = loyaltyStampUserActual.number || userPhone;
          loyaltyStampUserEmail = loyaltyStampUserActual.email || '';

          /////////////////////////////////////////////////////////////////////

          // 2022-08-15 - Possible bug fixes

          if (!loyaltyStampUserCRM) {
            // if the crm user of this registered user is not created for this outlet, create for it

            // 2024-08-21 - no create/delete here

            // let userIdHumanUnique = await generateUniqueUserIdHuman();

            // let crmUserId = uuidv4();

            // let crmUser = {
            //   uniqueId: crmUserId,
            //   merchantId,
            //   merchantName,
            //   outletId,

            //   avatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(
            //     userName
            //   ).replace(/%20/g, "+")}`,
            //   dob: moment().valueOf(),
            //   email: loyaltyStampUserEmail,
            //   gender: 'Male',
            //   name: userName,
            //   number: loyaltyStampUserPhone,
            //   uniqueName: loyaltyStampUserEmail,

            //   address: '',
            //   lat: 0,
            //   lng: 0,
            //   geoHash: '',

            //   pointsRedeemPackageDisableDict: {},
            //   timeline: {},

            //   emailSecond: '',
            //   numberSecond: '',
            //   addressSecond: '',
            //   latSecond: 0,
            //   lngSecond: 0,
            //   geoHashSecond: '',

            //   commonId: crmUserId,

            //   userIdHuman: userIdHumanUnique,

            //   createdAt: Date.now(),
            //   updatedAt: Date.now(),
            //   deletedAt: null,
            // };

            // firestore().collection(Collections.CRMUser).doc(crmUser.uniqueId).set(crmUser);

            // // loyaltyStampUserCRMId = crmUserId;

            // loyaltyStampUserCRM = crmUser; // 2022-10-06 - Fixes
          }

          // 2022-08-15 - Try to use the crm user's email

          if (loyaltyStampUserCRM && loyaltyStampUserCRM.email) {
            loyaltyStampUserCRMId = loyaltyStampUserCRM.uniqueId || '';
            loyaltyStampUserEmail = loyaltyStampUserCRM.email || '';
          }

          /////////////////////////////////////////////////////////////////////
        }

        if (!loyaltyStampUserActual && !loyaltyStampUserCRM) {
          // means not registered user, and not the crm user for this outlet

          // create a crm user

          // console.log('loyalty stamp > 3rd');

          // 2024-08-21 - no create/delete here

          // let crmUserEmail = await generateUniqueEmailAddress();
          // let userIdHumanUnique = await generateUniqueUserIdHuman();

          // let crmUserId = uuidv4();

          // // console.log(crmUserEmail);

          // let crmUser = {
          //   uniqueId: crmUserId,
          //   merchantId,
          //   merchantName,
          //   outletId,

          //   avatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(
          //     userName
          //   ).replace(/%20/g, "+")}`,
          //   dob: moment().valueOf(),
          //   email: crmUserEmail,
          //   gender: 'Male',
          //   name: userName,
          //   number: userPhone,
          //   uniqueName: crmUserEmail,

          //   address: '',
          //   lat: 0,
          //   lng: 0,
          //   geoHash: '',

          //   pointsRedeemPackageDisableDict: {},
          //   timeline: {},

          //   emailSecond: '',
          //   numberSecond: '',
          //   addressSecond: '',
          //   latSecond: 0,
          //   lngSecond: 0,
          //   geoHashSecond: '',

          //   commonId: crmUserId,

          //   userIdHuman: userIdHumanUnique,

          //   createdAt: Date.now(),
          //   updatedAt: Date.now(),
          //   deletedAt: null,
          // };

          // firestore().collection(Collections.CRMUser).doc(crmUser.uniqueId).set(crmUser);

          // loyaltyStampUserCRM = crmUser; // 2022-10-06 - Added

          // loyaltyStampUserCRMId = crmUserId;
          // loyaltyStampUserPhone = userPhone;
          // loyaltyStampUserEmail = crmUserEmail;
        }

        /////////////////////////////////////////////////////////////

        if (loyaltyStampUserCRM &&
          loyaltyStampUserCRM.levelCashbackRate &&
          loyaltyStampUserCRM.levelId) {

          // console.log('========get tier level========');

          let loyaltyTierLevel = null;
          if (loyaltyTier && loyaltyTier.levels && loyaltyTier.levels.length > 0) {
            for (let i = 0; i < loyaltyTier.levels.length; i++) {
              if (loyaltyTier.levels[i].levelId === loyaltyStampUserCRM.levelId) {
                // cashbackPercentage = loyaltyTier.levels[i].levelCashbackRate;
                loyaltyTierLevel = loyaltyTier.levels[i];

                break;
              }
            }
          }

          if (loyaltyTierLevel) {
            cashbackPercentage = loyaltyTierLevel.levelCashbackRate;
          }
          else {
            cashbackPercentage = loyaltyStampUserCRM.levelCashbackRate;
          }
        }

        if (loyaltyTier && !loyaltyTier.isActive) {
          // console.log('========use base cashback========');

          cashbackPercentage = 0.1;
        }
        else {
          // here can determine to upgrade the user as well (in future can aggregate user data (total spent/vists) to crmuser collection itself, save bandwidth cost)

          // console.log('========go to tier level checking========');
          // // console.log(loyaltyTier);
          // // console.log(loyaltyStampUserCRM);

          if (loyaltyTier &&
            loyaltyTier.levels &&
            loyaltyTier.levels.length > 0 &&
            loyaltyStampUserCRM) {

            const startCountDate = moment().subtract(loyaltyTier.countPastTimeMonths, 'month').valueOf();

            let userOrders = [];

            if (loyaltyStampUserCRM.userId) {
              // user that got app

              let userOrderSnapshot = await firestore()
                .collection(Collections.UserOrder)
                .where('outletId', '==', outletId)
                .where('userId', '==', loyaltyStampUserCRM.userId)
                .where('createdAt', '>=', startCountDate)
                // .orderBy('createdAt', 'desc')
                // .limit(1)
                .get();

              if (!userOrderSnapshot.empty) {
                userOrderSnapshot.forEach(doc => {
                  userOrders.push(doc.data());
                });
              }
            }
            else {
              // user that didn't got app

              let userOrderSnapshot = await firestore()
                .collection(Collections.UserOrder)
                .where('outletId', '==', outletId)
                .where('userPhone', '==', loyaltyStampUserCRM.number)
                .where('createdAt', '>=', startCountDate)
                // .orderBy('createdAt', 'desc')
                // .limit(1)
                .get();

              if (!userOrderSnapshot.empty) {
                userOrderSnapshot.forEach(doc => {
                  userOrders.push(doc.data());
                });
              }
            }

            const totalSpent = userOrders.reduce((accum, order) => (!isNaN(order.finalPrice) ? order.finalPrice : 0) + accum, 0);
            const totalVisit = userOrders.length; // or dine in only? 

            if (loyaltyStampUserCRM.levelId) {
              // existing tier system user

              // console.log('========existing tier system user========');

              // 2024-08-21 - no create/delete here

              // let nextLoyaltyTierLevel = null;

              // for (let i = 0; i < loyaltyTier.levels.length; i++) {
              //   // decide based on total visits and/or total spents

              //   let isValid = false;

              //   if (loyaltyTier.isCountByTotalSpents && !loyaltyTier.isCountByTotalVisits) {
              //     if (totalSpent >= loyaltyTier.levels[i].levelTotalSpents) {
              //       isValid = true;
              //     }
              //   }
              //   else if (!loyaltyTier.isCountByTotalSpents && loyaltyTier.isCountByTotalVisits) {
              //     if (totalVisit >= loyaltyTier.levels[i].levelTotalVisits) {
              //       isValid = true;
              //     }
              //   }
              //   else if (loyaltyTier.isCountByTotalSpents && loyaltyTier.isCountByTotalVisits) {
              //     if (totalSpent >= loyaltyTier.levels[i].levelTotalSpents &&
              //       totalVisit >= loyaltyTier.levels[i].levelTotalVisits) {
              //       isValid = true;
              //     }
              //   }

              //   if (isValid) {
              //     nextLoyaltyTierLevel = loyaltyTier.levels[i];

              //     if (loyaltyStampUserCRM.levelOrderIndex >= nextLoyaltyTierLevel.orderIndex) {
              //       // means already level below current user, continue

              //     }
              //   }
              //   else {
              //     break;
              //   }
              // }

              // if (nextLoyaltyTierLevel) {
              //   firestore().collection(Collections.CRMUser).doc(loyaltyStampUserCRM.uniqueId).update({
              //     levelId: nextLoyaltyTierLevel.levelId,
              //     levelName: nextLoyaltyTierLevel.levelName,
              //     levelCashbackRate: nextLoyaltyTierLevel.levelCashbackRate,

              //     levelTotalSpents: nextLoyaltyTierLevel.levelTotalSpents,
              //     levelTotalVisits: nextLoyaltyTierLevel.levelTotalVisits,

              //     levelOrderIndex: nextLoyaltyTierLevel.orderIndex,
              //     levelIsActive: nextLoyaltyTierLevel.isActive,

              //     updatedAt: Date.now(),
              //   });
              // }
            }
            else {
              // new tier system user

              // // console.log('========new tier system user========');

              // firestore().collection(Collections.CRMUser).doc(loyaltyStampUserCRM.uniqueId).update({
              //     levelId: loyaltyTier.levels[0].levelId,
              //     levelName: loyaltyTier.levels[0].levelName,
              //     levelCashbackRate: loyaltyTier.levels[0].levelCashbackRate,

              //     levelTotalSpents: loyaltyTier.levels[0].levelTotalSpents,
              //     levelTotalVisits: loyaltyTier.levels[0].levelTotalVisits,

              //     levelOrderIndex: loyaltyTier.levels[0].orderIndex,
              //     levelIsActive: loyaltyTier.levels[0].isActive,

              //     updatedAt: Date.now(),
              // });

              // console.log('========new tier system user========');

              // 2024-08-21 - no create/delete here

              // let nextLoyaltyTierLevel = null;

              // let isValid = false;

              // if (loyaltyTier.isCountByTotalSpents && !loyaltyTier.isCountByTotalVisits) {
              //   if (totalSpent >= loyaltyTier.levels[0].levelTotalSpents) {
              //     isValid = true;
              //   }
              // }
              // else if (!loyaltyTier.isCountByTotalSpents && loyaltyTier.isCountByTotalVisits) {
              //   if (totalVisit >= loyaltyTier.levels[0].levelTotalVisits) {
              //     isValid = true;
              //   }
              // }
              // else if (loyaltyTier.isCountByTotalSpents && loyaltyTier.isCountByTotalVisits) {
              //   if (totalSpent >= loyaltyTier.levels[0].levelTotalSpents &&
              //     totalVisit >= loyaltyTier.levels[0].levelTotalVisits) {
              //     isValid = true;
              //   }
              // }

              // if (isValid) {
              //   nextLoyaltyTierLevel = loyaltyTier.levels[0];

              //   if (loyaltyStampUserCRM.levelOrderIndex >= nextLoyaltyTierLevel.orderIndex) {
              //     // means already level below current user, continue

              //   }
              // }
              // else {
              // }

              // if (nextLoyaltyTierLevel) {
              //   firestore().collection(Collections.CRMUser).doc(loyaltyStampUserCRM.uniqueId).update({
              //     levelId: nextLoyaltyTierLevel.levelId,
              //     levelName: nextLoyaltyTierLevel.levelName,
              //     levelCashbackRate: nextLoyaltyTierLevel.levelCashbackRate,

              //     levelTotalSpents: nextLoyaltyTierLevel.levelTotalSpents,
              //     levelTotalVisits: nextLoyaltyTierLevel.levelTotalVisits,

              //     levelOrderIndex: nextLoyaltyTierLevel.orderIndex,
              //     levelIsActive: nextLoyaltyTierLevel.isActive,

              //     updatedAt: Date.now(),
              //   });
              // }
            }
          }
        }

        /////////////////////////////////////////////////////////////

        // time to create loyalty campaign credit transaction

        // 2024-08-21 - no create/delete here
        // 2024-08-21 - here need to refund/undo refund

        let loyaltyCampaignCreditTransaction = {
          uniqueId: uuidv4(),

          userId: loyaltyStampUserActualId,
          crmUserId: loyaltyStampUserCRMId,
          email: loyaltyStampUserEmail,
          phone: loyaltyStampUserPhone,

          orderId: destOrder.uniqueId,
          merchantId,
          outletId,

          // transactionType: LOYALTY_CAMPAIGN_CREDIT_TRANSACTION_TYPE.EARN,
          transactionType: params.orderProcessType === ORDER_PROCESS_TYPE.REFUND ? LOYALTY_CAMPAIGN_CREDIT_TRANSACTION_TYPE.DEDUCT : LOYALTY_CAMPAIGN_CREDIT_TRANSACTION_TYPE.EARN,
          // amount: +parseFloat(destOrder.finalPrice * cashbackPercentage).toFixed(2),
          amount: +parseFloat(params.processAmount * cashbackPercentage).toFixed(2),

          merchantName,
          outletName,
          merchantLogo,
          outletCover,

          createdAt: Date.now(),
          updatedAt: Date.now(),
          deletedAt: null,
        };

        // firestore().collection(Collections.LoyaltyCampaignCreditTransaction).doc(loyaltyCampaignCreditTransaction.uniqueId).set(loyaltyCampaignCreditTransaction);
        firestore().collection(Collections.UserPointsTransaction).doc(loyaltyCampaignCreditTransaction.uniqueId).set(loyaltyCampaignCreditTransaction);

        // 2024-08-21 - no create/delete here

        // ///////////////////////////////////////////////////////////////

        // // 2022-11-22 - Try to add tags from the product to the crm user

        // let tagUserEmail = loyaltyStampUserEmail ? loyaltyStampUserEmail : '';
        // let tagUserPhone = loyaltyStampUserPhone ? loyaltyStampUserPhone : '';
        // let tagUserFcm = '';
        // if (loyaltyStampUserActual && loyaltyStampUserActual.tokenFcm) {
        //   tagUserFcm = loyaltyStampUserActual.tokenFcm;
        // }

        // if (tagUserEmail || tagUserPhone || tagUserFcm) {
        //   for (let tagIndex = 0; tagIndex < destOrder.cartItems.length; tagIndex++) {
        //     let outletItemSnapshot = await firestore()
        //       .collection(Collections.OutletItem)
        //       .where('uniqueId', '==', destOrder.cartItems[tagIndex].itemId)
        //       .limit(1)
        //       .get();

        //     let outletItemToTag = null;
        //     if (!outletItemSnapshot.empty) {
        //       outletItemToTag = outletItemSnapshot.docs[0].data();
        //     }

        //     if (outletItemToTag.crmUserTagIdList &&
        //       outletItemToTag.crmUserTagIdList.length > 0) {
        //       for (let crmTagIndex = 0; crmTagIndex < outletItemToTag.crmUserTagIdList.length; crmTagIndex++) {
        //         let crmUserTagSnapshot = await firestore()
        //           .collection(Collections.CRMUserTag)
        //           .where('uniqueId', '==', outletItemToTag.crmUserTagIdList[crmTagIndex])
        //           .limit(1)
        //           .get();

        //         let crmUserTag = null;
        //         if (!crmUserTagSnapshot.empty) {
        //           crmUserTag = outletItemSnapshot.docs[0].data();
        //         }

        //         if (crmUserTag) {
        //           let emailListNew = (crmUserTag.emailList && crmUserTag.emailList.length > 0) ? crmUserTag.emailList : [];
        //           let phoneListNew = (crmUserTag.phoneList && crmUserTag.phoneList.length > 0) ? crmUserTag.phoneList : [];
        //           let tokenFcmListNew = (crmUserTag.tokenFcmList && crmUserTag.tokenFcmList.length > 0) ? crmUserTag.tokenFcmList : [];

        //           if (tagUserEmail && !emailListNew.includes(tagUserEmail)) {
        //             emailListNew.push(tagUserEmail);
        //           }

        //           if (tagUserPhone && !phoneListNew.includes(tagUserPhone)) {
        //             phoneListNew.push(tagUserPhone);
        //           }

        //           if (tagUserFcm && !tokenFcmListNew.includes(tagUserFcm)) {
        //             tokenFcmListNew.push(tagUserFcm);
        //           }

        //           firestore()
        //             .collection(Collections.CRMUserTag)
        //             .doc(outletItemToTag.crmUserTagIdList[crmTagIndex])
        //             .update({
        //               emailList: emailListNew,
        //               phoneList: phoneListNew,
        //               tokenFcmList: tokenFcmListNew,

        //               updatedAt: Date.now(),
        //             });
        //         }
        //       }
        //     }
        //   }
        // }

        // ///////////////////////////////////////////////////////////////

        // // 2022-12-22 - Auto create and/or assign tags to user

        // if (loyaltyStampUserEmail && loyaltyStampUserPhone) {
        //   await assignDefaultTagsToUser({
        //     email: loyaltyStampUserEmail,
        //     phone: loyaltyStampUserPhone,
        //   }, destOrder, loyaltyStampUserCRM);
        // }

        // ///////////////////////////////////////////////////////////////

        // // 2022-12-23 - Earn loyalty stamps

        // if (destOrder.loyaltyStampBuyList && destOrder.loyaltyStampBuyList.length > 0) {
        //   for (let i = 0; i < destOrder.loyaltyStampBuyList.length; i++) {
        //     const loyaltyStampBuy = destOrder.loyaltyStampBuyList[i];

        //     const userLoyaltyStampSnapshot = await firestore()
        //       .collection(Collections.UserLoyaltyStamp)
        //       .where('loyaltyStampId', '==', loyaltyStampBuy.loyaltyStampId)
        //       // .where('email', '==', userEmail)
        //       .where('email', '==', loyaltyStampUserEmail)
        //       .where('deletedAt', '==', null)
        //       .limit(1)
        //       .get();

        //     let userLoyaltyStamp = null;
        //     if (!userLoyaltyStampSnapshot.empty) {
        //       userLoyaltyStamp = userLoyaltyStampSnapshot.docs[0].data();
        //     }

        //     if (userLoyaltyStamp) {
        //       // exist

        //       let buyIdHistory = [];
        //       let buyHistory = [];

        //       if (userLoyaltyStamp.buyIdHistory.includes(loyaltyStampBuy.lsItemId)) {
        //         // buyIdHistory = userLoyaltyStamp.buyIdHistory;
        //         // buyHistory = userLoyaltyStamp.buyHistory;

        //         buyIdHistory = [
        //           ...userLoyaltyStamp.buyIdHistory,
        //           loyaltyStampBuy.lsItemId,
        //         ];
        //         buyHistory = [
        //           ...userLoyaltyStamp.buyHistory,
        //           {
        //             ...loyaltyStampBuy,
        //             userOrderId: destOrder.uniqueId,
        //           },
        //         ];
        //       }
        //       else {
        //         buyIdHistory = [
        //           ...userLoyaltyStamp.buyIdHistory,
        //           loyaltyStampBuy.lsItemId,
        //         ];
        //         buyHistory = [
        //           ...userLoyaltyStamp.buyHistory,
        //           {
        //             ...loyaltyStampBuy,
        //             userOrderId: destOrder.uniqueId,
        //           },
        //         ];
        //       }

        //       let stampCount = userLoyaltyStamp.stampCount + loyaltyStampBuy.noOfStamp;
        //       // stampCount = Math.min(stampCount, ) // as for now let if overflow max stamp num, show as capped amount at ui

        //       let isCompleted = await checkUserLoyaltyStampCompleted(loyaltyStampBuy.loyaltyStampId, {
        //         ...userLoyaltyStamp,

        //         stampCount,

        //         buyIdHistory,
        //         buyHistory,
        //       });

        //       firestore().collection(Collections.UserLoyaltyStamp).doc(userLoyaltyStamp.uniqueId).update({
        //         stampCount,

        //         buyIdHistory,
        //         buyHistory,

        //         deletedAt: isCompleted ? Date.now() : null,

        //         updatedAt: Date.now(),
        //       });
        //     }
        //     else {
        //       // not exist

        //       // 2024-04-30 - inherit stamp count
        //       let stampCountInherit = 0;
        //       if (loyaltyStampUserCRM && loyaltyStampUserCRM.stampInherit &&
        //         loyaltyStampUserCRM.stampInherit[loyaltyStampBuy.loyaltyStampId] > 0) {
        //         stampCountInherit = loyaltyStampUserCRM.stampInherit[loyaltyStampBuy.loyaltyStampId];
        //       }

        //       userLoyaltyStamp = {
        //         uniqueId: uuidv4(),
        //         // userId: user.firebaseUid,
        //         userId: loyaltyStampUserActualId ? loyaltyStampUserActualId : '',
        //         crmUserId: loyaltyStampUserCRMId,
        //         email: loyaltyStampUserEmail,
        //         phone: loyaltyStampUserPhone,

        //         outletId,
        //         merchantId,

        //         merchantLogo,
        //         outletCover,

        //         loyaltyStampId: loyaltyStampBuy.loyaltyStampId,

        //         stampCount: loyaltyStampBuy.noOfStamp + stampCountInherit,

        //         buyIdHistory: [
        //           loyaltyStampBuy.lsItemId,
        //         ],
        //         buyHistory: [
        //           {
        //             ...loyaltyStampBuy,
        //             userOrderId: destOrder.uniqueId,
        //           },
        //         ],
        //         getIdHistory: [

        //         ],
        //         getHistory: [

        //         ],

        //         createdAt: Date.now(),
        //         updatedAt: Date.now(),
        //         deletedAt: null,
        //       };

        //       let isCompleted = await checkUserLoyaltyStampCompleted(loyaltyStampBuy.loyaltyStampId, userLoyaltyStamp);
        //       userLoyaltyStamp.deletedAt = isCompleted ? Date.now() : null;

        //       firestore().collection(Collections.UserLoyaltyStamp).doc(userLoyaltyStamp.uniqueId).set(userLoyaltyStamp);
        //     }
        //   }
        // }

        // ///////////////////////////////////////////////////////////////
      }
      catch (ex) {
        // console.log('loyalty campaign exception');
        console.log(ex);
      }
    }

    // redeem loyalty campaign for current user

    // if (loyaltyCampaignId && userLoyaltyCampaignId) {
    //   await firestore().collection(Collections.UserLoyaltyCampaign).doc(userLoyaltyCampaignId).update({
    //     redeemDate: Date.now(),

    //     updatedAt: Date.now(),
    //   });
    // }

    // redeem loyalty credit for current user

    // if (discountPromotionsTotalLCC && discountPromotionsTotalLCC > 0) {
    //   let loyaltyCampaignCreditTransaction = {
    //     uniqueId: uuidv4(),

    //     userId: loyaltyStampUserActualId,
    //     crmUserId: loyaltyStampUserCRMId,
    //     email: loyaltyStampUserEmail,
    //     phone: loyaltyStampUserPhone,

    //     orderId: destOrder.uniqueId,
    //     merchantId: merchantId,
    //     outletId: outletId,

    //     transactionType: LOYALTY_CAMPAIGN_CREDIT_TRANSACTION_TYPE.REDEEM,
    //     amount: -discountPromotionsTotalLCC,

    //     merchantName: merchantName,
    //     outletName: outletName,
    //     merchantLogo: merchantLogo,
    //     outletCover: outletCover,

    //     createdAt: Date.now(),
    //     updatedAt: Date.now(),
    //     deletedAt: null,
    //   };

    //   await firestore().collection(Collections.LoyaltyCampaignCreditTransaction).doc(loyaltyCampaignCreditTransaction.uniqueId).set(loyaltyCampaignCreditTransaction);
    // }
  }, 1000);
};

export const isOutletDisplay = () => {
  // console.log('===============');
  // console.log('isOutletDisplay');
  // console.dir(global.currOutlet, { depth : null });
  // console.log(global.odPairingDevice);
  // console.log(global.odPairingType);
  // console.log(!global.supportCodeData);
  // console.log('===============');

  if (global.currOutlet &&
    global.currOutlet.uniqueId &&
    global.currOutlet.odActive &&
    global.odPairingDevice === OUTLET_DISPLAY_PAIRING_DEVICE.DISPLAY &&
    global.odPairingType &&
    !global.supportCodeData
  ) {
    return true;
  }
  else {
    return false;
  }
};

export const isOutletHost = () => {
  if (global.currOutlet &&
    global.currOutlet.uniqueId &&
    global.currOutlet.odActive &&
    global.odPairingDevice === OUTLET_DISPLAY_PAIRING_DEVICE.HOST &&
    global.odPairingType &&
    !global.supportCodeData
  ) {
    return true;
  }
  else {
    return false;
  }
};

export const updateOutletDisplayData = async (
  props,
) => {
  try {
    if (isOutletHost()) {
      const documentKey = `${global.currOutlet.uniqueId}-${global.odPairingType}`;

      // Reference to the document
      const documentRef = firestore().collection(Collections.OutletDisplay).doc(documentKey);

      // Use the set method with merge: true to update the field or create the document
      await documentRef.set(
        {
          // [field]: value,

          ...props,

          uniqueId: documentKey,

          outletId: global.currOutlet.uniqueId,
          pairingType: global.odPairingType,

          createdAt: Date.now(),
          updatedAt: Date.now(),
          deletedAt: null,
        },
        { merge: true } // Merge ensures it won't overwrite the document if it already exists
      );

      console.log(`Document updated successfully in collection: ${Collections.OutletDisplay}`);
    }
  } catch (error) {
    console.error('Error updating the document:', error);
  }
};

export const getDeviceUniqueId = async () => {
  const duid = await AsyncStorage.getItem('duid');

  if (duid) {
    global.duid = duid;

    return duid;
  }
  else {
    const duidNew = uuidv4();

    await AsyncStorage.setItem('duid', duidNew);

    global.duid = duidNew;

    return duidNew;
  }
};

export const generateCoordinatesForTables = (tables, screenWidth, screenHeight, columns, cellWidth, cellHeight, marginXPercent, marginYPercent) => {
  if (columns <= 0 || cellWidth <= 0 || cellHeight <= 0) {
    throw new Error("Columns, cellWidth, and cellHeight must be greater than 0.");
  }
  if (marginXPercent < 0 || marginYPercent < 0) {
    throw new Error("Margins must be non-negative percentages.");
  }

  // Calculate the actual margins based on percentage values
  const marginX = (marginXPercent / 100) * screenWidth;
  const marginY = (marginYPercent / 100) * screenHeight;

  global.draggableParam.marginX = marginX;
  global.draggableParam.marginY = marginY;

  // const totalItems = 24; // Fixed number of items
  const coordinates = [];
  const rowsPerScreen = Math.floor(screenHeight / (cellHeight + marginY)); // Max rows that fit on the screen
  const maxCellsPerScreen = rowsPerScreen * columns; // Max cells that fit on the screen

  for (let i = 0; i < tables.length; i++) {
    const effectiveIndex = i % maxCellsPerScreen; // Wrap index to fit within the screen
    const columnIndex = effectiveIndex % columns; // Determine column index
    const rowIndex = Math.floor(effectiveIndex / columns); // Determine row index

    let x = columnIndex * (cellWidth + marginX); // X-coordinate with calculated margin
    let y = rowIndex * (cellHeight + marginY);  // Y-coordinate with calculated margin

    if (tables[i].posX !== undefined && tables[i].posY !== undefined) {
      console.log('saved pos info');
      console.log(`code: ${tables[i].code}`);
      console.log(`posX: ${tables[i].posX}`);
      console.log(`posY: ${tables[i].posY}`);

      x = tables[i].posX * screenWidth; // X-coordinate
      y = tables[i].posY * screenHeight;  // Y-coordinate
    }

    if (global.draggableTablePosDict[tables[i].uniqueId]) {
      const dragPos = global.draggableTablePosDict[tables[i].uniqueId];

      // x = dragPos.x * screenWidth; // X-coordinate
      // y = dragPos.y * screenHeight;  // Y-coordinate

      // x = dragPos.actualX - global.draggableParam.minX; // X-coordinate
      // y = dragPos.actualY - global.draggableParam.minY;  // Y-coordinate

      x = dragPos.actualX; // X-coordinate
      y = dragPos.actualY;  // Y-coordinate
    }

    // console.log(tables[i].code);
    // console.log(x);
    // console.log(y);
    // console.log(tables[i].posX);
    // console.log(tables[i].posY);

    coordinates.push(
      {
        x: x,
        y: y,
        ...tables[i],
      },
    );
  }

  // console.log('generateCoordinatesForTables');
  // console.log(coordinates);
  // console.log(screenWidth);
  // console.log(screenHeight);
  // console.log(columns);
  // console.log(cellWidth);
  // console.log(cellHeight);

  return coordinates;
};

export const normalizeToRange = (x, y, minX, maxX, minY, maxY) => {
  // Normalize x and y within the range 0 to 1
  const normalizedX = (x - minX) / (maxX - minX);
  const normalizedY = (y - minY) / (maxY - minY);

  return { x: normalizedX, y: normalizedY };
};

export const excludeSkipScItems = (
  totalSc,
  cartItems,
  scRate,
  isRefundOrder = false,
) => {
  if (cartItems) {
    let toDeductSc = 0;
    for (let i = 0; i < cartItems.length; i++) {
      let currCartItem = cartItems[i];
      let addOns = currCartItem.addOns ? currCartItem.addOns : currCartItem.addons;

      let itemPrice = currCartItem.price;
      if (isRefundOrder) {
        itemPrice = currCartItem.priceBackup ? currCartItem.priceBackup : currCartItem.price;
      }

      let toSkipSc = false;
      for (let j = 0; j < addOns.length; j++) {
        if (addOns[j].skipSc) {
          toSkipSc = true;
          break;
        }
      }

      if (toSkipSc) {
        toDeductSc = BigNumber(toDeductSc).plus(
          (BigNumber(itemPrice).multipliedBy(scRate))
        ).toNumber();
      }
    }

    if (BigNumber(toDeductSc).isGreaterThan(0)) {
      let totalScNew = Math.max(BigNumber(totalSc).minus(toDeductSc).toNumber(), 0);

      return totalScNew;
    }
    else {
      return totalSc;
    }
  }
  else {
    return totalSc;
  }
};

export const claimTaggableVoucherMerchantFunc = async (
  params,
) => {
  let body = {
    funcCall: true,
    // batchItem: batch,

    // tcId: tcCartItem.creditTypeId,
    // tcOrderId: params.uniqueId,

    userPhone: params.userPhone,
    outletId: params.outletId,
    merchantId: params.merchantId,
    merchantName: params.merchantName,
    outletName: params.outletName,
    merchantLogo: params.merchantLogo,
    outletCover: params.outletCover,
    userName: params.userName,

    dob: moment().valueOf(),

    address: '',
    lat: 0,
    lng: 0,

    // userOrderId: (registerUserOrder.uniqueId && !registerUserOrder.isCashbackClaimed) ? registerUserOrder.uniqueId : null,
    // taggableVoucherId: (showVoucherInfo && showVoucherInfo.uniqueId) ? showVoucherInfo.uniqueId : null,
    // taggableVoucherId: batch.voucherId,
    taggableVoucherId: params.taggableVoucherId,

    isVerified: false,

    // userIdAnonymous: params.userIdAnonymous,
    // toConvertAnonymousPoints: true,

    //////////////////////

    // google review

    // rating: rating,
    // review: review,
    toUpdateGR: false,
    // outletEmail: selectedOutlet.email,

    //////////////////////

    // 2024-02-29 - loyalty voucher support

    // loyaltyCampaignId: (showVoucherInfo && showVoucherInfo.loyaltyCampaignId) ? (showVoucherInfo.loyaltyCampaignId) : '',
    // batchId: (showVoucherInfo && showVoucherInfo.batchId) ? (showVoucherInfo.batchId) : '',
    // batchIndex: (showVoucherInfo && showVoucherInfo.batchIndex) ? (showVoucherInfo.batchIndex) : '',

    //////////////////////  

    // 2024-06-18 e-invoice
    // toUpdateEiDetails: toUpdateEiDetails ? toUpdateEiDetails : false,
    // epNameTo: epNameToTemp ? epNameToTemp : epNameTo,
    // // epPhoneTo: epPhoneToTemp ? epPhoneToTemp : epPhoneTo,
    // epPhoneTo: userNumber,
    // epAddr1To: epAddr1ToTemp ? epAddr1ToTemp : epAddr1To,
    // epCityTo: epCityToTemp ? epCityToTemp : epCityTo,
    // epCodeTo: epCodeToTemp ? epCodeToTemp : epCodeTo,
    // epStateTo: epStateToTemp ? epStateToTemp : epStateTo,
    // emailSecond: epEmailToTemp ? epEmailToTemp : epEmailTo,
    // tin: epTinToTemp ? epTinToTemp : epTinTo,
    // eiIdType: epIdTypeToTemp ? epIdTypeToTemp : epIdTypeTo,
    // eiId: epIdToTemp ? epIdToTemp : epIdTo,
  };

  await new Promise(async (resolve, reject) => {
    ApiClient.POST(API.claimTaggableVoucherMerchant, body).then((result) => {
      callback && callback();

      resolve(result);
    }).catch(ex => {
      console.error(ex);

      logToFile(ex);

      reject(ex);
    });
  });
};

export function extractImageNameFromUrl(url) {
  const regex = /(?:\/|^)image(\d*)\.\w+$/;
  const match = url.match(regex);

  if (match) {
    const name = "image";
    const number = match[1] ? parseInt(match[1], 10) : null;
    return { name, number };
  }

  return null; // Return null if no match is found
}

export const convertOutletOpeningToGrabOpeningHours = async (data) => {
  const dayMap = {
    monday: "mon",
    tuesday: "tue",
    wednesday: "wed",
    thursday: "thu",
    friday: "fri",
    saturday: "sat",
    sunday: "sun"
  };

  const openingHour = {};

  Object.keys(dayMap).forEach(day => {
    if (data[day]) {
      const [start, end] = data[day].split('-');

      // Convert time format from "HHMM" to "HH:MM"
      const formatTime = (time) => `${time.slice(0, 2)}:${time.slice(2)}`;

      openingHour[dayMap[day]] = [{
        startTime: formatTime(start),
        endTime: formatTime(end)
      }];
    }
  });

  return {
    openingHour,
    force: true
  };
}

export const consumeOutletSupplyItemRecursive = async (outletSupplyItemParam, currCartItem, userOrder, qtyRecursive = 1) => {
  let outletSupplyItem = null;
  console.log('consume 1');
  console.log(outletSupplyItemParam);
  try {
    let osiKey = '';
    if (outletSupplyItemParam.outletSupplyItemId) {
      osiKey = outletSupplyItemParam.outletSupplyItemId;
    }
    else if (outletSupplyItemParam.uniqueId) {
      osiKey = outletSupplyItemParam.uniqueId;
    }
    else if (outletSupplyItemParam.osiId) {
      osiKey = outletSupplyItemParam.osiId;
    }
    else if (outletSupplyItemParam.supplyItemId) {
      osiKey = outletSupplyItemParam.supplyItemId;
    }

    if (osiKey) {
      let outletSupplyItemFindSnapshot = await firestore()
        .collection(Collections.OutletSupplyItem)
        .where('uniqueId', '==', osiKey)
        .limit(1)
        .get();

      console.log('consume 2');

      if (outletSupplyItemFindSnapshot && !outletSupplyItemFindSnapshot.empty) {
        outletSupplyItem = outletSupplyItemFindSnapshot.docs[0].data(); // Use .docs[0] to access the document data
        console.log('Outlet Supply Item found:', outletSupplyItem);
      } else {
        console.warn('No Outlet Supply Item found for:', outletSupplyItemParam);
        return false; // Return false if no item is found
      }

      console.log('consume 3');

      if (outletSupplyItem.sli && outletSupplyItem.sli.length > 0) {
        for (let j = 0; j < outletSupplyItem.sli.length; j++) {
          const currStockLinkItem = outletSupplyItem.sli[j];
          console.log('consume 4');
          if (currStockLinkItem.supplyItemId && currStockLinkItem.quantityUsage > 0) {
            // consume
            let osi = null;
            if (global.outletSupplyItemsDict[currStockLinkItem.supplyItemId]) {
              osi = global.outletSupplyItemsDict[currStockLinkItem.supplyItemId];
            }
            console.log('consume 5');
            await firestore()
              .collection(Collections.OutletSupplyItem)
              .doc(currStockLinkItem.supplyItemId)
              .update({
                quantity: firestore.FieldValue.increment(-calculateQuantityUsageAndQuantityWastage(currStockLinkItem) * currCartItem.quantity * qtyRecursive),
                updatedAt: Date.now(),
              });

            console.log(`Updated Outlet Supply Item: ${currStockLinkItem.supplyItemId}`);

            setTimeout(() => {
              recordOutletSupplyItemTransaction({
                outletSupplyItemId: currStockLinkItem.supplyItemId,
                name: currStockLinkItem.name,
                updatedAt: Date.now(),
                cn: Collections.OutletSupplyItem,
                cnId: currCartItem.itemId,
                cnData: {
                  userOrder,
                  cartItem: currCartItem,
                  quantity: currCartItem.quantity,
                  osiParent: outletSupplyItemParam,
                },
                am: -calculateQuantityUsageAndQuantityWastage(currStockLinkItem) * currCartItem.quantity * qtyRecursive,
                amw: -calculateQuantityWastage(currStockLinkItem) * currCartItem.quantity * qtyRecursive,
                osi,
              });

              // Recursive call
              consumeOutletSupplyItemRecursive(currStockLinkItem, currCartItem, userOrder,
                calculateQuantityUsageAndQuantityWastage(currStockLinkItem) * currCartItem.quantity * qtyRecursive);
              console.log('record end');
            }, j * global.currOutlet.ositTime ? global.currOutlet.ositTime : 3000); // wait some time before next call
            console.log('consume last');
          }
          else {
            console.log('currStockLinkItem false return message', currStockLinkItem);
          }
        }
      } else {
        console.log('No stock link items found for:', outletSupplyItem);
        return true; // Return true if no stock link items
      }
    }
    else {
      return true;
    }

  } catch (error) {
    console.error('Error in consumeOutletSupplyItemRecursive:', error);
  }
};

////////////////////////////////////////////

// 2025-03-16 - consume wo order

export const consumeOutletSupplyItemRecursiveWO = async (outletSupplyItemParam, woItem, woOrder, qtyRecursive = 1) => {
  let outletSupplyItem = null;
  console.log('consume 1');
  console.log(outletSupplyItemParam);
  try {
    let osiKey = '';
    if (outletSupplyItemParam.outletSupplyItemId) {
      osiKey = outletSupplyItemParam.outletSupplyItemId;
    }
    else if (outletSupplyItemParam.uniqueId) {
      osiKey = outletSupplyItemParam.uniqueId;
    }
    else if (outletSupplyItemParam.osiId) {
      osiKey = outletSupplyItemParam.osiId;
    }

    if (osiKey) {
      let outletSupplyItemFindSnapshot = await firestore()
        .collection(Collections.OutletSupplyItem)
        .where('uniqueId', '==', osiKey)
        .limit(1)
        .get();

      console.log('consume 2');

      if (outletSupplyItemFindSnapshot && !outletSupplyItemFindSnapshot.empty) {
        outletSupplyItem = outletSupplyItemFindSnapshot.docs[0].data(); // Use .docs[0] to access the document data
        console.log('Outlet Supply Item found:', outletSupplyItem);
      } else {
        console.warn('No Outlet Supply Item found for:', outletSupplyItemParam);
        return false; // Return false if no item is found
      }

      console.log('consume 3');

      if (outletSupplyItem.sli && outletSupplyItem.sli.length > 0) {
        for (let j = 0; j < outletSupplyItem.sli.length; j++) {
          const currStockLinkItem = outletSupplyItem.sli[j];
          console.log('consume 4');
          if (currStockLinkItem.supplyItemId && currStockLinkItem.quantityUsage > 0) {
            // consume
            let osi = null;
            if (global.outletSupplyItemsDict[currStockLinkItem.supplyItemId]) {
              osi = global.outletSupplyItemsDict[currStockLinkItem.supplyItemId];
            }
            console.log('consume 5');
            await firestore()
              .collection(Collections.OutletSupplyItem)
              .doc(currStockLinkItem.supplyItemId)
              .update({
                quantity: firestore.FieldValue.increment(-calculateQuantityUsageAndQuantityWastage(currStockLinkItem) * woItem.usage * qtyRecursive),
                updatedAt: Date.now(),
              });

            console.log(`Updated Outlet Supply Item: ${currStockLinkItem.supplyItemId}`);

            setTimeout(() => {
              recordOutletSupplyItemTransaction({
                outletSupplyItemId: currStockLinkItem.supplyItemId,
                name: currStockLinkItem.name,
                updatedAt: Date.now(),
                cn: Collections.OutletSupplyItem,
                cnId: woItem.itemId,
                cnData: {
                  woOrder,
                  woItem: woItem,
                },
                am: -calculateQuantityUsageAndQuantityWastage(currStockLinkItem) * woItem.usage * qtyRecursive,
                amw: -calculateQuantityWastage(currStockLinkItem) * woItem.usage * qtyRecursive,
                osi,
              });

              // Recursive call
              consumeOutletSupplyItemRecursiveWO(currStockLinkItem, woItem, woOrder, calculateQuantityUsageAndQuantityWastage(currStockLinkItem) * woItem.usage * qtyRecursive);
              console.log('record end');
            }, j * global.currOutlet.ositTime ? global.currOutlet.ositTime : 3000); // wait some time before next call
            console.log('consume last');
          }
          else {
            console.log('currStockLinkItem false return message', currStockLinkItem);
          }
        }
      } else {
        console.log('No stock link items found for:', outletSupplyItem);
        return true; // Return true if no stock link items
      }
    }
    else {
      return true;
    }

  } catch (error) {
    console.error('Error in consumeOutletSupplyItemRecursiveWO:', error);
  }
};

////////////////////////////////////////////

export const calculateQuantityUsageAndQuantityWastage = (currStockLinkItem) => {
  return (currStockLinkItem.quantityUsage +
    (currStockLinkItem.quantityWastage ?
      (BigNumber(currStockLinkItem.quantityUsage).multipliedBy(currStockLinkItem.quantityWastage).dividedBy(100).toNumber())
      : 0)
  );
};

export const calculateQuantityWastage = (currStockLinkItem) => {
  return (
    (currStockLinkItem.quantityWastage ?
      (BigNumber(currStockLinkItem.quantityUsage).multipliedBy(currStockLinkItem.quantityWastage).dividedBy(100).toNumber())
      : 0)
  );
};

export const calculateStockLinkItemCost = (currStockLinkItem) => {
  return (currStockLinkItem.quantityUsage +
    (currStockLinkItem.quantityWastage ?
      (BigNumber(currStockLinkItem.quantityUsage).multipliedBy(currStockLinkItem.quantityWastage).dividedBy(100).toNumber())
      : 0)
  );
};

export const getCostUsageWastageFromUserOrder = (
  userOrder,
  cartItem = null,
  outletItem = null,
  level = '',
  // dataCompare = null,
  // outletItems,
) => {
  let result = {
    am: 0,
    amw: 0,
    amc: 0,
    amwc: 0,
  };

  if (outletItem) {
    const ciList = Object.entries(userOrder.ci ? userOrder.ci : {}).map(
      ([key, value]) => ({ key, value }),
    );

    if (level === COST_USAGE_WASTAGE_LEVEL.CATEGORY) {
      for (let i = 0; i < ciList.length; i++) {
        if (ciList[i].value.cid === cartItem.cartItemDate &&
          outletItem.uniqueId === ciList[i].value.cnId
          // && outletItem.categoryId === dataCompare.uniqueId
        ) {
          result.am += Math.abs(ciList[i].value.am) - Math.abs(ciList[i].value.amw);
          result.amw += Math.abs(ciList[i].value.amw);
          result.amc += Math.abs(ciList[i].value.amc);
          result.amwc += Math.abs(ciList[i].value.amwc || 0);

          // const outletItem = outletItems.find(item => {
          //   return item.uniqueId === ciList[i].value.cnId && item.categoryId === dataCompare.uniqueId;
          // });

          // if (outletItem) {
          //   result.am += ciList[i].value.am - ciList[i].value.amw;
          //   result.amw += ciList[i].value.amw;
          //   result.amc += ciList[i].value.amc;
          // }
        }
      }
    }
    else if (level === COST_USAGE_WASTAGE_LEVEL.PRODUCT) {
      for (let i = 0; i < ciList.length; i++) {
        if (ciList[i].value.cid === cartItem.cartItemDate &&
          outletItem.uniqueId === ciList[i].value.cnId
        ) {
          result.am += Math.abs(ciList[i].value.am) - Math.abs(ciList[i].value.amw);
          result.amw += Math.abs(ciList[i].value.amw);
          result.amc += Math.abs(ciList[i].value.amc);
          result.amwc += Math.abs(ciList[i].value.amwc || 0);

          // const outletItem = outletItems.find(item => {
          //   return item.uniqueId === ciList[i].value.cnId &&
          //     item.uniqueId === dataCompare.uniqueId;
          // });

          // if (outletItem) {
          //   result.am += ciList[i].value.am - ciList[i].value.amw;
          //   result.amw += ciList[i].value.amw;
          //   result.amc += ciList[i].value.amc;
          // }
        }
      }
    }

    return result;
  }
  else {
    return result;
  }
};

export const getOrderFinalPrice = (order) => {
  let orderFinalPrice = order.finalPrice;

  if (order.depoAmt > 0) {
    orderFinalPrice = BigNumber(order.finalPrice).minus(order.depoAmt).toNumber();
  }

  return orderFinalPrice;
};

/////////////////////////////////////////////

// 2025-04-15 - isTabletWrapper implementation

// Cache the result
let isTabletCache = null;

export const isTabletV2 = async () => {
  // Return cached result if available
  if (isTabletCache !== null) return isTabletCache;

  try {
    // Check using DeviceInfo first (most reliable)
    if (await DeviceInfo.isTablet()) {
      isTabletCache = true;
      return true;
    }

    // Platform-specific checks
    if (Platform.OS === 'ios') {
      // Check iPad models
      const model = await DeviceInfo.getModel();
      if (model.startsWith('iPad')) {
        isTabletCache = true;
        return true;
      }

      // Check screen aspect ratio for iPads
      const { height, width } = Dimensions.get('window');
      const aspectRatio = Math.max(height, width) / Math.min(height, width);
      if (aspectRatio < 1.6) {
        isTabletCache = true;
        return true;
      }
    } else {
      // Android checks
      const model = await DeviceInfo.getModel();
      const brand = await DeviceInfo.getBrand();
      const manufacturer = await DeviceInfo.getManufacturer();

      // Check for tablet keywords in model/brand/manufacturer
      const tabletKeywords = [
        'Tab', 'Pad', 'MediaPad', 'Galaxy Tab', 'SM-T',
        'Lenovo Tab', 'Nexus 7', 'Nexus 9', 'Nexus 10',
        'XT', 'KF', 'MI PAD'
      ];

      const isKnownTablet = tabletKeywords.some(keyword =>
        model.includes(keyword) ||
        manufacturer.includes(keyword) ||
        brand.includes(keyword)
      );

      if (isKnownTablet) {
        isTabletCache = true;
        return true;
      }

      // Check screen size
      const { width } = Dimensions.get('window');
      if (width >= 600) {  // Typical tablet breakpoint
        isTabletCache = true;
        return true;
      }

      // Check physical screen size
      const screenInches = await getScreenSizeInInches();
      if (screenInches >= 7) {
        isTabletCache = true;
        return true;
      }
    }

    // If none of the above matched, it's a phone
    isTabletCache = false;
    return false;
  } catch (error) {
    console.error('Device detection error:', error);
    // Fallback to screen size check
    const { width } = Dimensions.get('window');
    return width >= 600;
  }
};

// Helper function to calculate screen size in inches
const getScreenSizeInInches = async () => {
  try {
    const { width, height } = Dimensions.get('window');
    const screenScale = Dimensions.get('window').scale;

    // Convert pixels to inches (160 dpi baseline)
    const adjustedWidth = width * screenScale;
    const adjustedHeight = height * screenScale;

    return Math.sqrt(
      Math.pow(adjustedWidth / 160, 2) +
      Math.pow(adjustedHeight / 160, 2)
    );
  } catch {
    return 0;
  }
};

export const isTabletWrapper = () => {
  return isTabletOriginal || global.isTabletCache;
};

/////////////////////////////////////////////

// Filter function to hide rejected/cancelled orders older than 1 day
export const filterOldRejectedCancelledOrders = (orders) => {
  if (!orders || !Array.isArray(orders)) {
    return orders;
  }

  const now = moment();
  const sixAM = moment().startOf('day').add(6, 'hours');

  // Before 6:00 AM → return all orders (no filtering)
  if (now.isBefore(sixAM)) {
    return orders;
  }

  const oneDayAgo = moment().subtract(1, 'day').valueOf();

  return orders.filter(order => {
    // Check if order is rejected or cancelled
    const isRejectedOrCancelled =
      order.orderStatus === USER_ORDER_STATUS.ORDER_REJECTED_BY_MERCHANT ||
      order.orderStatus === USER_ORDER_STATUS.ORDER_CANCELLED_BY_MERCHANT ||
      order.orderStatus === USER_ORDER_STATUS.ORDER_CANCELLED_BY_USER;

    // If order is rejected/cancelled, check if it's older than 1 day
    if (isRejectedOrCancelled) {
      // Use updatedAt if available (when status was changed), otherwise use createdAt
      const orderTime = order.updatedAt || order.createdAt;
      return orderTime > oneDayAgo;
    }

    // Keep all other orders (not rejected/cancelled)
    return true;
  });
};

/////////////////////////////////////////////
